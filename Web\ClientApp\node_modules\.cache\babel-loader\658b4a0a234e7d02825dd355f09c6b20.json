{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n  return target;\n}\nimport { EnterLeaveCounter } from './EnterLeaveCounter.js';\nimport { createNativeDragSource, matchNativeItemType } from './NativeDragSources/index.js';\nimport * as NativeTypes from './NativeTypes.js';\nimport { getDragPreviewOffset, getEventClientOffset, getNodeClientOffset } from './OffsetUtils.js';\nimport { OptionsReader } from './OptionsReader.js';\nexport class HTML5BackendImpl {\n  /**\n  * Generate profiling statistics for the HTML5Backend.\n  */\n  profile() {\n    var ref, ref1;\n    return {\n      sourcePreviewNodes: this.sourcePreviewNodes.size,\n      sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n      sourceNodeOptions: this.sourceNodeOptions.size,\n      sourceNodes: this.sourceNodes.size,\n      dragStartSourceIds: ((ref = this.dragStartSourceIds) === null || ref === void 0 ? void 0 : ref.length) || 0,\n      dropTargetIds: this.dropTargetIds.length,\n      dragEnterTargetIds: this.dragEnterTargetIds.length,\n      dragOverTargetIds: ((ref1 = this.dragOverTargetIds) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n    };\n  }\n  // public for test\n  get window() {\n    return this.options.window;\n  }\n  get document() {\n    return this.options.document;\n  }\n  /**\n  * Get the root element to use for event subscriptions\n  */\n  get rootElement() {\n    return this.options.rootElement;\n  }\n  setup() {\n    const root = this.rootElement;\n    if (root === undefined) {\n      return;\n    }\n    if (root.__isReactDndBackendSetUp) {\n      throw new Error('Cannot have two HTML5 backends at the same time.');\n    }\n    root.__isReactDndBackendSetUp = true;\n    this.addEventListeners(root);\n  }\n  teardown() {\n    const root = this.rootElement;\n    if (root === undefined) {\n      return;\n    }\n    root.__isReactDndBackendSetUp = false;\n    this.removeEventListeners(this.rootElement);\n    this.clearCurrentDragSourceNode();\n    if (this.asyncEndDragFrameId) {\n      var ref;\n      (ref = this.window) === null || ref === void 0 ? void 0 : ref.cancelAnimationFrame(this.asyncEndDragFrameId);\n    }\n  }\n  connectDragPreview(sourceId, node, options) {\n    this.sourcePreviewNodeOptions.set(sourceId, options);\n    this.sourcePreviewNodes.set(sourceId, node);\n    return () => {\n      this.sourcePreviewNodes.delete(sourceId);\n      this.sourcePreviewNodeOptions.delete(sourceId);\n    };\n  }\n  connectDragSource(sourceId, node, options) {\n    this.sourceNodes.set(sourceId, node);\n    this.sourceNodeOptions.set(sourceId, options);\n    const handleDragStart = e => this.handleDragStart(e, sourceId);\n    const handleSelectStart = e => this.handleSelectStart(e);\n    node.setAttribute('draggable', 'true');\n    node.addEventListener('dragstart', handleDragStart);\n    node.addEventListener('selectstart', handleSelectStart);\n    return () => {\n      this.sourceNodes.delete(sourceId);\n      this.sourceNodeOptions.delete(sourceId);\n      node.removeEventListener('dragstart', handleDragStart);\n      node.removeEventListener('selectstart', handleSelectStart);\n      node.setAttribute('draggable', 'false');\n    };\n  }\n  connectDropTarget(targetId, node) {\n    const handleDragEnter = e => this.handleDragEnter(e, targetId);\n    const handleDragOver = e => this.handleDragOver(e, targetId);\n    const handleDrop = e => this.handleDrop(e, targetId);\n    node.addEventListener('dragenter', handleDragEnter);\n    node.addEventListener('dragover', handleDragOver);\n    node.addEventListener('drop', handleDrop);\n    return () => {\n      node.removeEventListener('dragenter', handleDragEnter);\n      node.removeEventListener('dragover', handleDragOver);\n      node.removeEventListener('drop', handleDrop);\n    };\n  }\n  addEventListeners(target) {\n    // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n    if (!target.addEventListener) {\n      return;\n    }\n    target.addEventListener('dragstart', this.handleTopDragStart);\n    target.addEventListener('dragstart', this.handleTopDragStartCapture, true);\n    target.addEventListener('dragend', this.handleTopDragEndCapture, true);\n    target.addEventListener('dragenter', this.handleTopDragEnter);\n    target.addEventListener('dragenter', this.handleTopDragEnterCapture, true);\n    target.addEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n    target.addEventListener('dragover', this.handleTopDragOver);\n    target.addEventListener('dragover', this.handleTopDragOverCapture, true);\n    target.addEventListener('drop', this.handleTopDrop);\n    target.addEventListener('drop', this.handleTopDropCapture, true);\n  }\n  removeEventListeners(target) {\n    // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n    if (!target.removeEventListener) {\n      return;\n    }\n    target.removeEventListener('dragstart', this.handleTopDragStart);\n    target.removeEventListener('dragstart', this.handleTopDragStartCapture, true);\n    target.removeEventListener('dragend', this.handleTopDragEndCapture, true);\n    target.removeEventListener('dragenter', this.handleTopDragEnter);\n    target.removeEventListener('dragenter', this.handleTopDragEnterCapture, true);\n    target.removeEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n    target.removeEventListener('dragover', this.handleTopDragOver);\n    target.removeEventListener('dragover', this.handleTopDragOverCapture, true);\n    target.removeEventListener('drop', this.handleTopDrop);\n    target.removeEventListener('drop', this.handleTopDropCapture, true);\n  }\n  getCurrentSourceNodeOptions() {\n    const sourceId = this.monitor.getSourceId();\n    const sourceNodeOptions = this.sourceNodeOptions.get(sourceId);\n    return _objectSpread({\n      dropEffect: this.altKeyPressed ? 'copy' : 'move'\n    }, sourceNodeOptions || {});\n  }\n  getCurrentDropEffect() {\n    if (this.isDraggingNativeItem()) {\n      // It makes more sense to default to 'copy' for native resources\n      return 'copy';\n    }\n    return this.getCurrentSourceNodeOptions().dropEffect;\n  }\n  getCurrentSourcePreviewNodeOptions() {\n    const sourceId = this.monitor.getSourceId();\n    const sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId);\n    return _objectSpread({\n      anchorX: 0.5,\n      anchorY: 0.5,\n      captureDraggingState: false\n    }, sourcePreviewNodeOptions || {});\n  }\n  isDraggingNativeItem() {\n    const itemType = this.monitor.getItemType();\n    return Object.keys(NativeTypes).some(key => NativeTypes[key] === itemType);\n  }\n  beginDragNativeItem(type, dataTransfer) {\n    this.clearCurrentDragSourceNode();\n    this.currentNativeSource = createNativeDragSource(type, dataTransfer);\n    this.currentNativeHandle = this.registry.addSource(type, this.currentNativeSource);\n    this.actions.beginDrag([this.currentNativeHandle]);\n  }\n  setCurrentDragSourceNode(node) {\n    this.clearCurrentDragSourceNode();\n    this.currentDragSourceNode = node;\n    // A timeout of > 0 is necessary to resolve Firefox issue referenced\n    // See:\n    //   * https://github.com/react-dnd/react-dnd/pull/928\n    //   * https://github.com/react-dnd/react-dnd/issues/869\n    const MOUSE_MOVE_TIMEOUT = 1000;\n    // Receiving a mouse event in the middle of a dragging operation\n    // means it has ended and the drag source node disappeared from DOM,\n    // so the browser didn't dispatch the dragend event.\n    //\n    // We need to wait before we start listening for mousemove events.\n    // This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n    // immediately in some browsers.\n    //\n    // See:\n    //   * https://github.com/react-dnd/react-dnd/pull/928\n    //   * https://github.com/react-dnd/react-dnd/issues/869\n    //\n    this.mouseMoveTimeoutTimer = setTimeout(() => {\n      var ref;\n      return (ref = this.rootElement) === null || ref === void 0 ? void 0 : ref.addEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n    }, MOUSE_MOVE_TIMEOUT);\n  }\n  clearCurrentDragSourceNode() {\n    if (this.currentDragSourceNode) {\n      this.currentDragSourceNode = null;\n      if (this.rootElement) {\n        var ref;\n        (ref = this.window) === null || ref === void 0 ? void 0 : ref.clearTimeout(this.mouseMoveTimeoutTimer || undefined);\n        this.rootElement.removeEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n      }\n      this.mouseMoveTimeoutTimer = null;\n      return true;\n    }\n    return false;\n  }\n  handleDragStart(e, sourceId) {\n    if (e.defaultPrevented) {\n      return;\n    }\n    if (!this.dragStartSourceIds) {\n      this.dragStartSourceIds = [];\n    }\n    this.dragStartSourceIds.unshift(sourceId);\n  }\n  handleDragEnter(_e, targetId) {\n    this.dragEnterTargetIds.unshift(targetId);\n  }\n  handleDragOver(_e, targetId) {\n    if (this.dragOverTargetIds === null) {\n      this.dragOverTargetIds = [];\n    }\n    this.dragOverTargetIds.unshift(targetId);\n  }\n  handleDrop(_e, targetId) {\n    this.dropTargetIds.unshift(targetId);\n  }\n  constructor(manager, globalContext, options) {\n    this.sourcePreviewNodes = new Map();\n    this.sourcePreviewNodeOptions = new Map();\n    this.sourceNodes = new Map();\n    this.sourceNodeOptions = new Map();\n    this.dragStartSourceIds = null;\n    this.dropTargetIds = [];\n    this.dragEnterTargetIds = [];\n    this.currentNativeSource = null;\n    this.currentNativeHandle = null;\n    this.currentDragSourceNode = null;\n    this.altKeyPressed = false;\n    this.mouseMoveTimeoutTimer = null;\n    this.asyncEndDragFrameId = null;\n    this.dragOverTargetIds = null;\n    this.lastClientOffset = null;\n    this.hoverRafId = null;\n    this.getSourceClientOffset = sourceId => {\n      const source = this.sourceNodes.get(sourceId);\n      return source && getNodeClientOffset(source) || null;\n    };\n    this.endDragNativeItem = () => {\n      if (!this.isDraggingNativeItem()) {\n        return;\n      }\n      this.actions.endDrag();\n      if (this.currentNativeHandle) {\n        this.registry.removeSource(this.currentNativeHandle);\n      }\n      this.currentNativeHandle = null;\n      this.currentNativeSource = null;\n    };\n    this.isNodeInDocument = node => {\n      // Check the node either in the main document or in the current context\n      return Boolean(node && this.document && this.document.body && this.document.body.contains(node));\n    };\n    this.endDragIfSourceWasRemovedFromDOM = () => {\n      const node = this.currentDragSourceNode;\n      if (node == null || this.isNodeInDocument(node)) {\n        return;\n      }\n      if (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n        this.actions.endDrag();\n      }\n      this.cancelHover();\n    };\n    this.scheduleHover = dragOverTargetIds => {\n      if (this.hoverRafId === null && typeof requestAnimationFrame !== 'undefined') {\n        this.hoverRafId = requestAnimationFrame(() => {\n          if (this.monitor.isDragging()) {\n            this.actions.hover(dragOverTargetIds || [], {\n              clientOffset: this.lastClientOffset\n            });\n          }\n          this.hoverRafId = null;\n        });\n      }\n    };\n    this.cancelHover = () => {\n      if (this.hoverRafId !== null && typeof cancelAnimationFrame !== 'undefined') {\n        cancelAnimationFrame(this.hoverRafId);\n        this.hoverRafId = null;\n      }\n    };\n    this.handleTopDragStartCapture = () => {\n      this.clearCurrentDragSourceNode();\n      this.dragStartSourceIds = [];\n    };\n    this.handleTopDragStart = e => {\n      if (e.defaultPrevented) {\n        return;\n      }\n      const {\n        dragStartSourceIds\n      } = this;\n      this.dragStartSourceIds = null;\n      const clientOffset = getEventClientOffset(e);\n      // Avoid crashing if we missed a drop event or our previous drag died\n      if (this.monitor.isDragging()) {\n        this.actions.endDrag();\n        this.cancelHover();\n      }\n      // Don't publish the source just yet (see why below)\n      this.actions.beginDrag(dragStartSourceIds || [], {\n        publishSource: false,\n        getSourceClientOffset: this.getSourceClientOffset,\n        clientOffset\n      });\n      const {\n        dataTransfer\n      } = e;\n      const nativeType = matchNativeItemType(dataTransfer);\n      if (this.monitor.isDragging()) {\n        if (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n          // Use custom drag image if user specifies it.\n          // If child drag source refuses drag but parent agrees,\n          // use parent's node as drag image. Neither works in IE though.\n          const sourceId = this.monitor.getSourceId();\n          const sourceNode = this.sourceNodes.get(sourceId);\n          const dragPreview = this.sourcePreviewNodes.get(sourceId) || sourceNode;\n          if (dragPreview) {\n            const {\n              anchorX,\n              anchorY,\n              offsetX,\n              offsetY\n            } = this.getCurrentSourcePreviewNodeOptions();\n            const anchorPoint = {\n              anchorX,\n              anchorY\n            };\n            const offsetPoint = {\n              offsetX,\n              offsetY\n            };\n            const dragPreviewOffset = getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint);\n            dataTransfer.setDragImage(dragPreview, dragPreviewOffset.x, dragPreviewOffset.y);\n          }\n        }\n        try {\n          // Firefox won't drag without setting data\n          dataTransfer === null || dataTransfer === void 0 ? void 0 : dataTransfer.setData('application/json', {});\n        } catch (err) {\n          // IE doesn't support MIME types in setData\n        }\n        // Store drag source node so we can check whether\n        // it is removed from DOM and trigger endDrag manually.\n        this.setCurrentDragSourceNode(e.target);\n        // Now we are ready to publish the drag source.. or are we not?\n        const {\n          captureDraggingState\n        } = this.getCurrentSourcePreviewNodeOptions();\n        if (!captureDraggingState) {\n          // Usually we want to publish it in the next tick so that browser\n          // is able to screenshot the current (not yet dragging) state.\n          //\n          // It also neatly avoids a situation where render() returns null\n          // in the same tick for the source element, and browser freaks out.\n          setTimeout(() => this.actions.publishDragSource(), 0);\n        } else {\n          // In some cases the user may want to override this behavior, e.g.\n          // to work around IE not supporting custom drag previews.\n          //\n          // When using a custom drag layer, the only way to prevent\n          // the default drag preview from drawing in IE is to screenshot\n          // the dragging state in which the node itself has zero opacity\n          // and height. In this case, though, returning null from render()\n          // will abruptly end the dragging, which is not obvious.\n          //\n          // This is the reason such behavior is strictly opt-in.\n          this.actions.publishDragSource();\n        }\n      } else if (nativeType) {\n        // A native item (such as URL) dragged from inside the document\n        this.beginDragNativeItem(nativeType);\n      } else if (dataTransfer && !dataTransfer.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute('draggable'))) {\n        // Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n        // Just let it drag. It's a native type (URL or text) and will be picked up in\n        // dragenter handler.\n        return;\n      } else {\n        // If by this time no drag source reacted, tell browser not to drag.\n        e.preventDefault();\n      }\n    };\n    this.handleTopDragEndCapture = () => {\n      if (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n        // Firefox can dispatch this event in an infinite loop\n        // if dragend handler does something like showing an alert.\n        // Only proceed if we have not handled it already.\n        this.actions.endDrag();\n      }\n      this.cancelHover();\n    };\n    this.handleTopDragEnterCapture = e => {\n      this.dragEnterTargetIds = [];\n      if (this.isDraggingNativeItem()) {\n        var ref;\n        (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n      }\n      const isFirstEnter = this.enterLeaveCounter.enter(e.target);\n      if (!isFirstEnter || this.monitor.isDragging()) {\n        return;\n      }\n      const {\n        dataTransfer\n      } = e;\n      const nativeType = matchNativeItemType(dataTransfer);\n      if (nativeType) {\n        // A native item (such as file or URL) dragged from outside the document\n        this.beginDragNativeItem(nativeType, dataTransfer);\n      }\n    };\n    this.handleTopDragEnter = e => {\n      const {\n        dragEnterTargetIds\n      } = this;\n      this.dragEnterTargetIds = [];\n      if (!this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        return;\n      }\n      this.altKeyPressed = e.altKey;\n      // If the target changes position as the result of `dragenter`, `dragover` might still\n      // get dispatched despite target being no longer there. The easy solution is to check\n      // whether there actually is a target before firing `hover`.\n      if (dragEnterTargetIds.length > 0) {\n        this.actions.hover(dragEnterTargetIds, {\n          clientOffset: getEventClientOffset(e)\n        });\n      }\n      const canDrop = dragEnterTargetIds.some(targetId => this.monitor.canDropOnTarget(targetId));\n      if (canDrop) {\n        // IE requires this to fire dragover events\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = this.getCurrentDropEffect();\n        }\n      }\n    };\n    this.handleTopDragOverCapture = e => {\n      this.dragOverTargetIds = [];\n      if (this.isDraggingNativeItem()) {\n        var ref;\n        (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n      }\n    };\n    this.handleTopDragOver = e => {\n      const {\n        dragOverTargetIds\n      } = this;\n      this.dragOverTargetIds = [];\n      if (!this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        // Prevent default \"drop and blow away the whole document\" action.\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n        return;\n      }\n      this.altKeyPressed = e.altKey;\n      this.lastClientOffset = getEventClientOffset(e);\n      this.scheduleHover(dragOverTargetIds);\n      const canDrop = (dragOverTargetIds || []).some(targetId => this.monitor.canDropOnTarget(targetId));\n      if (canDrop) {\n        // Show user-specified drop effect.\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = this.getCurrentDropEffect();\n        }\n      } else if (this.isDraggingNativeItem()) {\n        // Don't show a nice cursor but still prevent default\n        // \"drop and blow away the whole document\" action.\n        e.preventDefault();\n      } else {\n        e.preventDefault();\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n      }\n    };\n    this.handleTopDragLeaveCapture = e => {\n      if (this.isDraggingNativeItem()) {\n        e.preventDefault();\n      }\n      const isLastLeave = this.enterLeaveCounter.leave(e.target);\n      if (!isLastLeave) {\n        return;\n      }\n      if (this.isDraggingNativeItem()) {\n        setTimeout(() => this.endDragNativeItem(), 0);\n      }\n      this.cancelHover();\n    };\n    this.handleTopDropCapture = e => {\n      this.dropTargetIds = [];\n      if (this.isDraggingNativeItem()) {\n        var ref;\n        e.preventDefault();\n        (ref = this.currentNativeSource) === null || ref === void 0 ? void 0 : ref.loadDataTransfer(e.dataTransfer);\n      } else if (matchNativeItemType(e.dataTransfer)) {\n        // Dragging some elements, like <a> and <img> may still behave like a native drag event,\n        // even if the current drag event matches a user-defined type.\n        // Stop the default behavior when we're not expecting a native item to be dropped.\n        e.preventDefault();\n      }\n      this.enterLeaveCounter.reset();\n    };\n    this.handleTopDrop = e => {\n      const {\n        dropTargetIds\n      } = this;\n      this.dropTargetIds = [];\n      this.actions.hover(dropTargetIds, {\n        clientOffset: getEventClientOffset(e)\n      });\n      this.actions.drop({\n        dropEffect: this.getCurrentDropEffect()\n      });\n      if (this.isDraggingNativeItem()) {\n        this.endDragNativeItem();\n      } else if (this.monitor.isDragging()) {\n        this.actions.endDrag();\n      }\n      this.cancelHover();\n    };\n    this.handleSelectStart = e => {\n      const target = e.target;\n      // Only IE requires us to explicitly say\n      // we want drag drop operation to start\n      if (typeof target.dragDrop !== 'function') {\n        return;\n      }\n      // Inputs and textareas should be selectable\n      if (target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n        return;\n      }\n      // For other targets, ask IE\n      // to enable drag and drop\n      e.preventDefault();\n      target.dragDrop();\n    };\n    this.options = new OptionsReader(globalContext, options);\n    this.actions = manager.getActions();\n    this.monitor = manager.getMonitor();\n    this.registry = manager.getRegistry();\n    this.enterLeaveCounter = new EnterLeaveCounter(this.isNodeInDocument);\n  }\n}", "map": {"version": 3, "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D,SACCC,sBAAsB,EACtBC,mBAAmB,QACb,8BAA8B;AAErC,YAAYC,WAAW,MAAM,kBAAkB;AAC/C,SACCC,oBAAoB,EACpBC,oBAAoB,EACpBC,mBAAmB,QACb,kBAAkB;AACzB,SAASC,aAAa,QAAQ,oBAAoB;AAKlD,OAAO,MAAMC,gBAAgB;EA0C5B;;;EAGAC,OAAc,GAA2B;QAMnBC,GAAuB,EAGxBC,IAAsB;IAR1C,OAAO;MACNC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACC,IAAI;MAChDC,wBAAwB,EAAE,IAAI,CAACA,wBAAwB,CAACD,IAAI;MAC5DE,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACF,IAAI;MAC9CG,WAAW,EAAE,IAAI,CAACA,WAAW,CAACH,IAAI;MAClCI,kBAAkB,EAAE,KAAuB,GAAvB,IAAI,CAACA,kBAAkB,cAAvBP,GAAuB,WAAQ,GAA/B,MAA+B,GAA/BA,GAAuB,CAAEQ,MAAM,KAAI,CAAC;MACxDC,aAAa,EAAE,IAAI,CAACA,aAAa,CAACD,MAAM;MACxCE,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACF,MAAM;MAClDG,iBAAiB,EAAE,MAAsB,GAAtB,IAAI,CAACA,iBAAiB,cAAtBV,IAAsB,WAAQ,GAA9B,MAA8B,GAA9BA,IAAsB,CAAEO,MAAM,KAAI;KACrD;;EAGF;EACA,IAAWI,MAAM,GAAuB;IACvC,OAAO,IAAI,CAACC,OAAO,CAACD,MAAM;;EAE3B,IAAWE,QAAQ,GAAyB;IAC3C,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ;;EAE7B;;;EAGA,IAAYC,WAAW,GAAqB;IAC3C,OAAO,IAAI,CAACF,OAAO,CAACE,WAAW;;EAGhCC,KAAY,GAAS;IACpB,MAAMC,IAAI,GAAG,IAAI,CAACF,WAAW;IAC7B,IAAIE,IAAI,KAAKC,SAAS,EAAE;MACvB;;IAGD,IAAID,IAAI,CAACE,wBAAwB,EAAE;MAClC,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;;IAEpEH,IAAI,CAACE,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACE,iBAAiB,CAACJ,IAAI,CAAC;;EAG7BK,QAAe,GAAS;IACvB,MAAML,IAAI,GAAG,IAAI,CAACF,WAAW;IAC7B,IAAIE,IAAI,KAAKC,SAAS,EAAE;MACvB;;IAGDD,IAAI,CAACE,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAACR,WAAW,CAAY;IACtD,IAAI,CAACS,0BAA0B,EAAE;IACjC,IAAI,IAAI,CAACC,mBAAmB,EAAE;UAC7BzB,GAAW;MAAX,IAAW,GAAX,IAAI,CAACY,MAAM,cAAXZ,GAAW,WAAsB,GAAjC,MAAiC,GAAjCA,GAAW,CAAE0B,oBAAoB,CAAC,IAAI,CAACD,mBAAmB,CAAC;;;EAI7DE,kBAAyB,CACxBC,QAAgB,EAChBC,IAAa,EACbhB,OAAY,EACE;IACd,IAAI,CAACT,wBAAwB,CAAC0B,GAAG,CAACF,QAAQ,EAAEf,OAAO,CAAC;IACpD,IAAI,CAACX,kBAAkB,CAAC4B,GAAG,CAACF,QAAQ,EAAEC,IAAI,CAAC;IAE3C,OAAO,MAAY;MAClB,IAAI,CAAC3B,kBAAkB,CAAC6B,MAAM,CAACH,QAAQ,CAAC;MACxC,IAAI,CAACxB,wBAAwB,CAAC2B,MAAM,CAACH,QAAQ,CAAC;KAC9C;;EAGFI,iBAAwB,CACvBJ,QAAgB,EAChBC,IAAa,EACbhB,OAAY,EACE;IACd,IAAI,CAACP,WAAW,CAACwB,GAAG,CAACF,QAAQ,EAAEC,IAAI,CAAC;IACpC,IAAI,CAACxB,iBAAiB,CAACyB,GAAG,CAACF,QAAQ,EAAEf,OAAO,CAAC;IAE7C,MAAMoB,eAAe,GAAIC,CAAM,IAAK,IAAI,CAACD,eAAe,CAACC,CAAC,EAAEN,QAAQ,CAAC;IACrE,MAAMO,iBAAiB,GAAID,CAAM,IAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAAC;IAE/DL,IAAI,CAACO,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;IACtCP,IAAI,CAACQ,gBAAgB,CAAC,WAAW,EAAEJ,eAAe,CAAC;IACnDJ,IAAI,CAACQ,gBAAgB,CAAC,aAAa,EAAEF,iBAAiB,CAAC;IAEvD,OAAO,MAAY;MAClB,IAAI,CAAC7B,WAAW,CAACyB,MAAM,CAACH,QAAQ,CAAC;MACjC,IAAI,CAACvB,iBAAiB,CAAC0B,MAAM,CAACH,QAAQ,CAAC;MAEvCC,IAAI,CAACS,mBAAmB,CAAC,WAAW,EAAEL,eAAe,CAAC;MACtDJ,IAAI,CAACS,mBAAmB,CAAC,aAAa,EAAEH,iBAAiB,CAAC;MAC1DN,IAAI,CAACO,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC;KACvC;;EAGFG,iBAAwB,CAACC,QAAgB,EAAEX,IAAiB,EAAe;IAC1E,MAAMY,eAAe,GAAIP,CAAY,IAAK,IAAI,CAACO,eAAe,CAACP,CAAC,EAAEM,QAAQ,CAAC;IAC3E,MAAME,cAAc,GAAIR,CAAY,IAAK,IAAI,CAACQ,cAAc,CAACR,CAAC,EAAEM,QAAQ,CAAC;IACzE,MAAMG,UAAU,GAAIT,CAAY,IAAK,IAAI,CAACS,UAAU,CAACT,CAAC,EAAEM,QAAQ,CAAC;IAEjEX,IAAI,CAACQ,gBAAgB,CAAC,WAAW,EAAEI,eAAe,CAAC;IACnDZ,IAAI,CAACQ,gBAAgB,CAAC,UAAU,EAAEK,cAAc,CAAC;IACjDb,IAAI,CAACQ,gBAAgB,CAAC,MAAM,EAAEM,UAAU,CAAC;IAEzC,OAAO,MAAY;MAClBd,IAAI,CAACS,mBAAmB,CAAC,WAAW,EAAEG,eAAe,CAAC;MACtDZ,IAAI,CAACS,mBAAmB,CAAC,UAAU,EAAEI,cAAc,CAAC;MACpDb,IAAI,CAACS,mBAAmB,CAAC,MAAM,EAAEK,UAAU,CAAC;KAC5C;;EAGFtB,iBAAyB,CAACuB,MAAY,EAAE;IACvC;IACA,IAAI,CAACA,MAAM,CAACP,gBAAgB,EAAE;MAC7B;;IAEDO,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACQ,kBAAkB,CACvB;IACDD,MAAM,CAACP,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACS,yBAAyB,EAAE,IAAI,CAAC;IAC1EF,MAAM,CAACP,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACU,uBAAuB,EAAE,IAAI,CAAC;IACtEH,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACW,kBAAkB,CACvB;IACDJ,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACY,yBAAyB,EAC9B,IAAI,CACJ;IACDL,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACa,yBAAyB,EAC9B,IAAI,CACJ;IACDN,MAAM,CAACP,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACc,iBAAiB,CAAkB;IAC5EP,MAAM,CAACP,gBAAgB,CACtB,UAAU,EACV,IAAI,CAACe,wBAAwB,EAC7B,IAAI,CACJ;IACDR,MAAM,CAACP,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACgB,aAAa,CAAkB;IACpET,MAAM,CAACP,gBAAgB,CACtB,MAAM,EACN,IAAI,CAACiB,oBAAoB,EACzB,IAAI,CACJ;;EAGF/B,oBAA4B,CAACqB,MAAY,EAAE;IAC1C;IACA,IAAI,CAACA,MAAM,CAACN,mBAAmB,EAAE;MAChC;;IAEDM,MAAM,CAACN,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACO,kBAAkB,CAAQ;IACvED,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACQ,yBAAyB,EAC9B,IAAI,CACJ;IACDF,MAAM,CAACN,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACS,uBAAuB,EAAE,IAAI,CAAC;IACzEH,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACU,kBAAkB,CACvB;IACDJ,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACW,yBAAyB,EAC9B,IAAI,CACJ;IACDL,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACY,yBAAyB,EAC9B,IAAI,CACJ;IACDN,MAAM,CAACN,mBAAmB,CACzB,UAAU,EACV,IAAI,CAACa,iBAAiB,CACtB;IACDP,MAAM,CAACN,mBAAmB,CACzB,UAAU,EACV,IAAI,CAACc,wBAAwB,EAC7B,IAAI,CACJ;IACDR,MAAM,CAACN,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACe,aAAa,CAAkB;IACvET,MAAM,CAACN,mBAAmB,CACzB,MAAM,EACN,IAAI,CAACgB,oBAAoB,EACzB,IAAI,CACJ;;EAGFC,2BAAmC,GAAG;IACrC,MAAM3B,QAAQ,GAAG,IAAI,CAAC4B,OAAO,CAACC,WAAW,EAAE;IAC3C,MAAMpD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACqD,GAAG,CAAC9B,QAAQ,CAAC;IAE9D,OAAO+B;MACNC,UAAU,EAAE,IAAI,CAACC,aAAa,GAAG,MAAM,GAAG;OACtCxD,iBAAiB,IAAI,EAAE,CAC3B;;EAGFyD,oBAA4B,GAAG;IAC9B,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;MAChC;MACA,OAAO,MAAM;;IAGd,OAAO,IAAI,CAACR,2BAA2B,EAAE,CAACK,UAAU;;EAGrDI,kCAA0C,GAAG;IAC5C,MAAMpC,QAAQ,GAAG,IAAI,CAAC4B,OAAO,CAACC,WAAW,EAAE;IAC3C,MAAMrD,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACsD,GAAG,CAAC9B,QAAQ,CAAC;IAE5E,OAAO+B;MACNM,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,oBAAoB,EAAE;OAClB/D,wBAAwB,IAAI,EAAE,CAClC;;EAQF2D,oBAA4B,GAAG;IAC9B,MAAMK,QAAQ,GAAG,IAAI,CAACZ,OAAO,CAACa,WAAW,EAAE;IAC3C,OAAOC,MAAM,CAACC,IAAI,CAAC9E,WAAW,CAAC,CAAC+E,IAAI,CAClCC,GAAW,IAAKhF,WAAY,CAASgF,GAAG,CAAC,KAAKL,QAAQ,CACvD;;EAGFM,mBAA2B,CAACC,IAAY,EAAEC,YAA2B,EAAE;IACtE,IAAI,CAACpD,0BAA0B,EAAE;IAEjC,IAAI,CAACqD,mBAAmB,GAAGtF,sBAAsB,CAACoF,IAAI,EAAEC,YAAY,CAAC;IACrE,IAAI,CAACE,mBAAmB,GAAG,IAAI,CAACC,QAAQ,CAACC,SAAS,CACjDL,IAAI,EACJ,IAAI,CAACE,mBAAmB,CACxB;IACD,IAAI,CAACI,OAAO,CAACC,SAAS,CAAC,CAAC,IAAI,CAACJ,mBAAmB,CAAC,CAAC;;EAsCnDK,wBAAgC,CAACtD,IAAoB,EAAE;IACtD,IAAI,CAACL,0BAA0B,EAAE;IACjC,IAAI,CAAC4D,qBAAqB,GAAGvD,IAAI;IAEjC;IACA;IACA;IACA;IACA,MAAMwD,kBAAkB,GAAG,IAAI;IAE/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,qBAAqB,GAAGC,UAAU,CAAC,MAAM;UACtCvF,GAAgB;MAAvB,OAAO,IAAgB,GAAhB,IAAI,CAACe,WAAW,cAAhBf,GAAgB,WAAkB,GAAlC,MAAkC,GAAlCA,GAAgB,CAAEqC,gBAAgB,CACxC,WAAW,EACX,IAAI,CAACmD,gCAAgC,EACrC,IAAI,CACJ;KACD,EAAEH,kBAAkB,CAAC;;EAGvB7D,0BAAkC,GAAG;IACpC,IAAI,IAAI,CAAC4D,qBAAqB,EAAE;MAC/B,IAAI,CAACA,qBAAqB,GAAG,IAAI;MAEjC,IAAI,IAAI,CAACrE,WAAW,EAAE;YACrBf,GAAW;QAAX,IAAW,GAAX,IAAI,CAACY,MAAM,cAAXZ,GAAW,WAAc,GAAzB,MAAyB,GAAzBA,GAAW,CAAEyF,YAAY,CAAC,IAAI,CAACH,qBAAqB,IAAIpE,SAAS,CAAC;QAClE,IAAI,CAACH,WAAW,CAACuB,mBAAmB,CACnC,WAAW,EACX,IAAI,CAACkD,gCAAgC,EACrC,IAAI,CACJ;;MAGF,IAAI,CAACF,qBAAqB,GAAG,IAAI;MACjC,OAAO,IAAI;;IAGZ,OAAO,KAAK;;EAmCbrD,eAAsB,CAACC,CAAY,EAAEN,QAAgB,EAAQ;IAC5D,IAAIM,CAAC,CAACwD,gBAAgB,EAAE;MACvB;;IAGD,IAAI,CAAC,IAAI,CAACnF,kBAAkB,EAAE;MAC7B,IAAI,CAACA,kBAAkB,GAAG,EAAE;;IAE7B,IAAI,CAACA,kBAAkB,CAACoF,OAAO,CAAC/D,QAAQ,CAAC;;EA8I1Ca,eAAsB,CAACmD,EAAa,EAAEpD,QAAgB,EAAQ;IAC7D,IAAI,CAAC9B,kBAAkB,CAACiF,OAAO,CAACnD,QAAQ,CAAC;;EA4C1CE,cAAqB,CAACkD,EAAa,EAAEpD,QAAgB,EAAQ;IAC5D,IAAI,IAAI,CAAC7B,iBAAiB,KAAK,IAAI,EAAE;MACpC,IAAI,CAACA,iBAAiB,GAAG,EAAE;;IAE5B,IAAI,CAACA,iBAAiB,CAACgF,OAAO,CAACnD,QAAQ,CAAC;;EA6EzCG,UAAiB,CAACiD,EAAa,EAAEpD,QAAgB,EAAQ;IACxD,IAAI,CAAC/B,aAAa,CAACkF,OAAO,CAACnD,QAAQ,CAAC;;EA/oBrCqD,YACCC,OAAwB,EACxBC,aAAmC,EACnClF,OAA6B,EAC5B;IAvBF,KAAQX,kBAAkB,GAAyB,IAAI8F,GAAG,EAAE;IAC5D,KAAQ5F,wBAAwB,GAAqB,IAAI4F,GAAG,EAAE;IAC9D,KAAQ1F,WAAW,GAAyB,IAAI0F,GAAG,EAAE;IACrD,KAAQ3F,iBAAiB,GAAqB,IAAI2F,GAAG,EAAE;IAEvD,KAAQzF,kBAAkB,GAAoB,IAAI;IAClD,KAAQE,aAAa,GAAa,EAAE;IACpC,KAAQC,kBAAkB,GAAa,EAAE;IACzC,KAAQmE,mBAAmB,GAA4B,IAAI;IAC3D,KAAQC,mBAAmB,GAAsB,IAAI;IACrD,KAAQM,qBAAqB,GAAmB,IAAI;IACpD,KAAQvB,aAAa,GAAG,KAAK;IAC7B,KAAQyB,qBAAqB,GAAkB,IAAI;IACnD,KAAQ7D,mBAAmB,GAAkB,IAAI;IACjD,KAAQd,iBAAiB,GAAoB,IAAI;IAEjD,KAAQsF,gBAAgB,GAAmB,IAAI;IAC/C,KAAQC,UAAU,GAAkB,IAAI;IA+OxC,KAAQC,qBAAqB,GAAIvE,QAAgB,IAAqB;MACrE,MAAMwE,MAAM,GAAG,IAAI,CAAC9F,WAAW,CAACoD,GAAG,CAAC9B,QAAQ,CAAC;MAC7C,OAAOwE,MAAO,IAAIxG,mBAAmB,CAACwG,MAAM,CAAgB,IAAK,IAAI;KACrE;IAoBD,KAAQC,iBAAiB,GAAG,MAAY;MACvC,IAAI,CAAC,IAAI,CAACtC,oBAAoB,EAAE,EAAE;QACjC;;MAGD,IAAI,CAACkB,OAAO,CAACqB,OAAO,EAAE;MACtB,IAAI,IAAI,CAACxB,mBAAmB,EAAE;QAC7B,IAAI,CAACC,QAAQ,CAACwB,YAAY,CAAC,IAAI,CAACzB,mBAAmB,CAAC;;MAErD,IAAI,CAACA,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACD,mBAAmB,GAAG,IAAI;KAC/B;IAED,KAAQ2B,gBAAgB,GAAI3E,IAA6B,IAAc;MACtE;MACA,OAAO4E,OAAO,CACb5E,IAAI,IACH,IAAI,CAACf,QAAQ,IACb,IAAI,CAACA,QAAQ,CAAC4F,IAAI,IAClB,IAAI,CAAC5F,QAAQ,CAAC4F,IAAI,CAACC,QAAQ,CAAC9E,IAAI,CAAC,CAClC;KACD;IAED,KAAQ2D,gCAAgC,GAAG,MAAY;MACtD,MAAM3D,IAAI,GAAG,IAAI,CAACuD,qBAAqB;MACvC,IAAIvD,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC2E,gBAAgB,CAAC3E,IAAI,CAAC,EAAE;QAChD;;MAGD,IAAI,IAAI,CAACL,0BAA0B,EAAE,IAAI,IAAI,CAACgC,OAAO,CAACoD,UAAU,EAAE,EAAE;QACnE,IAAI,CAAC3B,OAAO,CAACqB,OAAO,EAAE;;MAEvB,IAAI,CAACO,WAAW,EAAE;KAClB;IAqDD,KAAQC,aAAa,GAAInG,iBAAkC,IAAK;MAC/D,IACC,IAAI,CAACuF,UAAU,KAAK,IAAI,IACxB,OAAOa,qBAAqB,KAAK,WAAW,EAC3C;QACD,IAAI,CAACb,UAAU,GAAGa,qBAAqB,CAAC,MAAM;UAC7C,IAAI,IAAI,CAACvD,OAAO,CAACoD,UAAU,EAAE,EAAE;YAC9B,IAAI,CAAC3B,OAAO,CAAC+B,KAAK,CAACrG,iBAAiB,IAAI,EAAE,EAAE;cAC3CsG,YAAY,EAAE,IAAI,CAAChB;aACnB,CAAC;;UAGH,IAAI,CAACC,UAAU,GAAG,IAAI;SACtB,CAAC;;KAEH;IAED,KAAQW,WAAW,GAAG,MAAM;MAC3B,IACC,IAAI,CAACX,UAAU,KAAK,IAAI,IACxB,OAAOxE,oBAAoB,KAAK,WAAW,EAC1C;QACDA,oBAAoB,CAAC,IAAI,CAACwE,UAAU,CAAC;QACrC,IAAI,CAACA,UAAU,GAAG,IAAI;;KAEvB;IAED,KAAOpD,yBAAyB,GAAG,MAAY;MAC9C,IAAI,CAACtB,0BAA0B,EAAE;MACjC,IAAI,CAACjB,kBAAkB,GAAG,EAAE;KAC5B;IAaD,KAAOsC,kBAAkB,GAAIX,CAAY,IAAW;MACnD,IAAIA,CAAC,CAACwD,gBAAgB,EAAE;QACvB;;MAGD,MAAM;QAAEnF;MAAkB,CAAE,GAAG,IAAI;MACnC,IAAI,CAACA,kBAAkB,GAAG,IAAI;MAE9B,MAAM0G,YAAY,GAAGtH,oBAAoB,CAACuC,CAAC,CAAC;MAE5C;MACA,IAAI,IAAI,CAACsB,OAAO,CAACoD,UAAU,EAAE,EAAE;QAC9B,IAAI,CAAC3B,OAAO,CAACqB,OAAO,EAAE;QACtB,IAAI,CAACO,WAAW,EAAE;;MAGnB;MACA,IAAI,CAAC5B,OAAO,CAACC,SAAS,CAAC3E,kBAAkB,IAAI,EAAE,EAAE;QAChD2G,aAAa,EAAE,KAAK;QACpBf,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;QACjDc;OACA,CAAC;MAEF,MAAM;QAAErC;MAAY,CAAE,GAAG1C,CAAC;MAC1B,MAAMiF,UAAU,GAAG3H,mBAAmB,CAACoF,YAAY,CAAC;MAEpD,IAAI,IAAI,CAACpB,OAAO,CAACoD,UAAU,EAAE,EAAE;QAC9B,IAAIhC,YAAY,IAAI,OAAOA,YAAY,CAACwC,YAAY,KAAK,UAAU,EAAE;UACpE;UACA;UACA;UACA,MAAMxF,QAAQ,GAAW,IAAI,CAAC4B,OAAO,CAACC,WAAW,EAAE;UACnD,MAAM4D,UAAU,GAAG,IAAI,CAAC/G,WAAW,CAACoD,GAAG,CAAC9B,QAAQ,CAAC;UACjD,MAAM0F,WAAW,GAAG,IAAI,CAACpH,kBAAkB,CAACwD,GAAG,CAAC9B,QAAQ,CAAC,IAAIyF,UAAU;UAEvE,IAAIC,WAAW,EAAE;YAChB,MAAM;cAAErD,OAAO;cAAEC,OAAO;cAAEqD,OAAO;cAAEC;YAAO,CAAE,GAC3C,IAAI,CAACxD,kCAAkC,EAAE;YAC1C,MAAMyD,WAAW,GAAG;cAAExD,OAAO;cAAEC;aAAS;YACxC,MAAMwD,WAAW,GAAG;cAAEH,OAAO;cAAEC;aAAS;YACxC,MAAMG,iBAAiB,GAAGjI,oBAAoB,CAC7C2H,UAAU,EACVC,WAAW,EACXL,YAAY,EACZQ,WAAW,EACXC,WAAW,CACX;YAED9C,YAAY,CAACwC,YAAY,CACxBE,WAAW,EACXK,iBAAiB,CAACC,CAAC,EACnBD,iBAAiB,CAACE,CAAC,CACnB;;;QAIH,IAAI;UACH;UACAjD,YAAY,aAAZA,YAAY,WAAS,GAArBA,MAAqB,GAArBA,YAAY,CAAEkD,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAQ;SACpD,CAAC,OAAOC,GAAG,EAAE;UACb;QAAA;QAGD;QACA;QACA,IAAI,CAAC5C,wBAAwB,CAACjD,CAAC,CAACU,MAAM,CAAY;QAElD;QACA,MAAM;UAAEuB;QAAoB,CAAE,GAAG,IAAI,CAACH,kCAAkC,EAAE;QAC1E,IAAI,CAACG,oBAAoB,EAAE;UAC1B;UACA;UACA;UACA;UACA;UACAoB,UAAU,CAAC,MAAM,IAAI,CAACN,OAAO,CAAC+C,iBAAiB,EAAE,EAAE,CAAC,CAAC;SACrD,MAAM;UACN;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAAC/C,OAAO,CAAC+C,iBAAiB,EAAE;;OAEjC,MAAM,IAAIb,UAAU,EAAE;QACtB;QACA,IAAI,CAACzC,mBAAmB,CAACyC,UAAU,CAAC;OACpC,MAAM,IACNvC,YAAY,IACZ,CAACA,YAAY,CAACqD,KAAK,KAClB/F,CAAE,CAACU,MAAM,IAAI,CAACV,CAAE,CAACU,MAAM,CAAasF,YAAY,IAChD,CAAChG,CAAE,CAACU,MAAM,CAAasF,YAAY,CAAC,WAAW,CAAC,CAAC,EACjD;QACD;QACA;QACA;QACA;OACA,MAAM;QACN;QACAhG,CAAC,CAACiG,cAAc,EAAE;;KAEnB;IAED,KAAOpF,uBAAuB,GAAG,MAAY;MAC5C,IAAI,IAAI,CAACvB,0BAA0B,EAAE,IAAI,IAAI,CAACgC,OAAO,CAACoD,UAAU,EAAE,EAAE;QACnE;QACA;QACA;QACA,IAAI,CAAC3B,OAAO,CAACqB,OAAO,EAAE;;MAEvB,IAAI,CAACO,WAAW,EAAE;KAClB;IAED,KAAO5D,yBAAyB,GAAIf,CAAY,IAAW;MAC1D,IAAI,CAACxB,kBAAkB,GAAG,EAAE;MAE5B,IAAI,IAAI,CAACqD,oBAAoB,EAAE,EAAE;YAChC/D,GAAwB;QAAxB,IAAwB,GAAxB,IAAI,CAAC6E,mBAAmB,cAAxB7E,GAAwB,WAAkB,GAA1C,MAA0C,GAA1CA,GAAwB,CAAEoI,gBAAgB,CAAClG,CAAC,CAAC0C,YAAY,CAAC;;MAG3D,MAAMyD,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAACC,KAAK,CAACrG,CAAC,CAACU,MAAM,CAAC;MAC3D,IAAI,CAACyF,YAAY,IAAI,IAAI,CAAC7E,OAAO,CAACoD,UAAU,EAAE,EAAE;QAC/C;;MAGD,MAAM;QAAEhC;MAAY,CAAE,GAAG1C,CAAC;MAC1B,MAAMiF,UAAU,GAAG3H,mBAAmB,CAACoF,YAAY,CAAC;MAEpD,IAAIuC,UAAU,EAAE;QACf;QACA,IAAI,CAACzC,mBAAmB,CAACyC,UAAU,EAAEvC,YAAY,CAAiB;;KAEnE;IAMD,KAAO5B,kBAAkB,GAAId,CAAY,IAAW;MACnD,MAAM;QAAExB;MAAkB,CAAE,GAAG,IAAI;MACnC,IAAI,CAACA,kBAAkB,GAAG,EAAE;MAE5B,IAAI,CAAC,IAAI,CAAC8C,OAAO,CAACoD,UAAU,EAAE,EAAE;QAC/B;QACA;;MAGD,IAAI,CAAC/C,aAAa,GAAG3B,CAAC,CAACsG,MAAM;MAE7B;MACA;MACA;MACA,IAAI9H,kBAAkB,CAACF,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACyE,OAAO,CAAC+B,KAAK,CAACtG,kBAAkB,EAAE;UACtCuG,YAAY,EAAEtH,oBAAoB,CAACuC,CAAC;SACpC,CAAC;;MAGH,MAAMuG,OAAO,GAAG/H,kBAAkB,CAAC8D,IAAI,CAAEhC,QAAQ,IAChD,IAAI,CAACgB,OAAO,CAACkF,eAAe,CAAClG,QAAQ,CAAC,CACtC;MAED,IAAIiG,OAAO,EAAE;QACZ;QACAvG,CAAC,CAACiG,cAAc,EAAE;QAClB,IAAIjG,CAAC,CAAC0C,YAAY,EAAE;UACnB1C,CAAC,CAAC0C,YAAY,CAAChB,UAAU,GAAG,IAAI,CAACE,oBAAoB,EAAE;;;KAGzD;IAED,KAAOV,wBAAwB,GAAIlB,CAAY,IAAW;MACzD,IAAI,CAACvB,iBAAiB,GAAG,EAAE;MAE3B,IAAI,IAAI,CAACoD,oBAAoB,EAAE,EAAE;YAChC/D,GAAwB;QAAxB,IAAwB,GAAxB,IAAI,CAAC6E,mBAAmB,cAAxB7E,GAAwB,WAAkB,GAA1C,MAA0C,GAA1CA,GAAwB,CAAEoI,gBAAgB,CAAClG,CAAC,CAAC0C,YAAY,CAAC;;KAE3D;IASD,KAAOzB,iBAAiB,GAAIjB,CAAY,IAAW;MAClD,MAAM;QAAEvB;MAAiB,CAAE,GAAG,IAAI;MAClC,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAE3B,IAAI,CAAC,IAAI,CAAC6C,OAAO,CAACoD,UAAU,EAAE,EAAE;QAC/B;QACA;QACA1E,CAAC,CAACiG,cAAc,EAAE;QAClB,IAAIjG,CAAC,CAAC0C,YAAY,EAAE;UACnB1C,CAAC,CAAC0C,YAAY,CAAChB,UAAU,GAAG,MAAM;;QAEnC;;MAGD,IAAI,CAACC,aAAa,GAAG3B,CAAC,CAACsG,MAAM;MAC7B,IAAI,CAACvC,gBAAgB,GAAGtG,oBAAoB,CAACuC,CAAC,CAAC;MAE/C,IAAI,CAAC4E,aAAa,CAACnG,iBAAiB,CAAC;MAErC,MAAM8H,OAAO,GAAG,CAAC9H,iBAAiB,IAAI,EAAE,EAAE6D,IAAI,CAAEhC,QAAQ,IACvD,IAAI,CAACgB,OAAO,CAACkF,eAAe,CAAClG,QAAQ,CAAC,CACtC;MAED,IAAIiG,OAAO,EAAE;QACZ;QACAvG,CAAC,CAACiG,cAAc,EAAE;QAClB,IAAIjG,CAAC,CAAC0C,YAAY,EAAE;UACnB1C,CAAC,CAAC0C,YAAY,CAAChB,UAAU,GAAG,IAAI,CAACE,oBAAoB,EAAE;;OAExD,MAAM,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;QACvC;QACA;QACA7B,CAAC,CAACiG,cAAc,EAAE;OAClB,MAAM;QACNjG,CAAC,CAACiG,cAAc,EAAE;QAClB,IAAIjG,CAAC,CAAC0C,YAAY,EAAE;UACnB1C,CAAC,CAAC0C,YAAY,CAAChB,UAAU,GAAG,MAAM;;;KAGpC;IAED,KAAOV,yBAAyB,GAAIhB,CAAY,IAAW;MAC1D,IAAI,IAAI,CAAC6B,oBAAoB,EAAE,EAAE;QAChC7B,CAAC,CAACiG,cAAc,EAAE;;MAGnB,MAAMQ,WAAW,GAAG,IAAI,CAACL,iBAAiB,CAACM,KAAK,CAAC1G,CAAC,CAACU,MAAM,CAAC;MAC1D,IAAI,CAAC+F,WAAW,EAAE;QACjB;;MAGD,IAAI,IAAI,CAAC5E,oBAAoB,EAAE,EAAE;QAChCwB,UAAU,CAAC,MAAM,IAAI,CAACc,iBAAiB,EAAE,EAAE,CAAC,CAAC;;MAE9C,IAAI,CAACQ,WAAW,EAAE;KAClB;IAED,KAAOvD,oBAAoB,GAAIpB,CAAY,IAAW;MACrD,IAAI,CAACzB,aAAa,GAAG,EAAE;MAEvB,IAAI,IAAI,CAACsD,oBAAoB,EAAE,EAAE;YAEhC/D,GAAwB;QADxBkC,CAAC,CAACiG,cAAc,EAAE;QAClB,IAAwB,GAAxB,IAAI,CAACtD,mBAAmB,cAAxB7E,GAAwB,WAAkB,GAA1C,MAA0C,GAA1CA,GAAwB,CAAEoI,gBAAgB,CAAClG,CAAC,CAAC0C,YAAY,CAAC;OAC1D,MAAM,IAAIpF,mBAAmB,CAAC0C,CAAC,CAAC0C,YAAY,CAAC,EAAE;QAC/C;QACA;QACA;QAEA1C,CAAC,CAACiG,cAAc,EAAE;;MAGnB,IAAI,CAACG,iBAAiB,CAACO,KAAK,EAAE;KAC9B;IAMD,KAAOxF,aAAa,GAAInB,CAAY,IAAW;MAC9C,MAAM;QAAEzB;MAAa,CAAE,GAAG,IAAI;MAC9B,IAAI,CAACA,aAAa,GAAG,EAAE;MAEvB,IAAI,CAACwE,OAAO,CAAC+B,KAAK,CAACvG,aAAa,EAAE;QACjCwG,YAAY,EAAEtH,oBAAoB,CAACuC,CAAC;OACpC,CAAC;MACF,IAAI,CAAC+C,OAAO,CAAC6D,IAAI,CAAC;QAAElF,UAAU,EAAE,IAAI,CAACE,oBAAoB;OAAI,CAAC;MAE9D,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;QAChC,IAAI,CAACsC,iBAAiB,EAAE;OACxB,MAAM,IAAI,IAAI,CAAC7C,OAAO,CAACoD,UAAU,EAAE,EAAE;QACrC,IAAI,CAAC3B,OAAO,CAACqB,OAAO,EAAE;;MAEvB,IAAI,CAACO,WAAW,EAAE;KAClB;IAED,KAAO1E,iBAAiB,GAAID,CAAY,IAAW;MAClD,MAAMU,MAAM,GAAGV,CAAC,CAACU,MAAM;MAEvB;MACA;MACA,IAAI,OAAOA,MAAM,CAACmG,QAAQ,KAAK,UAAU,EAAE;QAC1C;;MAGD;MACA,IACCnG,MAAM,CAACoG,OAAO,KAAK,OAAO,IAC1BpG,MAAM,CAACoG,OAAO,KAAK,QAAQ,IAC3BpG,MAAM,CAACoG,OAAO,KAAK,UAAU,IAC7BpG,MAAM,CAACqG,iBAAiB,EACvB;QACD;;MAGD;MACA;MACA/G,CAAC,CAACiG,cAAc,EAAE;MAClBvF,MAAM,CAACmG,QAAQ,EAAE;KACjB;IArrBA,IAAI,CAAClI,OAAO,GAAG,IAAIhB,aAAa,CAACkG,aAAa,EAAElF,OAAO,CAAC;IACxD,IAAI,CAACoE,OAAO,GAAGa,OAAO,CAACoD,UAAU,EAAE;IACnC,IAAI,CAAC1F,OAAO,GAAGsC,OAAO,CAACqD,UAAU,EAAE;IACnC,IAAI,CAACpE,QAAQ,GAAGe,OAAO,CAACsD,WAAW,EAAE;IACrC,IAAI,CAACd,iBAAiB,GAAG,IAAIhJ,iBAAiB,CAAC,IAAI,CAACkH,gBAAgB,CAAC", "names": ["EnterLeave<PERSON><PERSON>nter", "createNativeDragSource", "matchNativeItemType", "NativeTypes", "getDragPreviewOffset", "getEventClientOffset", "getNodeClientOffset", "OptionsReader", "HTML5BackendImpl", "profile", "ref", "ref1", "sourcePreviewNodes", "size", "sourcePreviewNodeOptions", "sourceNodeOptions", "sourceNodes", "dragStartSourceIds", "length", "dropTargetIds", "dragEnterTargetIds", "dragOverTargetIds", "window", "options", "document", "rootElement", "setup", "root", "undefined", "__isReactDndBackendSetUp", "Error", "addEventListeners", "teardown", "removeEventListeners", "clearCurrentDragSourceNode", "asyncEndDragFrameId", "cancelAnimationFrame", "connectDragPreview", "sourceId", "node", "set", "delete", "connectDragSource", "handleDragStart", "e", "handleSelectStart", "setAttribute", "addEventListener", "removeEventListener", "connectDropTarget", "targetId", "handleDragEnter", "handleDragOver", "handleDrop", "target", "handleTopDragStart", "handleTopDragStartCapture", "handleTopDragEndCapture", "handleTopDragEnter", "handleTopDragEnterCapture", "handleTopDragLeaveCapture", "handleTopDragOver", "handleTopDragOverCapture", "handleTopDrop", "handleTopDropCapture", "getCurrentSourceNodeOptions", "monitor", "getSourceId", "get", "_objectSpread", "dropEffect", "altKeyPressed", "getCurrentDropEffect", "isDraggingNativeItem", "getCurrentSourcePreviewNodeOptions", "anchorX", "anchorY", "captureDraggingState", "itemType", "getItemType", "Object", "keys", "some", "key", "beginDragNativeItem", "type", "dataTransfer", "currentNativeSource", "currentNative<PERSON><PERSON>le", "registry", "addSource", "actions", "beginDrag", "setCurrentDragSourceNode", "currentDragSourceNode", "MOUSE_MOVE_TIMEOUT", "mouseMoveTimeoutTimer", "setTimeout", "endDragIfSourceWasRemovedFromDOM", "clearTimeout", "defaultPrevented", "unshift", "_e", "constructor", "manager", "globalContext", "Map", "lastClientOffset", "hoverRafId", "getSourceClientOffset", "source", "endDragNativeItem", "endDrag", "removeSource", "isNodeInDocument", "Boolean", "body", "contains", "isDragging", "cancelHover", "scheduleHover", "requestAnimationFrame", "hover", "clientOffset", "publishSource", "nativeType", "setDragImage", "sourceNode", "dragPreview", "offsetX", "offsetY", "anchorPoint", "offsetPoint", "dragPreviewOffset", "x", "y", "setData", "err", "publishDragSource", "types", "hasAttribute", "preventDefault", "loadDataTransfer", "isFirstEnter", "enterLeaveCounter", "enter", "altKey", "canDrop", "canDropOnTarget", "isLastLeave", "leave", "reset", "drop", "dragDrop", "tagName", "isContentEditable", "getActions", "getMonitor", "getRegistry"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd-html5-backend\\src\\HTML5BackendImpl.ts"], "sourcesContent": ["import type {\n\tBackend,\n\t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport { EnterLeaveCounter } from './EnterLeaveCounter.js'\nimport {\n\tcreateNativeDragSource,\n\tmatchNativeItemType,\n} from './NativeDragSources/index.js'\nimport type { NativeDragSource } from './NativeDragSources/NativeDragSource.js'\nimport * as NativeTypes from './NativeTypes.js'\nimport {\n\tgetDragPreviewOffset,\n\tgetEventClientOffset,\n\tgetNodeClientOffset,\n} from './OffsetUtils.js'\nimport { OptionsReader } from './OptionsReader.js'\nimport type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\ntype RootNode = Node & { __isReactDndBackendSetUp: boolean | undefined }\n\nexport class HTML5BackendImpl implements Backend {\n\tprivate options: OptionsReader\n\n\t// React-Dnd Components\n\tprivate actions: DragDropActions\n\tprivate monitor: DragDropMonitor\n\tprivate registry: HandlerRegistry\n\n\t// Internal State\n\tprivate enterLeaveCounter: EnterLeaveCounter\n\n\tprivate sourcePreviewNodes: Map<string, Element> = new Map()\n\tprivate sourcePreviewNodeOptions: Map<string, any> = new Map()\n\tprivate sourceNodes: Map<string, Element> = new Map()\n\tprivate sourceNodeOptions: Map<string, any> = new Map()\n\n\tprivate dragStartSourceIds: string[] | null = null\n\tprivate dropTargetIds: string[] = []\n\tprivate dragEnterTargetIds: string[] = []\n\tprivate currentNativeSource: NativeDragSource | null = null\n\tprivate currentNativeHandle: Identifier | null = null\n\tprivate currentDragSourceNode: Element | null = null\n\tprivate altKeyPressed = false\n\tprivate mouseMoveTimeoutTimer: number | null = null\n\tprivate asyncEndDragFrameId: number | null = null\n\tprivate dragOverTargetIds: string[] | null = null\n\n\tprivate lastClientOffset: XYCoord | null = null\n\tprivate hoverRafId: number | null = null\n\n\tpublic constructor(\n\t\tmanager: DragDropManager,\n\t\tglobalContext?: HTML5BackendContext,\n\t\toptions?: HTML5BackendOptions,\n\t) {\n\t\tthis.options = new OptionsReader(globalContext, options)\n\t\tthis.actions = manager.getActions()\n\t\tthis.monitor = manager.getMonitor()\n\t\tthis.registry = manager.getRegistry()\n\t\tthis.enterLeaveCounter = new EnterLeaveCounter(this.isNodeInDocument)\n\t}\n\n\t/**\n\t * Generate profiling statistics for the HTML5Backend.\n\t */\n\tpublic profile(): Record<string, number> {\n\t\treturn {\n\t\t\tsourcePreviewNodes: this.sourcePreviewNodes.size,\n\t\t\tsourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n\t\t\tsourceNodeOptions: this.sourceNodeOptions.size,\n\t\t\tsourceNodes: this.sourceNodes.size,\n\t\t\tdragStartSourceIds: this.dragStartSourceIds?.length || 0,\n\t\t\tdropTargetIds: this.dropTargetIds.length,\n\t\t\tdragEnterTargetIds: this.dragEnterTargetIds.length,\n\t\t\tdragOverTargetIds: this.dragOverTargetIds?.length || 0,\n\t\t}\n\t}\n\n\t// public for test\n\tpublic get window(): Window | undefined {\n\t\treturn this.options.window\n\t}\n\tpublic get document(): Document | undefined {\n\t\treturn this.options.document\n\t}\n\t/**\n\t * Get the root element to use for event subscriptions\n\t */\n\tprivate get rootElement(): Node | undefined {\n\t\treturn this.options.rootElement as Node\n\t}\n\n\tpublic setup(): void {\n\t\tconst root = this.rootElement as RootNode | undefined\n\t\tif (root === undefined) {\n\t\t\treturn\n\t\t}\n\n\t\tif (root.__isReactDndBackendSetUp) {\n\t\t\tthrow new Error('Cannot have two HTML5 backends at the same time.')\n\t\t}\n\t\troot.__isReactDndBackendSetUp = true\n\t\tthis.addEventListeners(root)\n\t}\n\n\tpublic teardown(): void {\n\t\tconst root = this.rootElement as RootNode\n\t\tif (root === undefined) {\n\t\t\treturn\n\t\t}\n\n\t\troot.__isReactDndBackendSetUp = false\n\t\tthis.removeEventListeners(this.rootElement as Element)\n\t\tthis.clearCurrentDragSourceNode()\n\t\tif (this.asyncEndDragFrameId) {\n\t\t\tthis.window?.cancelAnimationFrame(this.asyncEndDragFrameId)\n\t\t}\n\t}\n\n\tpublic connectDragPreview(\n\t\tsourceId: string,\n\t\tnode: Element,\n\t\toptions: any,\n\t): Unsubscribe {\n\t\tthis.sourcePreviewNodeOptions.set(sourceId, options)\n\t\tthis.sourcePreviewNodes.set(sourceId, node)\n\n\t\treturn (): void => {\n\t\t\tthis.sourcePreviewNodes.delete(sourceId)\n\t\t\tthis.sourcePreviewNodeOptions.delete(sourceId)\n\t\t}\n\t}\n\n\tpublic connectDragSource(\n\t\tsourceId: string,\n\t\tnode: Element,\n\t\toptions: any,\n\t): Unsubscribe {\n\t\tthis.sourceNodes.set(sourceId, node)\n\t\tthis.sourceNodeOptions.set(sourceId, options)\n\n\t\tconst handleDragStart = (e: any) => this.handleDragStart(e, sourceId)\n\t\tconst handleSelectStart = (e: any) => this.handleSelectStart(e)\n\n\t\tnode.setAttribute('draggable', 'true')\n\t\tnode.addEventListener('dragstart', handleDragStart)\n\t\tnode.addEventListener('selectstart', handleSelectStart)\n\n\t\treturn (): void => {\n\t\t\tthis.sourceNodes.delete(sourceId)\n\t\t\tthis.sourceNodeOptions.delete(sourceId)\n\n\t\t\tnode.removeEventListener('dragstart', handleDragStart)\n\t\t\tnode.removeEventListener('selectstart', handleSelectStart)\n\t\t\tnode.setAttribute('draggable', 'false')\n\t\t}\n\t}\n\n\tpublic connectDropTarget(targetId: string, node: HTMLElement): Unsubscribe {\n\t\tconst handleDragEnter = (e: DragEvent) => this.handleDragEnter(e, targetId)\n\t\tconst handleDragOver = (e: DragEvent) => this.handleDragOver(e, targetId)\n\t\tconst handleDrop = (e: DragEvent) => this.handleDrop(e, targetId)\n\n\t\tnode.addEventListener('dragenter', handleDragEnter)\n\t\tnode.addEventListener('dragover', handleDragOver)\n\t\tnode.addEventListener('drop', handleDrop)\n\n\t\treturn (): void => {\n\t\t\tnode.removeEventListener('dragenter', handleDragEnter)\n\t\t\tnode.removeEventListener('dragover', handleDragOver)\n\t\t\tnode.removeEventListener('drop', handleDrop)\n\t\t}\n\t}\n\n\tprivate addEventListeners(target: Node) {\n\t\t// SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n\t\tif (!target.addEventListener) {\n\t\t\treturn\n\t\t}\n\t\ttarget.addEventListener(\n\t\t\t'dragstart',\n\t\t\tthis.handleTopDragStart as EventListener,\n\t\t)\n\t\ttarget.addEventListener('dragstart', this.handleTopDragStartCapture, true)\n\t\ttarget.addEventListener('dragend', this.handleTopDragEndCapture, true)\n\t\ttarget.addEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnter as EventListener,\n\t\t)\n\t\ttarget.addEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnterCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener(\n\t\t\t'dragleave',\n\t\t\tthis.handleTopDragLeaveCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener('dragover', this.handleTopDragOver as EventListener)\n\t\ttarget.addEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOverCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener('drop', this.handleTopDrop as EventListener)\n\t\ttarget.addEventListener(\n\t\t\t'drop',\n\t\t\tthis.handleTopDropCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t}\n\n\tprivate removeEventListeners(target: Node) {\n\t\t// SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n\t\tif (!target.removeEventListener) {\n\t\t\treturn\n\t\t}\n\t\ttarget.removeEventListener('dragstart', this.handleTopDragStart as any)\n\t\ttarget.removeEventListener(\n\t\t\t'dragstart',\n\t\t\tthis.handleTopDragStartCapture,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener('dragend', this.handleTopDragEndCapture, true)\n\t\ttarget.removeEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnter as EventListener,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnterCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragleave',\n\t\t\tthis.handleTopDragLeaveCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOver as EventListener,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOverCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener('drop', this.handleTopDrop as EventListener)\n\t\ttarget.removeEventListener(\n\t\t\t'drop',\n\t\t\tthis.handleTopDropCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t}\n\n\tprivate getCurrentSourceNodeOptions() {\n\t\tconst sourceId = this.monitor.getSourceId() as string\n\t\tconst sourceNodeOptions = this.sourceNodeOptions.get(sourceId)\n\n\t\treturn {\n\t\t\tdropEffect: this.altKeyPressed ? 'copy' : 'move',\n\t\t\t...(sourceNodeOptions || {}),\n\t\t}\n\t}\n\n\tprivate getCurrentDropEffect() {\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\t// It makes more sense to default to 'copy' for native resources\n\t\t\treturn 'copy'\n\t\t}\n\n\t\treturn this.getCurrentSourceNodeOptions().dropEffect\n\t}\n\n\tprivate getCurrentSourcePreviewNodeOptions() {\n\t\tconst sourceId = this.monitor.getSourceId() as string\n\t\tconst sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId)\n\n\t\treturn {\n\t\t\tanchorX: 0.5,\n\t\t\tanchorY: 0.5,\n\t\t\tcaptureDraggingState: false,\n\t\t\t...(sourcePreviewNodeOptions || {}),\n\t\t}\n\t}\n\n\tprivate getSourceClientOffset = (sourceId: string): XYCoord | null => {\n\t\tconst source = this.sourceNodes.get(sourceId)\n\t\treturn (source && getNodeClientOffset(source as HTMLElement)) || null\n\t}\n\n\tprivate isDraggingNativeItem() {\n\t\tconst itemType = this.monitor.getItemType()\n\t\treturn Object.keys(NativeTypes).some(\n\t\t\t(key: string) => (NativeTypes as any)[key] === itemType,\n\t\t)\n\t}\n\n\tprivate beginDragNativeItem(type: string, dataTransfer?: DataTransfer) {\n\t\tthis.clearCurrentDragSourceNode()\n\n\t\tthis.currentNativeSource = createNativeDragSource(type, dataTransfer)\n\t\tthis.currentNativeHandle = this.registry.addSource(\n\t\t\ttype,\n\t\t\tthis.currentNativeSource,\n\t\t)\n\t\tthis.actions.beginDrag([this.currentNativeHandle])\n\t}\n\n\tprivate endDragNativeItem = (): void => {\n\t\tif (!this.isDraggingNativeItem()) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.actions.endDrag()\n\t\tif (this.currentNativeHandle) {\n\t\t\tthis.registry.removeSource(this.currentNativeHandle)\n\t\t}\n\t\tthis.currentNativeHandle = null\n\t\tthis.currentNativeSource = null\n\t}\n\n\tprivate isNodeInDocument = (node: Node | null | undefined): boolean => {\n\t\t// Check the node either in the main document or in the current context\n\t\treturn Boolean(\n\t\t\tnode &&\n\t\t\t\tthis.document &&\n\t\t\t\tthis.document.body &&\n\t\t\t\tthis.document.body.contains(node),\n\t\t)\n\t}\n\n\tprivate endDragIfSourceWasRemovedFromDOM = (): void => {\n\t\tconst node = this.currentDragSourceNode\n\t\tif (node == null || this.isNodeInDocument(node)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tprivate setCurrentDragSourceNode(node: Element | null) {\n\t\tthis.clearCurrentDragSourceNode()\n\t\tthis.currentDragSourceNode = node\n\n\t\t// A timeout of > 0 is necessary to resolve Firefox issue referenced\n\t\t// See:\n\t\t//   * https://github.com/react-dnd/react-dnd/pull/928\n\t\t//   * https://github.com/react-dnd/react-dnd/issues/869\n\t\tconst MOUSE_MOVE_TIMEOUT = 1000\n\n\t\t// Receiving a mouse event in the middle of a dragging operation\n\t\t// means it has ended and the drag source node disappeared from DOM,\n\t\t// so the browser didn't dispatch the dragend event.\n\t\t//\n\t\t// We need to wait before we start listening for mousemove events.\n\t\t// This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n\t\t// immediately in some browsers.\n\t\t//\n\t\t// See:\n\t\t//   * https://github.com/react-dnd/react-dnd/pull/928\n\t\t//   * https://github.com/react-dnd/react-dnd/issues/869\n\t\t//\n\t\tthis.mouseMoveTimeoutTimer = setTimeout(() => {\n\t\t\treturn this.rootElement?.addEventListener(\n\t\t\t\t'mousemove',\n\t\t\t\tthis.endDragIfSourceWasRemovedFromDOM,\n\t\t\t\ttrue,\n\t\t\t)\n\t\t}, MOUSE_MOVE_TIMEOUT) as any as number\n\t}\n\n\tprivate clearCurrentDragSourceNode() {\n\t\tif (this.currentDragSourceNode) {\n\t\t\tthis.currentDragSourceNode = null\n\n\t\t\tif (this.rootElement) {\n\t\t\t\tthis.window?.clearTimeout(this.mouseMoveTimeoutTimer || undefined)\n\t\t\t\tthis.rootElement.removeEventListener(\n\t\t\t\t\t'mousemove',\n\t\t\t\t\tthis.endDragIfSourceWasRemovedFromDOM,\n\t\t\t\t\ttrue,\n\t\t\t\t)\n\t\t\t}\n\n\t\t\tthis.mouseMoveTimeoutTimer = null\n\t\t\treturn true\n\t\t}\n\n\t\treturn false\n\t}\n\n\tprivate scheduleHover = (dragOverTargetIds: string[] | null) => {\n\t\tif (\n\t\t\tthis.hoverRafId === null &&\n\t\t\ttypeof requestAnimationFrame !== 'undefined'\n\t\t) {\n\t\t\tthis.hoverRafId = requestAnimationFrame(() => {\n\t\t\t\tif (this.monitor.isDragging()) {\n\t\t\t\t\tthis.actions.hover(dragOverTargetIds || [], {\n\t\t\t\t\t\tclientOffset: this.lastClientOffset,\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tthis.hoverRafId = null\n\t\t\t})\n\t\t}\n\t}\n\n\tprivate cancelHover = () => {\n\t\tif (\n\t\t\tthis.hoverRafId !== null &&\n\t\t\ttypeof cancelAnimationFrame !== 'undefined'\n\t\t) {\n\t\t\tcancelAnimationFrame(this.hoverRafId)\n\t\t\tthis.hoverRafId = null\n\t\t}\n\t}\n\n\tpublic handleTopDragStartCapture = (): void => {\n\t\tthis.clearCurrentDragSourceNode()\n\t\tthis.dragStartSourceIds = []\n\t}\n\n\tpublic handleDragStart(e: DragEvent, sourceId: string): void {\n\t\tif (e.defaultPrevented) {\n\t\t\treturn\n\t\t}\n\n\t\tif (!this.dragStartSourceIds) {\n\t\t\tthis.dragStartSourceIds = []\n\t\t}\n\t\tthis.dragStartSourceIds.unshift(sourceId)\n\t}\n\n\tpublic handleTopDragStart = (e: DragEvent): void => {\n\t\tif (e.defaultPrevented) {\n\t\t\treturn\n\t\t}\n\n\t\tconst { dragStartSourceIds } = this\n\t\tthis.dragStartSourceIds = null\n\n\t\tconst clientOffset = getEventClientOffset(e)\n\n\t\t// Avoid crashing if we missed a drop event or our previous drag died\n\t\tif (this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t\tthis.cancelHover()\n\t\t}\n\n\t\t// Don't publish the source just yet (see why below)\n\t\tthis.actions.beginDrag(dragStartSourceIds || [], {\n\t\t\tpublishSource: false,\n\t\t\tgetSourceClientOffset: this.getSourceClientOffset,\n\t\t\tclientOffset,\n\t\t})\n\n\t\tconst { dataTransfer } = e\n\t\tconst nativeType = matchNativeItemType(dataTransfer)\n\n\t\tif (this.monitor.isDragging()) {\n\t\t\tif (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n\t\t\t\t// Use custom drag image if user specifies it.\n\t\t\t\t// If child drag source refuses drag but parent agrees,\n\t\t\t\t// use parent's node as drag image. Neither works in IE though.\n\t\t\t\tconst sourceId: string = this.monitor.getSourceId() as string\n\t\t\t\tconst sourceNode = this.sourceNodes.get(sourceId)\n\t\t\t\tconst dragPreview = this.sourcePreviewNodes.get(sourceId) || sourceNode\n\n\t\t\t\tif (dragPreview) {\n\t\t\t\t\tconst { anchorX, anchorY, offsetX, offsetY } =\n\t\t\t\t\t\tthis.getCurrentSourcePreviewNodeOptions()\n\t\t\t\t\tconst anchorPoint = { anchorX, anchorY }\n\t\t\t\t\tconst offsetPoint = { offsetX, offsetY }\n\t\t\t\t\tconst dragPreviewOffset = getDragPreviewOffset(\n\t\t\t\t\t\tsourceNode as HTMLElement,\n\t\t\t\t\t\tdragPreview as HTMLElement,\n\t\t\t\t\t\tclientOffset,\n\t\t\t\t\t\tanchorPoint,\n\t\t\t\t\t\toffsetPoint,\n\t\t\t\t\t)\n\n\t\t\t\t\tdataTransfer.setDragImage(\n\t\t\t\t\t\tdragPreview,\n\t\t\t\t\t\tdragPreviewOffset.x,\n\t\t\t\t\t\tdragPreviewOffset.y,\n\t\t\t\t\t)\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// Firefox won't drag without setting data\n\t\t\t\tdataTransfer?.setData('application/json', {} as any)\n\t\t\t} catch (err) {\n\t\t\t\t// IE doesn't support MIME types in setData\n\t\t\t}\n\n\t\t\t// Store drag source node so we can check whether\n\t\t\t// it is removed from DOM and trigger endDrag manually.\n\t\t\tthis.setCurrentDragSourceNode(e.target as Element)\n\n\t\t\t// Now we are ready to publish the drag source.. or are we not?\n\t\t\tconst { captureDraggingState } = this.getCurrentSourcePreviewNodeOptions()\n\t\t\tif (!captureDraggingState) {\n\t\t\t\t// Usually we want to publish it in the next tick so that browser\n\t\t\t\t// is able to screenshot the current (not yet dragging) state.\n\t\t\t\t//\n\t\t\t\t// It also neatly avoids a situation where render() returns null\n\t\t\t\t// in the same tick for the source element, and browser freaks out.\n\t\t\t\tsetTimeout(() => this.actions.publishDragSource(), 0)\n\t\t\t} else {\n\t\t\t\t// In some cases the user may want to override this behavior, e.g.\n\t\t\t\t// to work around IE not supporting custom drag previews.\n\t\t\t\t//\n\t\t\t\t// When using a custom drag layer, the only way to prevent\n\t\t\t\t// the default drag preview from drawing in IE is to screenshot\n\t\t\t\t// the dragging state in which the node itself has zero opacity\n\t\t\t\t// and height. In this case, though, returning null from render()\n\t\t\t\t// will abruptly end the dragging, which is not obvious.\n\t\t\t\t//\n\t\t\t\t// This is the reason such behavior is strictly opt-in.\n\t\t\t\tthis.actions.publishDragSource()\n\t\t\t}\n\t\t} else if (nativeType) {\n\t\t\t// A native item (such as URL) dragged from inside the document\n\t\t\tthis.beginDragNativeItem(nativeType)\n\t\t} else if (\n\t\t\tdataTransfer &&\n\t\t\t!dataTransfer.types &&\n\t\t\t((e.target && !(e.target as Element).hasAttribute) ||\n\t\t\t\t!(e.target as Element).hasAttribute('draggable'))\n\t\t) {\n\t\t\t// Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n\t\t\t// Just let it drag. It's a native type (URL or text) and will be picked up in\n\t\t\t// dragenter handler.\n\t\t\treturn\n\t\t} else {\n\t\t\t// If by this time no drag source reacted, tell browser not to drag.\n\t\t\te.preventDefault()\n\t\t}\n\t}\n\n\tpublic handleTopDragEndCapture = (): void => {\n\t\tif (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n\t\t\t// Firefox can dispatch this event in an infinite loop\n\t\t\t// if dragend handler does something like showing an alert.\n\t\t\t// Only proceed if we have not handled it already.\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleTopDragEnterCapture = (e: DragEvent): void => {\n\t\tthis.dragEnterTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t}\n\n\t\tconst isFirstEnter = this.enterLeaveCounter.enter(e.target)\n\t\tif (!isFirstEnter || this.monitor.isDragging()) {\n\t\t\treturn\n\t\t}\n\n\t\tconst { dataTransfer } = e\n\t\tconst nativeType = matchNativeItemType(dataTransfer)\n\n\t\tif (nativeType) {\n\t\t\t// A native item (such as file or URL) dragged from outside the document\n\t\t\tthis.beginDragNativeItem(nativeType, dataTransfer as DataTransfer)\n\t\t}\n\t}\n\n\tpublic handleDragEnter(_e: DragEvent, targetId: string): void {\n\t\tthis.dragEnterTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDragEnter = (e: DragEvent): void => {\n\t\tconst { dragEnterTargetIds } = this\n\t\tthis.dragEnterTargetIds = []\n\n\t\tif (!this.monitor.isDragging()) {\n\t\t\t// This is probably a native item type we don't understand.\n\t\t\treturn\n\t\t}\n\n\t\tthis.altKeyPressed = e.altKey\n\n\t\t// If the target changes position as the result of `dragenter`, `dragover` might still\n\t\t// get dispatched despite target being no longer there. The easy solution is to check\n\t\t// whether there actually is a target before firing `hover`.\n\t\tif (dragEnterTargetIds.length > 0) {\n\t\t\tthis.actions.hover(dragEnterTargetIds, {\n\t\t\t\tclientOffset: getEventClientOffset(e),\n\t\t\t})\n\t\t}\n\n\t\tconst canDrop = dragEnterTargetIds.some((targetId) =>\n\t\t\tthis.monitor.canDropOnTarget(targetId),\n\t\t)\n\n\t\tif (canDrop) {\n\t\t\t// IE requires this to fire dragover events\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = this.getCurrentDropEffect()\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic handleTopDragOverCapture = (e: DragEvent): void => {\n\t\tthis.dragOverTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t}\n\t}\n\n\tpublic handleDragOver(_e: DragEvent, targetId: string): void {\n\t\tif (this.dragOverTargetIds === null) {\n\t\t\tthis.dragOverTargetIds = []\n\t\t}\n\t\tthis.dragOverTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDragOver = (e: DragEvent): void => {\n\t\tconst { dragOverTargetIds } = this\n\t\tthis.dragOverTargetIds = []\n\n\t\tif (!this.monitor.isDragging()) {\n\t\t\t// This is probably a native item type we don't understand.\n\t\t\t// Prevent default \"drop and blow away the whole document\" action.\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = 'none'\n\t\t\t}\n\t\t\treturn\n\t\t}\n\n\t\tthis.altKeyPressed = e.altKey\n\t\tthis.lastClientOffset = getEventClientOffset(e)\n\n\t\tthis.scheduleHover(dragOverTargetIds)\n\n\t\tconst canDrop = (dragOverTargetIds || []).some((targetId) =>\n\t\t\tthis.monitor.canDropOnTarget(targetId),\n\t\t)\n\n\t\tif (canDrop) {\n\t\t\t// Show user-specified drop effect.\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = this.getCurrentDropEffect()\n\t\t\t}\n\t\t} else if (this.isDraggingNativeItem()) {\n\t\t\t// Don't show a nice cursor but still prevent default\n\t\t\t// \"drop and blow away the whole document\" action.\n\t\t\te.preventDefault()\n\t\t} else {\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = 'none'\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic handleTopDragLeaveCapture = (e: DragEvent): void => {\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\te.preventDefault()\n\t\t}\n\n\t\tconst isLastLeave = this.enterLeaveCounter.leave(e.target)\n\t\tif (!isLastLeave) {\n\t\t\treturn\n\t\t}\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tsetTimeout(() => this.endDragNativeItem(), 0)\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleTopDropCapture = (e: DragEvent): void => {\n\t\tthis.dropTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\te.preventDefault()\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t} else if (matchNativeItemType(e.dataTransfer)) {\n\t\t\t// Dragging some elements, like <a> and <img> may still behave like a native drag event,\n\t\t\t// even if the current drag event matches a user-defined type.\n\t\t\t// Stop the default behavior when we're not expecting a native item to be dropped.\n\n\t\t\te.preventDefault()\n\t\t}\n\n\t\tthis.enterLeaveCounter.reset()\n\t}\n\n\tpublic handleDrop(_e: DragEvent, targetId: string): void {\n\t\tthis.dropTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDrop = (e: DragEvent): void => {\n\t\tconst { dropTargetIds } = this\n\t\tthis.dropTargetIds = []\n\n\t\tthis.actions.hover(dropTargetIds, {\n\t\t\tclientOffset: getEventClientOffset(e),\n\t\t})\n\t\tthis.actions.drop({ dropEffect: this.getCurrentDropEffect() })\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.endDragNativeItem()\n\t\t} else if (this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleSelectStart = (e: DragEvent): void => {\n\t\tconst target = e.target as HTMLElement & { dragDrop: () => void }\n\n\t\t// Only IE requires us to explicitly say\n\t\t// we want drag drop operation to start\n\t\tif (typeof target.dragDrop !== 'function') {\n\t\t\treturn\n\t\t}\n\n\t\t// Inputs and textareas should be selectable\n\t\tif (\n\t\t\ttarget.tagName === 'INPUT' ||\n\t\t\ttarget.tagName === 'SELECT' ||\n\t\t\ttarget.tagName === 'TEXTAREA' ||\n\t\t\ttarget.isContentEditable\n\t\t) {\n\t\t\treturn\n\t\t}\n\n\t\t// For other targets, ask IE\n\t\t// to enable drag and drop\n\t\te.preventDefault()\n\t\ttarget.dragDrop()\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}