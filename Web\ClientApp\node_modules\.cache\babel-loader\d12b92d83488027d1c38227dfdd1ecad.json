{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { plantApi } from 'api/plant-service';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const savePlants = createAsyncThunk('plants/save-plants', async (plants, _ref) => {\n  let {\n    rejectWithValue\n  } = _ref;\n  try {\n    const updatedPlants = await plantApi.saveAll(plants);\n    return updatedPlants;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const generateAndSaveSortOrder = createAsyncThunk('plants/generate-and-save-sort-order', async (_, _ref2) => {\n  let {\n    getState,\n    rejectWithValue\n  } = _ref2;\n  try {\n    const state = getState();\n    const plants = state.plants.plants;\n\n    // Check if all plants have null stickingSortOrder\n    const allHaveNullSortOrder = plants.every(p => p.stickingSortOrder == null);\n    if (allHaveNullSortOrder) {\n      // Sort plants by the default sort order and assign stickingSortOrder\n      const sortedPlants = [...plants].sort(sortPlant);\n      const plantsWithSortOrder = sortedPlants.map((plant, index) => ({\n        ...plant,\n        stickingSortOrder: index\n      }));\n\n      // Save all plants with their new sort order\n      const updatedPlants = await plantApi.saveAll(plantsWithSortOrder);\n      return updatedPlants;\n    }\n\n    // If not all have null sort order, just return the current plants\n    return plants;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    },\n    moveItem(state, action) {\n      const {\n        movingItem,\n        existingItem\n      } = action.payload;\n      console.log(\"moving item\", existingItem, movingItem);\n      if (existingItem.stickingSortOrder == null) {\n        // generate a new order for all items based on current index\n        state.plants = state.plants.sort(sortPlant).map((p, index) => ({\n          ...p,\n          stickingSortOrder: index\n        }));\n      }\n      const existingItemIndex = state.plants.findIndex(p => p._id === existingItem._id);\n\n      // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1\n      state.plants = state.plants.map(p => {\n        if (p._id === existingItem._id) {\n          return {\n            ...p,\n            stickingSortOrder: existingItemIndex + 1\n          };\n        } else if (p._id === movingItem._id) {\n          return {\n            ...p,\n            stickingSortOrder: existingItemIndex\n          };\n        } else if (p.stickingSortOrder != null && p.stickingSortOrder > 1) {\n          return {\n            ...p,\n            stickingSortOrder: p.stickingSortOrder + 1\n          };\n        }\n        return p;\n      });\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(savePlants.fulfilled, (state, action) => {\n      // Update the plants in state with the saved versions (which have updated _rev values)\n      const savedPlants = action.payload;\n      state.plants = state.plants.map(plant => {\n        const savedPlant = savedPlants.find(sp => sp._id === plant._id);\n        return savedPlant || plant;\n      });\n    }).addCase(generateAndSaveSortOrder.fulfilled, (state, action) => {\n      // Replace all plants with the updated versions that have sort orders\n      state.plants = action.payload;\n    });\n  }\n});\nexport const {\n  setPlants,\n  moveItem\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nfunction sortByStickingSortOrder(a, b) {\n  // Handle null values - place them last\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1; // b comes before a (non-null before null)\n\n  // Both are non-null, compare normally\n  return a.stickingSortOrder - b.stickingSortOrder;\n}\nfunction sortPlant(a, b) {\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "plantApi", "sortBy", "sortSizeName", "initialState", "plants", "savePlants", "rejectWithValue", "updatedPlants", "saveAll", "e", "generateAndSaveSortOrder", "_", "getState", "state", "allHaveNullSortOrder", "every", "p", "stickingSortOrder", "sortedPlants", "sort", "sortPlant", "plantsWithSortOrder", "map", "plant", "index", "plantsSlice", "name", "reducers", "setPlants", "action", "payload", "moveItem", "movingItem", "existingItem", "console", "log", "existingItemIndex", "findIndex", "_id", "extraReducers", "builder", "addCase", "fulfilled", "savedPlants", "savedPlant", "find", "sp", "actions", "selectPlants", "reducer", "sortByCrop", "sortByStickingSortOrder", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { plantApi } from 'api/plant-service';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const savePlants = createAsyncThunk<Plant[], Plant[], { state: RootState }>(\r\n  'plants/save-plants',\r\n  async (plants, { rejectWithValue }) => {\r\n    try {\r\n      const updatedPlants = await plantApi.saveAll(plants);\r\n      return updatedPlants;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const generateAndSaveSortOrder = createAsyncThunk<Plant[], void, { state: RootState }>(\r\n  'plants/generate-and-save-sort-order',\r\n  async (_, { getState, rejectWithValue }) => {\r\n    try {\r\n      const state = getState();\r\n      const plants = state.plants.plants;\r\n\r\n      // Check if all plants have null stickingSortOrder\r\n      const allHaveNullSortOrder = plants.every(p => p.stickingSortOrder == null);\r\n\r\n      if (allHaveNullSortOrder) {\r\n        // Sort plants by the default sort order and assign stickingSortOrder\r\n        const sortedPlants = [...plants].sort(sortPlant);\r\n        const plantsWithSortOrder = sortedPlants.map((plant, index) => ({\r\n          ...plant,\r\n          stickingSortOrder: index\r\n        }));\r\n\r\n        // Save all plants with their new sort order\r\n        const updatedPlants = await plantApi.saveAll(plantsWithSortOrder);\r\n        return updatedPlants;\r\n      }\r\n\r\n      // If not all have null sort order, just return the current plants\r\n      return plants;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    },\r\n    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {\r\n      const { movingItem, existingItem } = action.payload;\r\n\r\n      console.log(\"moving item\", existingItem, movingItem);\r\n\r\n      if (existingItem.stickingSortOrder == null) {\r\n        // generate a new order for all items based on current index\r\n        state.plants = state.plants.sort(sortPlant).map((p, index) => ({ ...p, stickingSortOrder: index }));\r\n      }\r\n\r\n      const existingItemIndex = state.plants.findIndex(p => p._id === existingItem._id);\r\n\r\n      // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1\r\n      state.plants = state.plants.map(p => {\r\n        if (p._id === existingItem._id) {\r\n          return { ...p, stickingSortOrder: existingItemIndex + 1 };\r\n        } else if (p._id === movingItem._id) {\r\n          return { ...p, stickingSortOrder: existingItemIndex };\r\n        } else if (p.stickingSortOrder != null && p.stickingSortOrder > 1) {\r\n          return { ...p, stickingSortOrder: p.stickingSortOrder + 1 };\r\n        }\r\n        return p;\r\n      });\r\n    }\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(savePlants.fulfilled, (state, action) => {\r\n        // Update the plants in state with the saved versions (which have updated _rev values)\r\n        const savedPlants = action.payload;\r\n        state.plants = state.plants.map(plant => {\r\n          const savedPlant = savedPlants.find(sp => sp._id === plant._id);\r\n          return savedPlant || plant;\r\n        });\r\n      })\r\n      .addCase(generateAndSaveSortOrder.fulfilled, (state, action) => {\r\n        // Replace all plants with the updated versions that have sort orders\r\n        state.plants = action.payload;\r\n      });\r\n  }\r\n});\r\n\r\nexport const { setPlants, moveItem } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\n\r\nfunction sortByStickingSortOrder(a: Plant, b: Plant) {\r\n  // Handle null values - place them last\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\r\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1;  // b comes before a (non-null before null)\r\n\r\n  // Both are non-null, compare normally\r\n  return a.stickingSortOrder! - b.stickingSortOrder!;\r\n}\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,EAAiBC,gBAAgB,QAAQ,kBAAkB;AAE/E,SAASC,QAAQ,QAAQ,mBAAmB;AAE5C,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAOjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGN,gBAAgB,CACxC,oBAAoB,EACpB,OAAOK,MAAM,WAA0B;EAAA,IAAxB;IAAEE;EAAgB,CAAC;EAChC,IAAI;IACF,MAAMC,aAAa,GAAG,MAAMP,QAAQ,CAACQ,OAAO,CAACJ,MAAM,CAAC;IACpD,OAAOG,aAAa;EACtB,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAOH,eAAe,CAACG,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,wBAAwB,GAAGX,gBAAgB,CACtD,qCAAqC,EACrC,OAAOY,CAAC,YAAoC;EAAA,IAAlC;IAAEC,QAAQ;IAAEN;EAAgB,CAAC;EACrC,IAAI;IACF,MAAMO,KAAK,GAAGD,QAAQ,EAAE;IACxB,MAAMR,MAAM,GAAGS,KAAK,CAACT,MAAM,CAACA,MAAM;;IAElC;IACA,MAAMU,oBAAoB,GAAGV,MAAM,CAACW,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,IAAI,IAAI,CAAC;IAE3E,IAAIH,oBAAoB,EAAE;MACxB;MACA,MAAMI,YAAY,GAAG,CAAC,GAAGd,MAAM,CAAC,CAACe,IAAI,CAACC,SAAS,CAAC;MAChD,MAAMC,mBAAmB,GAAGH,YAAY,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;QAC9D,GAAGD,KAAK;QACRN,iBAAiB,EAAEO;MACrB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMjB,aAAa,GAAG,MAAMP,QAAQ,CAACQ,OAAO,CAACa,mBAAmB,CAAC;MACjE,OAAOd,aAAa;IACtB;;IAEA;IACA,OAAOH,MAAM;EACf,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV,OAAOH,eAAe,CAACG,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMgB,WAAW,GAAG3B,WAAW,CAAC;EACrC4B,IAAI,EAAE,QAAQ;EACdvB,YAAY;EACZwB,QAAQ,EAAE;IACRC,SAAS,CAACf,KAAK,EAAEgB,MAA8B,EAAE;MAC/ChB,KAAK,CAACT,MAAM,GAAGyB,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDC,QAAQ,CAAClB,KAAK,EAAEgB,MAAiE,EAAE;MACjF,MAAM;QAAEG,UAAU;QAAEC;MAAa,CAAC,GAAGJ,MAAM,CAACC,OAAO;MAEnDI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,YAAY,EAAED,UAAU,CAAC;MAEpD,IAAIC,YAAY,CAAChB,iBAAiB,IAAI,IAAI,EAAE;QAC1C;QACAJ,KAAK,CAACT,MAAM,GAAGS,KAAK,CAACT,MAAM,CAACe,IAAI,CAACC,SAAS,CAAC,CAACE,GAAG,CAAC,CAACN,CAAC,EAAEQ,KAAK,MAAM;UAAE,GAAGR,CAAC;UAAEC,iBAAiB,EAAEO;QAAM,CAAC,CAAC,CAAC;MACrG;MAEA,MAAMY,iBAAiB,GAAGvB,KAAK,CAACT,MAAM,CAACiC,SAAS,CAACrB,CAAC,IAAIA,CAAC,CAACsB,GAAG,KAAKL,YAAY,CAACK,GAAG,CAAC;;MAEjF;MACAzB,KAAK,CAACT,MAAM,GAAGS,KAAK,CAACT,MAAM,CAACkB,GAAG,CAACN,CAAC,IAAI;QACnC,IAAIA,CAAC,CAACsB,GAAG,KAAKL,YAAY,CAACK,GAAG,EAAE;UAC9B,OAAO;YAAE,GAAGtB,CAAC;YAAEC,iBAAiB,EAAEmB,iBAAiB,GAAG;UAAE,CAAC;QAC3D,CAAC,MAAM,IAAIpB,CAAC,CAACsB,GAAG,KAAKN,UAAU,CAACM,GAAG,EAAE;UACnC,OAAO;YAAE,GAAGtB,CAAC;YAAEC,iBAAiB,EAAEmB;UAAkB,CAAC;QACvD,CAAC,MAAM,IAAIpB,CAAC,CAACC,iBAAiB,IAAI,IAAI,IAAID,CAAC,CAACC,iBAAiB,GAAG,CAAC,EAAE;UACjE,OAAO;YAAE,GAAGD,CAAC;YAAEC,iBAAiB,EAAED,CAAC,CAACC,iBAAiB,GAAG;UAAE,CAAC;QAC7D;QACA,OAAOD,CAAC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACDuB,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACpC,UAAU,CAACqC,SAAS,EAAE,CAAC7B,KAAK,EAAEgB,MAAM,KAAK;MAChD;MACA,MAAMc,WAAW,GAAGd,MAAM,CAACC,OAAO;MAClCjB,KAAK,CAACT,MAAM,GAAGS,KAAK,CAACT,MAAM,CAACkB,GAAG,CAACC,KAAK,IAAI;QACvC,MAAMqB,UAAU,GAAGD,WAAW,CAACE,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACR,GAAG,KAAKf,KAAK,CAACe,GAAG,CAAC;QAC/D,OAAOM,UAAU,IAAIrB,KAAK;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,CACDkB,OAAO,CAAC/B,wBAAwB,CAACgC,SAAS,EAAE,CAAC7B,KAAK,EAAEgB,MAAM,KAAK;MAC9D;MACAhB,KAAK,CAACT,MAAM,GAAGyB,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEF,SAAS;EAAEG;AAAS,CAAC,GAAGN,WAAW,CAACsB,OAAO;AAE1D,OAAO,MAAMC,YAAY,GAAInC,KAAgB,IAAKA,KAAK,CAACT,MAAM,CAACA,MAAM,CAACkB,GAAG,CAACN,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACG,IAAI,CAACC,SAAS,CAAC;AAExG,eAAeK,WAAW,CAACwB,OAAO;AAElC,MAAMC,UAAU,GAAGjD,MAAM,CAAC,MAAM,CAAC;AAEjC,SAASkD,uBAAuB,CAACC,CAAQ,EAAEC,CAAQ,EAAE;EACnD;EACA,IAAID,CAAC,CAACnC,iBAAiB,IAAI,IAAI,IAAIoC,CAAC,CAACpC,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC;EACxE,IAAImC,CAAC,CAACnC,iBAAiB,IAAI,IAAI,IAAIoC,CAAC,CAACpC,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3E,IAAImC,CAAC,CAACnC,iBAAiB,IAAI,IAAI,IAAIoC,CAAC,CAACpC,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAE;;EAE3E;EACA,OAAOmC,CAAC,CAACnC,iBAAiB,GAAIoC,CAAC,CAACpC,iBAAkB;AACpD;AAEA,SAASG,SAAS,CAACgC,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,uBAAuB,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIH,UAAU,CAACE,CAAC,EAAEC,CAAC,CAAC,IAAInD,YAAY,CAACkD,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AAC1F"}, "metadata": {}, "sourceType": "module"}