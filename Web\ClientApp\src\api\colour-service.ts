import * as models from './models/colour';
import { ServiceBase } from './service-base';

class ColourService extends ServiceBase {
  getAll() {
    return this.query<models.Colour>('filters/colours');
  }

  save(colour: models.Colour) {
    return this.saveDocument<models.Colour>(colour);
  }

  delete(colour: models.Colour) {
    return super.delete(colour);
  }
}

export const colourApi = new ColourService();
