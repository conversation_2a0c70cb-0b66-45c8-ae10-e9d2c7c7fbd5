{"ast": null, "code": "export const INIT_COORDS = 'dnd-core/INIT_COORDS';\nexport const BEGIN_DRAG = 'dnd-core/BEGIN_DRAG';\nexport const PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE';\nexport const HOVER = 'dnd-core/HOVER';\nexport const DROP = 'dnd-core/DROP';\nexport const END_DRAG = 'dnd-core/END_DRAG';", "map": {"version": 3, "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG,sBAAsB;AACjD,OAAO,MAAMC,UAAU,GAAG,qBAAqB;AAC/C,OAAO,MAAMC,mBAAmB,GAAG,8BAA8B;AACjE,OAAO,MAAMC,KAAK,GAAG,gBAAgB;AACrC,OAAO,MAAMC,IAAI,GAAG,eAAe;AACnC,OAAO,MAAMC,QAAQ,GAAG,mBAAmB", "names": ["INIT_COORDS", "BEGIN_DRAG", "PUBLISH_DRAG_SOURCE", "HOVER", "DROP", "END_DRAG"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\actions\\dragDrop\\types.ts"], "sourcesContent": ["export const INIT_COORDS = 'dnd-core/INIT_COORDS'\nexport const BEGIN_DRAG = 'dnd-core/BEGIN_DRAG'\nexport const PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE'\nexport const HOVER = 'dnd-core/HOVER'\nexport const DROP = 'dnd-core/DROP'\nexport const END_DRAG = 'dnd-core/END_DRAG'\n"]}, "metadata": {}, "sourceType": "module"}