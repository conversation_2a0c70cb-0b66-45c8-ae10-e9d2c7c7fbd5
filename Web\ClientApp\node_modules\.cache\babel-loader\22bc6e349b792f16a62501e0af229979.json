{"ast": null, "code": "let nextUniqueId = 0;\nexport function getNextUniqueId() {\n  return nextUniqueId++;\n}", "map": {"version": 3, "mappings": "AAAA,IAAIA,YAAY,GAAG,CAAC;AAEpB,OAAO,SAASC,eAAe,GAAW;EACzC,OAAOD,YAAY,EAAE", "names": ["nextUniqueId", "getNextUniqueId"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\utils\\getNextUniqueId.ts"], "sourcesContent": ["let nextUniqueId = 0\n\nexport function getNextUniqueId(): number {\n\treturn nextUniqueId++\n}\n"]}, "metadata": {}, "sourceType": "module"}