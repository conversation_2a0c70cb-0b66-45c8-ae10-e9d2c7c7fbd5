import { useSelector } from 'react-redux';
import moment from 'moment';
import { classNames } from 'utils/class-names';
import { formatDate } from 'utils/format';
import { selectTasks } from './driver-task-slice';
import { ListItem } from './List-Item';

interface ListDateProps {
  date: string;
}

export function ListDate({ date }: ListDateProps) {
  const m = moment(date),
    today = moment(),
    isPast = m.isBefore(today, 'day'),
    allTasks = useSelector(selectTasks),
    filteredTasks = allTasks.filter((t) => moment(t.dueDate).isSame(m, 'day')),
    formatted = formatDate(date, 'dddd, MMM D');
  return (
    <>
      <h5
        className={classNames(
          'col-12 p-2 mb-0 border border-2 rounded text-center',
          isPast && 'text-danger'
        )}>
        {formatted}
      </h5>
      <div className="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4 mt-0 mb-4">
        {filteredTasks.map((task) => (
          <ListItem key={task._id} task={task} />
        ))}
      </div>
    </>
  );
}
