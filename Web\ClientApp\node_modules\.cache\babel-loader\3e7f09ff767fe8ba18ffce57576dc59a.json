{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nexport function validateSourceContract(source) {\n  invariant(typeof source.canDrag === 'function', 'Expected canDrag to be a function.');\n  invariant(typeof source.beginDrag === 'function', 'Expected beginDrag to be a function.');\n  invariant(typeof source.endDrag === 'function', 'Expected endDrag to be a function.');\n}\nexport function validateTargetContract(target) {\n  invariant(typeof target.canDrop === 'function', 'Expected canDrop to be a function.');\n  invariant(typeof target.hover === 'function', 'Expected hover to be a function.');\n  invariant(typeof target.drop === 'function', 'Expected beginDrag to be a function.');\n}\nexport function validateType(type, allowArray) {\n  if (allowArray && Array.isArray(type)) {\n    type.forEach(t => validateType(t, false));\n    return;\n  }\n  invariant(typeof type === 'string' || typeof type === 'symbol', allowArray ? 'Type can only be a string, a symbol, or an array of either.' : 'Type can only be a string or a symbol.');\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAIhD,OAAO,SAASC,sBAAsB,CAACC,MAAkB,EAAQ;EAChEF,SAAS,CACR,OAAOE,MAAM,CAACC,OAAO,KAAK,UAAU,EACpC,oCAAoC,CACpC;EACDH,SAAS,CACR,OAAOE,MAAM,CAACE,SAAS,KAAK,UAAU,EACtC,sCAAsC,CACtC;EACDJ,SAAS,CACR,OAAOE,MAAM,CAACG,OAAO,KAAK,UAAU,EACpC,oCAAoC,CACpC;;AAGF,OAAO,SAASC,sBAAsB,CAACC,MAAkB,EAAQ;EAChEP,SAAS,CACR,OAAOO,MAAM,CAACC,OAAO,KAAK,UAAU,EACpC,oCAAoC,CACpC;EACDR,SAAS,CACR,OAAOO,MAAM,CAACE,KAAK,KAAK,UAAU,EAClC,kCAAkC,CAClC;EACDT,SAAS,CACR,OAAOO,MAAM,CAACG,IAAI,KAAK,UAAU,EACjC,sCAAsC,CACtC;;AAGF,OAAO,SAASC,YAAY,CAC3BC,IAA+B,EAC/BC,UAAoB,EACb;EACP,IAAIA,UAAU,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IACtCA,IAAI,CAACI,OAAO,CAAEC,CAAC,IAAKN,YAAY,CAACM,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3C;;EAGDjB,SAAS,CACR,OAAOY,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,QAAQ,EACpDC,UAAU,GACP,6DAA6D,GAC7D,wCAAwC,CAC3C", "names": ["invariant", "validateSourceContract", "source", "canDrag", "beginDrag", "endDrag", "validateTargetContract", "target", "canDrop", "hover", "drop", "validateType", "type", "allowArray", "Array", "isArray", "for<PERSON>ach", "t"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\contracts.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type { DragSource, DropTarget, Identifier } from './interfaces.js'\n\nexport function validateSourceContract(source: DragSource): void {\n\tinvariant(\n\t\ttypeof source.canDrag === 'function',\n\t\t'Expected canDrag to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof source.beginDrag === 'function',\n\t\t'Expected beginDrag to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof source.endDrag === 'function',\n\t\t'Expected endDrag to be a function.',\n\t)\n}\n\nexport function validateTargetContract(target: DropTarget): void {\n\tinvariant(\n\t\ttypeof target.canDrop === 'function',\n\t\t'Expected canDrop to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof target.hover === 'function',\n\t\t'Expected hover to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof target.drop === 'function',\n\t\t'Expected beginDrag to be a function.',\n\t)\n}\n\nexport function validateType(\n\ttype: Identifier | Identifier[],\n\tallowArray?: boolean,\n): void {\n\tif (allowArray && Array.isArray(type)) {\n\t\ttype.forEach((t) => validateType(t, false))\n\t\treturn\n\t}\n\n\tinvariant(\n\t\ttypeof type === 'string' || typeof type === 'symbol',\n\t\tallowArray\n\t\t\t? 'Type can only be a string, a symbol, or an array of either.'\n\t\t\t: 'Type can only be a string or a symbol.',\n\t)\n}\n"]}, "metadata": {}, "sourceType": "module"}