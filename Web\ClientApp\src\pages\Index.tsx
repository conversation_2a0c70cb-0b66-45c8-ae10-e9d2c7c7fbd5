import { routes } from 'app/routes';
import { Navigate } from 'react-router-dom';
import { useAuth } from 'features/auth/use-auth';
import { Loading } from 'features/loading/Loading';

export function Index() {
  const { user, isInRole } = useAuth(),
    isDriver = isInRole('driver');

  if (!user) {
    return <Loading />;
  }

  if (isDriver) {
    return <Navigate to={routes.driverTasks.list.path} />;
  }

  return <Navigate to={routes.orders.path} />;
}

export default Index;
