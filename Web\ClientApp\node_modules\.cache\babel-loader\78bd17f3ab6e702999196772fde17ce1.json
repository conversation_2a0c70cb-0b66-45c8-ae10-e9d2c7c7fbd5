{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    },\n    moveItem(state, action) {\n      const {\n        movingItem,\n        existingItem\n      } = action.payload;\n      console.log(\"moving item\", existingItem, movingItem);\n      if (existingItem.stickingSortOrder == null) {\n        existingItem.stickingSortOrder = 0;\n        movingItem.stickingSortOrder = 1;\n        //move the rest up by 1\n        state.plants = state.plants.map(p => {\n          if (p._id === existingItem._id) {\n            return existingItem;\n          } else if (p._id === movingItem._id) {\n            return movingItem;\n          } else if (p.stickingSortOrder != null) {\n            p.stickingSortOrder++;\n          }\n          return p;\n        });\n      } else {\n        // set the moved item to the existing item's order\n        movingItem.stickingSortOrder = existingItem.stickingSortOrder;\n        // move the existing item and all items with a higher order up by 1\n        state.plants = state.plants.map(p => {\n          var _existingItem$stickin;\n          if (p._id === existingItem._id) {\n            return movingItem;\n          } else if (p._id === movingItem._id) {\n            return existingItem;\n          } else if (p.stickingSortOrder != null && p.stickingSortOrder > ((_existingItem$stickin = existingItem.stickingSortOrder) !== null && _existingItem$stickin !== void 0 ? _existingItem$stickin : 0)) {\n            p.stickingSortOrder--;\n          }\n          return p;\n        });\n      }\n    }\n  }\n});\nexport const {\n  setPlants,\n  moveItem\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nconst sortByStickingSortOrder = sortBy('stickingSortOrder');\nfunction sortPlant(a, b) {\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "sortBy", "sortSizeName", "initialState", "plants", "plantsSlice", "name", "reducers", "setPlants", "state", "action", "payload", "moveItem", "movingItem", "existingItem", "console", "log", "stickingSortOrder", "map", "p", "_id", "actions", "selectPlants", "sort", "sortPlant", "reducer", "sortByCrop", "sortByStickingSortOrder", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    },\r\n    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {\r\n      const { movingItem, existingItem } = action.payload;\r\n\r\n      console.log(\"moving item\", existingItem, movingItem);\r\n\r\n      if (existingItem.stickingSortOrder == null) {\r\n        existingItem.stickingSortOrder = 0;\r\n        movingItem.stickingSortOrder = 1;\r\n        //move the rest up by 1\r\n        state.plants = state.plants.map(p => {\r\n          if (p._id === existingItem._id) {\r\n            return existingItem;\r\n          } else if (p._id === movingItem._id) {\r\n            return movingItem;\r\n          } else if (p.stickingSortOrder != null) {\r\n            p.stickingSortOrder++;\r\n          }\r\n          return p;\r\n        });\r\n      } else {\r\n        // set the moved item to the existing item's order\r\n        movingItem.stickingSortOrder = existingItem.stickingSortOrder;\r\n        // move the existing item and all items with a higher order up by 1\r\n        state.plants = state.plants.map(p => {\r\n          if (p._id === existingItem._id) {\r\n            return movingItem;\r\n          } else if (p._id === movingItem._id) {\r\n            return existingItem;\r\n          } else if (p.stickingSortOrder != null && p.stickingSortOrder > (existingItem.stickingSortOrder ?? 0)) {\r\n            p.stickingSortOrder--;\r\n          }\r\n          return p;\r\n        });\r\n      }\r\n    }\r\n  }\r\n});\r\n\r\nexport const { setPlants, moveItem } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\nconst sortByStickingSortOrder = sortBy('stickingSortOrder');\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAMjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGL,WAAW,CAAC;EACrCM,IAAI,EAAE,QAAQ;EACdH,YAAY;EACZI,QAAQ,EAAE;IACRC,SAAS,CAACC,KAAK,EAAEC,MAA8B,EAAE;MAC/CD,KAAK,CAACL,MAAM,GAAGM,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDC,QAAQ,CAACH,KAAK,EAAEC,MAAiE,EAAE;MACjF,MAAM;QAAEG,UAAU;QAAEC;MAAa,CAAC,GAAGJ,MAAM,CAACC,OAAO;MAEnDI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,YAAY,EAAED,UAAU,CAAC;MAEpD,IAAIC,YAAY,CAACG,iBAAiB,IAAI,IAAI,EAAE;QAC1CH,YAAY,CAACG,iBAAiB,GAAG,CAAC;QAClCJ,UAAU,CAACI,iBAAiB,GAAG,CAAC;QAChC;QACAR,KAAK,CAACL,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACc,GAAG,CAACC,CAAC,IAAI;UACnC,IAAIA,CAAC,CAACC,GAAG,KAAKN,YAAY,CAACM,GAAG,EAAE;YAC9B,OAAON,YAAY;UACrB,CAAC,MAAM,IAAIK,CAAC,CAACC,GAAG,KAAKP,UAAU,CAACO,GAAG,EAAE;YACnC,OAAOP,UAAU;UACnB,CAAC,MAAM,IAAIM,CAAC,CAACF,iBAAiB,IAAI,IAAI,EAAE;YACtCE,CAAC,CAACF,iBAAiB,EAAE;UACvB;UACA,OAAOE,CAAC;QACV,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAN,UAAU,CAACI,iBAAiB,GAAGH,YAAY,CAACG,iBAAiB;QAC7D;QACAR,KAAK,CAACL,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACc,GAAG,CAACC,CAAC,IAAI;UAAA;UACnC,IAAIA,CAAC,CAACC,GAAG,KAAKN,YAAY,CAACM,GAAG,EAAE;YAC9B,OAAOP,UAAU;UACnB,CAAC,MAAM,IAAIM,CAAC,CAACC,GAAG,KAAKP,UAAU,CAACO,GAAG,EAAE;YACnC,OAAON,YAAY;UACrB,CAAC,MAAM,IAAIK,CAAC,CAACF,iBAAiB,IAAI,IAAI,IAAIE,CAAC,CAACF,iBAAiB,6BAAIH,YAAY,CAACG,iBAAiB,yEAAI,CAAC,CAAC,EAAE;YACrGE,CAAC,CAACF,iBAAiB,EAAE;UACvB;UACA,OAAOE,CAAC;QACV,CAAC,CAAC;MACJ;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEX,SAAS;EAAEI;AAAS,CAAC,GAAGP,WAAW,CAACgB,OAAO;AAE1D,OAAO,MAAMC,YAAY,GAAIb,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAACA,MAAM,CAACc,GAAG,CAACC,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,SAAS,CAAC;AAExG,eAAenB,WAAW,CAACoB,OAAO;AAElC,MAAMC,UAAU,GAAGzB,MAAM,CAAC,MAAM,CAAC;AACjC,MAAM0B,uBAAuB,GAAG1B,MAAM,CAAC,mBAAmB,CAAC;AAE3D,SAASuB,SAAS,CAACI,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,uBAAuB,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIH,UAAU,CAACE,CAAC,EAAEC,CAAC,CAAC,IAAI3B,YAAY,CAAC0B,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AAC1F"}, "metadata": {}, "sourceType": "module"}