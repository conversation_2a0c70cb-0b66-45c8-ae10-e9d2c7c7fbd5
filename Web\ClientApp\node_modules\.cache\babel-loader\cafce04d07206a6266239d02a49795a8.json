{"ast": null, "code": "function _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { createDragDropManager } from 'dnd-core';\nimport { memo, useEffect } from 'react';\nimport { DndContext } from './DndContext.js';\nlet refCount = 0;\nconst INSTANCE_SYM = Symbol.for('__REACT_DND_CONTEXT_INSTANCE__');\nvar DndProvider = /*#__PURE__*/memo(function DndProvider(_param) {\n  var {\n      children\n    } = _param,\n    props = _objectWithoutProperties(_param, [\"children\"]);\n  const [manager, isGlobalInstance] = getDndContextValue(props) // memoized from props\n  ;\n  /**\n  * If the global context was used to store the DND context\n  * then where theres no more references to it we should\n  * clean it up to avoid memory leaks\n  */\n  useEffect(() => {\n    if (isGlobalInstance) {\n      const context = getGlobalContext();\n      ++refCount;\n      return () => {\n        if (--refCount === 0) {\n          context[INSTANCE_SYM] = null;\n        }\n      };\n    }\n    return;\n  }, []);\n  return /*#__PURE__*/_jsx(DndContext.Provider, {\n    value: manager,\n    children: children\n  });\n});\n/**\n * A React component that provides the React-DnD context\n */\nexport { DndProvider };\nfunction getDndContextValue(props) {\n  if ('manager' in props) {\n    const manager = {\n      dragDropManager: props.manager\n    };\n    return [manager, false];\n  }\n  const manager = createSingletonDndContext(props.backend, props.context, props.options, props.debugMode);\n  const isGlobalInstance = !props.context;\n  return [manager, isGlobalInstance];\n}\nfunction createSingletonDndContext(backend) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getGlobalContext();\n  let options = arguments.length > 2 ? arguments[2] : undefined;\n  let debugMode = arguments.length > 3 ? arguments[3] : undefined;\n  const ctx = context;\n  if (!ctx[INSTANCE_SYM]) {\n    ctx[INSTANCE_SYM] = {\n      dragDropManager: createDragDropManager(backend, context, options, debugMode)\n    };\n  }\n  return ctx[INSTANCE_SYM];\n}\nfunction getGlobalContext() {\n  return typeof global !== 'undefined' ? global : window;\n}", "map": {"version": 3, "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAASA,qBAAqB,QAAQ,UAAU;AAEhD,SAASC,IAAI,EAAEC,SAAS,QAAQ,OAAO;AAEvC,SAASC,UAAU,QAAQ,iBAAiB;AAe5C,IAAIC,QAAQ,GAAG,CAAC;AAChB,MAAMC,YAAY,GAAGC,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAKpDC,WAAW,gBAA2CP,IAAI,CACtE,SAASO,WAAW,CAACC,MAAsB,EAAE;MAAxB;MAAEC;IAAQ,CAAY,GAAtBD,MAAsB;IAAPE,KAAK,4BAApBF,MAAsB,GAApBC,UAAQ;EAC9B,MAAM,CAACE,OAAO,EAAEC,gBAAgB,CAAC,GAAGC,kBAAkB,CAACH,KAAK,CAAC,CAAC;EAAA;EAC9D;;;;;EAKAT,SAAS,CAAC,MAAM;IACf,IAAIW,gBAAgB,EAAE;MACrB,MAAME,OAAO,GAAGC,gBAAgB,EAAE;MAClC,EAAEZ,QAAQ;MAEV,OAAO,MAAM;QACZ,IAAI,EAAEA,QAAQ,KAAK,CAAC,EAAE;UACrBW,OAAO,CAACV,YAAY,CAAC,GAAG,IAAI;;OAE7B;;IAEF;GACA,EAAE,EAAE,CAAC;EAEN,oBAAOY,KAACd,UAAU,CAACe,QAAQ;IAACC,KAAK,EAAEP,OAAO;cAAGF;IAA+B;CAC5E,CACD;AA3BD;;;AAGA;AA0BA,SAASI,kBAAkB,CAACH,KAAyC,EAAE;EACtE,IAAI,SAAS,IAAIA,KAAK,EAAE;IACvB,MAAMC,OAAO,GAAG;MAAEQ,eAAe,EAAET,KAAK,CAACC;KAAS;IAClD,OAAO,CAACA,OAAO,EAAE,KAAK,CAAC;;EAGxB,MAAMA,OAAO,GAAGS,yBAAyB,CACxCV,KAAK,CAACW,OAAO,EACbX,KAAK,CAACI,OAAO,EACbJ,KAAK,CAACY,OAAO,EACbZ,KAAK,CAACa,SAAS,CACf;EACD,MAAMX,gBAAgB,GAAG,CAACF,KAAK,CAACI,OAAO;EAEvC,OAAO,CAACH,OAAO,EAAEC,gBAAgB,CAAC;;AAGnC,SAASQ,yBAAyB,CACjCC,OAAuB,EAItB;EAAA,IAHDP,OAAuB,uEAAGC,gBAAgB,EAAE;EAAA,IAC5CO,OAAuB;EAAA,IACvBC,SAAmB;EAEnB,MAAMC,GAAG,GAAGV,OAAO;EACnB,IAAI,CAACU,GAAG,CAACpB,YAAY,CAAC,EAAE;IACvBoB,GAAG,CAACpB,YAAY,CAAC,GAAG;MACnBe,eAAe,EAAEpB,qBAAqB,CACrCsB,OAAO,EACPP,OAAO,EACPQ,OAAO,EACPC,SAAS;KAEV;;EAEF,OAAOC,GAAG,CAACpB,YAAY,CAAC;;AAIzB,SAASW,gBAAgB,GAAG;EAC3B,OAAO,OAAOU,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAIC,MAAM", "names": ["createDragDropManager", "memo", "useEffect", "DndContext", "refCount", "INSTANCE_SYM", "Symbol", "for", "DndProvider", "_param", "children", "props", "manager", "isGlobalInstance", "getDndContextValue", "context", "getGlobalContext", "_jsx", "Provider", "value", "dragDropManager", "createSingletonDndContext", "backend", "options", "debugMode", "ctx", "global", "window"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\core\\DndProvider.tsx"], "sourcesContent": ["import type { BackendFactory, DragDropManager } from 'dnd-core'\nimport { createDragDropManager } from 'dnd-core'\nimport type { FC, ReactNode } from 'react'\nimport { memo, useEffect } from 'react'\n\nimport { DndContext } from './DndContext.js'\n\nexport type DndProviderProps<BackendContext, BackendOptions> =\n\t| {\n\t\t\tchildren?: ReactNode\n\t\t\tmanager: DragDropManager\n\t  }\n\t| {\n\t\t\tbackend: BackendFactory\n\t\t\tchildren?: ReactNode\n\t\t\tcontext?: BackendContext\n\t\t\toptions?: BackendOptions\n\t\t\tdebugMode?: boolean\n\t  }\n\nlet refCount = 0\nconst INSTANCE_SYM = Symbol.for('__REACT_DND_CONTEXT_INSTANCE__')\n\n/**\n * A React component that provides the React-DnD context\n */\nexport const DndProvider: FC<DndProviderProps<unknown, unknown>> = memo(\n\tfunction DndProvider({ children, ...props }) {\n\t\tconst [manager, isGlobalInstance] = getDndContextValue(props) // memoized from props\n\t\t/**\n\t\t * If the global context was used to store the DND context\n\t\t * then where theres no more references to it we should\n\t\t * clean it up to avoid memory leaks\n\t\t */\n\t\tuseEffect(() => {\n\t\t\tif (isGlobalInstance) {\n\t\t\t\tconst context = getGlobalContext()\n\t\t\t\t++refCount\n\n\t\t\t\treturn () => {\n\t\t\t\t\tif (--refCount === 0) {\n\t\t\t\t\t\tcontext[INSTANCE_SYM] = null\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn\n\t\t}, [])\n\n\t\treturn <DndContext.Provider value={manager}>{children}</DndContext.Provider>\n\t},\n)\n\nfunction getDndContextValue(props: DndProviderProps<unknown, unknown>) {\n\tif ('manager' in props) {\n\t\tconst manager = { dragDropManager: props.manager }\n\t\treturn [manager, false]\n\t}\n\n\tconst manager = createSingletonDndContext(\n\t\tprops.backend,\n\t\tprops.context,\n\t\tprops.options,\n\t\tprops.debugMode,\n\t)\n\tconst isGlobalInstance = !props.context\n\n\treturn [manager, isGlobalInstance]\n}\n\nfunction createSingletonDndContext<BackendContext, BackendOptions>(\n\tbackend: BackendFactory,\n\tcontext: BackendContext = getGlobalContext(),\n\toptions: BackendOptions,\n\tdebugMode?: boolean,\n) {\n\tconst ctx = context as any\n\tif (!ctx[INSTANCE_SYM]) {\n\t\tctx[INSTANCE_SYM] = {\n\t\t\tdragDropManager: createDragDropManager(\n\t\t\t\tbackend,\n\t\t\t\tcontext,\n\t\t\t\toptions,\n\t\t\t\tdebugMode,\n\t\t\t),\n\t\t}\n\t}\n\treturn ctx[INSTANCE_SYM]\n}\n\ndeclare const global: any\nfunction getGlobalContext() {\n\treturn typeof global !== 'undefined' ? global : (window as any)\n}\n"]}, "metadata": {}, "sourceType": "module"}