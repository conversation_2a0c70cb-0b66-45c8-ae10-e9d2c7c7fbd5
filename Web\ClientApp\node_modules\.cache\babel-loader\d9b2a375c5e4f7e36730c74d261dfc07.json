{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    },\n    moveItem(state, action) {\n      const {\n        movingItem,\n        existingItem\n      } = action.payload;\n      console.log(\"moving item\", existingItem, movingItem);\n      if (existingItem.stickingSortOrder == null) {\n        existingItem.stickingSortOrder = 0;\n        movingItem.stickingSortOrder = 1;\n      } else {\n        const existingIndex = state.plants.findIndex(p => p._id === existingItem._id),\n          movingIndex = state.plants.findIndex(p => p._id === movingItem._id),\n          plants = state.plants.map(p => ({\n            ...p\n          }));\n        plants.splice(existingIndex, 1);\n        plants.splice(movingIndex, 0, existingItem);\n        state.plants = plants;\n      }\n    }\n  }\n});\nexport const {\n  setPlants,\n  moveItem\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nconst sortByStickingSortOrder = sortBy('stickingSortOrder');\nfunction sortPlant(a, b) {\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "sortBy", "sortSizeName", "initialState", "plants", "plantsSlice", "name", "reducers", "setPlants", "state", "action", "payload", "moveItem", "movingItem", "existingItem", "console", "log", "stickingSortOrder", "existingIndex", "findIndex", "p", "_id", "movingIndex", "map", "splice", "actions", "selectPlants", "sort", "sortPlant", "reducer", "sortByCrop", "sortByStickingSortOrder", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    },\r\n    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {\r\n      const { movingItem, existingItem } = action.payload;\r\n\r\n      console.log(\"moving item\", existingItem, movingItem);\r\n\r\n      if (existingItem.stickingSortOrder == null) {\r\n        existingItem.stickingSortOrder = 0;\r\n        movingItem.stickingSortOrder = 1;\r\n      } else {\r\n        const existingIndex = state.plants.findIndex(p => p._id === existingItem._id),\r\n          movingIndex = state.plants.findIndex(p => p._id === movingItem._id),\r\n          plants = state.plants.map(p => ({...p}));\r\n        plants.splice(existingIndex, 1);\r\n        plants.splice(movingIndex, 0, existingItem);\r\n        state.plants = plants;\r\n      }\r\n    }\r\n  }\r\n});\r\n\r\nexport const { setPlants, moveItem } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\nconst sortByStickingSortOrder = sortBy('stickingSortOrder');\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAMjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGL,WAAW,CAAC;EACrCM,IAAI,EAAE,QAAQ;EACdH,YAAY;EACZI,QAAQ,EAAE;IACRC,SAAS,CAACC,KAAK,EAAEC,MAA8B,EAAE;MAC/CD,KAAK,CAACL,MAAM,GAAGM,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDC,QAAQ,CAACH,KAAK,EAAEC,MAAiE,EAAE;MACjF,MAAM;QAAEG,UAAU;QAAEC;MAAa,CAAC,GAAGJ,MAAM,CAACC,OAAO;MAEnDI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,YAAY,EAAED,UAAU,CAAC;MAEpD,IAAIC,YAAY,CAACG,iBAAiB,IAAI,IAAI,EAAE;QAC1CH,YAAY,CAACG,iBAAiB,GAAG,CAAC;QAClCJ,UAAU,CAACI,iBAAiB,GAAG,CAAC;MAClC,CAAC,MAAM;QACL,MAAMC,aAAa,GAAGT,KAAK,CAACL,MAAM,CAACe,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKP,YAAY,CAACO,GAAG,CAAC;UAC3EC,WAAW,GAAGb,KAAK,CAACL,MAAM,CAACe,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKR,UAAU,CAACQ,GAAG,CAAC;UACnEjB,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACmB,GAAG,CAACH,CAAC,KAAK;YAAC,GAAGA;UAAC,CAAC,CAAC,CAAC;QAC1ChB,MAAM,CAACoB,MAAM,CAACN,aAAa,EAAE,CAAC,CAAC;QAC/Bd,MAAM,CAACoB,MAAM,CAACF,WAAW,EAAE,CAAC,EAAER,YAAY,CAAC;QAC3CL,KAAK,CAACL,MAAM,GAAGA,MAAM;MACvB;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEI,SAAS;EAAEI;AAAS,CAAC,GAAGP,WAAW,CAACoB,OAAO;AAE1D,OAAO,MAAMC,YAAY,GAAIjB,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAACA,MAAM,CAACmB,GAAG,CAACH,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACO,IAAI,CAACC,SAAS,CAAC;AAExG,eAAevB,WAAW,CAACwB,OAAO;AAElC,MAAMC,UAAU,GAAG7B,MAAM,CAAC,MAAM,CAAC;AACjC,MAAM8B,uBAAuB,GAAG9B,MAAM,CAAC,mBAAmB,CAAC;AAE3D,SAAS2B,SAAS,CAACI,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,uBAAuB,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIH,UAAU,CAACE,CAAC,EAAEC,CAAC,CAAC,IAAI/B,YAAY,CAAC8B,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AAC1F"}, "metadata": {}, "sourceType": "module"}