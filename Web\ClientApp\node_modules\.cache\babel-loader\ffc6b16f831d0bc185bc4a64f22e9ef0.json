{"ast": null, "code": "import { asap } from '@react-dnd/asap';\nimport { invariant } from '@react-dnd/invariant';\nimport { addSource, addTarget, removeSource, removeTarget } from '../actions/registry.js';\nimport { validateSourceContract, validateTargetContract, validateType } from '../contracts.js';\nimport { HandlerRole } from '../interfaces.js';\nimport { getNextUniqueId } from '../utils/getNextUniqueId.js';\nfunction getNextHandlerId(role) {\n  const id = getNextUniqueId().toString();\n  switch (role) {\n    case HandlerRole.SOURCE:\n      return `S${id}`;\n    case HandlerRole.TARGET:\n      return `T${id}`;\n    default:\n      throw new Error(`Unknown Handler Role: ${role}`);\n  }\n}\nfunction parseRoleFromHandlerId(handlerId) {\n  switch (handlerId[0]) {\n    case 'S':\n      return HandlerRole.SOURCE;\n    case 'T':\n      return HandlerRole.TARGET;\n    default:\n      throw new Error(`Cannot parse handler ID: ${handlerId}`);\n  }\n}\nfunction mapContainsValue(map, searchValue) {\n  const entries = map.entries();\n  let isDone = false;\n  do {\n    const {\n      done,\n      value: [, value]\n    } = entries.next();\n    if (value === searchValue) {\n      return true;\n    }\n    isDone = !!done;\n  } while (!isDone);\n  return false;\n}\nexport class HandlerRegistryImpl {\n  addSource(type, source) {\n    validateType(type);\n    validateSourceContract(source);\n    const sourceId = this.addHandler(HandlerRole.SOURCE, type, source);\n    this.store.dispatch(addSource(sourceId));\n    return sourceId;\n  }\n  addTarget(type, target) {\n    validateType(type, true);\n    validateTargetContract(target);\n    const targetId = this.addHandler(HandlerRole.TARGET, type, target);\n    this.store.dispatch(addTarget(targetId));\n    return targetId;\n  }\n  containsHandler(handler) {\n    return mapContainsValue(this.dragSources, handler) || mapContainsValue(this.dropTargets, handler);\n  }\n  getSource(sourceId) {\n    let includePinned = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    invariant(this.isSourceId(sourceId), 'Expected a valid source ID.');\n    const isPinned = includePinned && sourceId === this.pinnedSourceId;\n    const source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId);\n    return source;\n  }\n  getTarget(targetId) {\n    invariant(this.isTargetId(targetId), 'Expected a valid target ID.');\n    return this.dropTargets.get(targetId);\n  }\n  getSourceType(sourceId) {\n    invariant(this.isSourceId(sourceId), 'Expected a valid source ID.');\n    return this.types.get(sourceId);\n  }\n  getTargetType(targetId) {\n    invariant(this.isTargetId(targetId), 'Expected a valid target ID.');\n    return this.types.get(targetId);\n  }\n  isSourceId(handlerId) {\n    const role = parseRoleFromHandlerId(handlerId);\n    return role === HandlerRole.SOURCE;\n  }\n  isTargetId(handlerId) {\n    const role = parseRoleFromHandlerId(handlerId);\n    return role === HandlerRole.TARGET;\n  }\n  removeSource(sourceId) {\n    invariant(this.getSource(sourceId), 'Expected an existing source.');\n    this.store.dispatch(removeSource(sourceId));\n    asap(() => {\n      this.dragSources.delete(sourceId);\n      this.types.delete(sourceId);\n    });\n  }\n  removeTarget(targetId) {\n    invariant(this.getTarget(targetId), 'Expected an existing target.');\n    this.store.dispatch(removeTarget(targetId));\n    this.dropTargets.delete(targetId);\n    this.types.delete(targetId);\n  }\n  pinSource(sourceId) {\n    const source = this.getSource(sourceId);\n    invariant(source, 'Expected an existing source.');\n    this.pinnedSourceId = sourceId;\n    this.pinnedSource = source;\n  }\n  unpinSource() {\n    invariant(this.pinnedSource, 'No source is pinned at the time.');\n    this.pinnedSourceId = null;\n    this.pinnedSource = null;\n  }\n  addHandler(role, type, handler) {\n    const id = getNextHandlerId(role);\n    this.types.set(id, type);\n    if (role === HandlerRole.SOURCE) {\n      this.dragSources.set(id, handler);\n    } else if (role === HandlerRole.TARGET) {\n      this.dropTargets.set(id, handler);\n    }\n    return id;\n  }\n  constructor(store) {\n    this.types = new Map();\n    this.dragSources = new Map();\n    this.dropTargets = new Map();\n    this.pinnedSourceId = null;\n    this.pinnedSource = null;\n    this.store = store;\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,SAASC,SAAS,QAAQ,sBAAsB;AAGhD,SACCC,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,YAAY,QACN,wBAAwB;AAC/B,SACCC,sBAAsB,EACtBC,sBAAsB,EACtBC,YAAY,QACN,iBAAiB;AASxB,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,SAASC,eAAe,QAAQ,6BAA6B;AAE7D,SAASC,gBAAgB,CAACC,IAAiB,EAAU;EACpD,MAAMC,EAAE,GAAGH,eAAe,EAAE,CAACI,QAAQ,EAAE;EACvC,QAAQF,IAAI;IACX,KAAKH,WAAW,CAACM,MAAM;MACtB,OAAQ,IAAGF,EAAG,EAAC;IAChB,KAAKJ,WAAW,CAACO,MAAM;MACtB,OAAQ,IAAGH,EAAG,EAAC;IAChB;MACC,MAAM,IAAII,KAAK,CAAE,yBAAwBL,IAAK,EAAC,CAAC;EAAA;;AAInD,SAASM,sBAAsB,CAACC,SAAiB,EAAE;EAClD,QAAQA,SAAS,CAAC,CAAC,CAAC;IACnB,KAAK,GAAG;MACP,OAAOV,WAAW,CAACM,MAAM;IAC1B,KAAK,GAAG;MACP,OAAON,WAAW,CAACO,MAAM;IAC1B;MACC,MAAM,IAAIC,KAAK,CAAE,4BAA2BE,SAAU,EAAC,CAAC;EAAA;;AAI3D,SAASC,gBAAgB,CAAIC,GAAmB,EAAEC,WAAc,EAAE;EACjE,MAAMC,OAAO,GAAGF,GAAG,CAACE,OAAO,EAAE;EAC7B,IAAIC,MAAM,GAAG,KAAK;EAClB,GAAG;IACF,MAAM;MACLC,IAAI;MACJC,KAAK,EAAE,GAAGA,KAAK;IAAC,CAChB,GAAGH,OAAO,CAACI,IAAI,EAAE;IAClB,IAAID,KAAK,KAAKJ,WAAW,EAAE;MAC1B,OAAO,IAAI;;IAEZE,MAAM,GAAG,CAAC,CAACC,IAAI;GACf,QAAQ,CAACD,MAAM;EAChB,OAAO,KAAK;;AAGb,OAAO,MAAMI,mBAAmB;EAY/B1B,SAAgB,CAAC2B,IAAgB,EAAEC,MAAkB,EAAU;IAC9DtB,YAAY,CAACqB,IAAI,CAAC;IAClBvB,sBAAsB,CAACwB,MAAM,CAAC;IAE9B,MAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACvB,WAAW,CAACM,MAAM,EAAEc,IAAI,EAAEC,MAAM,CAAC;IAClE,IAAI,CAACG,KAAK,CAACC,QAAQ,CAAChC,SAAS,CAAC6B,QAAQ,CAAC,CAAC;IACxC,OAAOA,QAAQ;;EAGhB5B,SAAgB,CAAC0B,IAAgB,EAAEM,MAAkB,EAAU;IAC9D3B,YAAY,CAACqB,IAAI,EAAE,IAAI,CAAC;IACxBtB,sBAAsB,CAAC4B,MAAM,CAAC;IAE9B,MAAMC,QAAQ,GAAG,IAAI,CAACJ,UAAU,CAACvB,WAAW,CAACO,MAAM,EAAEa,IAAI,EAAEM,MAAM,CAAC;IAClE,IAAI,CAACF,KAAK,CAACC,QAAQ,CAAC/B,SAAS,CAACiC,QAAQ,CAAC,CAAC;IACxC,OAAOA,QAAQ;;EAGhBC,eAAsB,CAACC,OAAgC,EAAW;IACjE,OACClB,gBAAgB,CAAC,IAAI,CAACmB,WAAW,EAAED,OAAO,CAAC,IAC3ClB,gBAAgB,CAAC,IAAI,CAACoB,WAAW,EAAEF,OAAO,CAAC;;EAI7CG,SAAgB,CAACV,QAAgB,EAAqC;IAAA,IAAnCW,aAAa,uEAAG,KAAK;IACvDzC,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAACZ,QAAQ,CAAC,EAAE,6BAA6B,CAAC;IACnE,MAAMa,QAAQ,GAAGF,aAAa,IAAIX,QAAQ,KAAK,IAAI,CAACc,cAAc;IAClE,MAAMf,MAAM,GAAGc,QAAQ,GAAG,IAAI,CAACE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAChB,QAAQ,CAAC;IAC5E,OAAOD,MAAM;;EAGdkB,SAAgB,CAACZ,QAAgB,EAAc;IAC9CnC,SAAS,CAAC,IAAI,CAACgD,UAAU,CAACb,QAAQ,CAAC,EAAE,6BAA6B,CAAC;IACnE,OAAO,IAAI,CAACI,WAAW,CAACO,GAAG,CAACX,QAAQ,CAAC;;EAGtCc,aAAoB,CAACnB,QAAgB,EAAc;IAClD9B,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAACZ,QAAQ,CAAC,EAAE,6BAA6B,CAAC;IACnE,OAAO,IAAI,CAACoB,KAAK,CAACJ,GAAG,CAAChB,QAAQ,CAAC;;EAGhCqB,aAAoB,CAAChB,QAAgB,EAA6B;IACjEnC,SAAS,CAAC,IAAI,CAACgD,UAAU,CAACb,QAAQ,CAAC,EAAE,6BAA6B,CAAC;IACnE,OAAO,IAAI,CAACe,KAAK,CAACJ,GAAG,CAACX,QAAQ,CAAC;;EAGhCO,UAAiB,CAACxB,SAAiB,EAAW;IAC7C,MAAMP,IAAI,GAAGM,sBAAsB,CAACC,SAAS,CAAC;IAC9C,OAAOP,IAAI,KAAKH,WAAW,CAACM,MAAM;;EAGnCkC,UAAiB,CAAC9B,SAAiB,EAAW;IAC7C,MAAMP,IAAI,GAAGM,sBAAsB,CAACC,SAAS,CAAC;IAC9C,OAAOP,IAAI,KAAKH,WAAW,CAACO,MAAM;;EAGnCZ,YAAmB,CAAC2B,QAAgB,EAAQ;IAC3C9B,SAAS,CAAC,IAAI,CAACwC,SAAS,CAACV,QAAQ,CAAC,EAAE,8BAA8B,CAAC;IACnE,IAAI,CAACE,KAAK,CAACC,QAAQ,CAAC9B,YAAY,CAAC2B,QAAQ,CAAC,CAAC;IAC3C/B,IAAI,CAAC,MAAM;MACV,IAAI,CAACuC,WAAW,CAACc,MAAM,CAACtB,QAAQ,CAAC;MACjC,IAAI,CAACoB,KAAK,CAACE,MAAM,CAACtB,QAAQ,CAAC;KAC3B,CAAC;;EAGH1B,YAAmB,CAAC+B,QAAgB,EAAQ;IAC3CnC,SAAS,CAAC,IAAI,CAAC+C,SAAS,CAACZ,QAAQ,CAAC,EAAE,8BAA8B,CAAC;IACnE,IAAI,CAACH,KAAK,CAACC,QAAQ,CAAC7B,YAAY,CAAC+B,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACI,WAAW,CAACa,MAAM,CAACjB,QAAQ,CAAC;IACjC,IAAI,CAACe,KAAK,CAACE,MAAM,CAACjB,QAAQ,CAAC;;EAG5BkB,SAAgB,CAACvB,QAAgB,EAAQ;IACxC,MAAMD,MAAM,GAAG,IAAI,CAACW,SAAS,CAACV,QAAQ,CAAC;IACvC9B,SAAS,CAAC6B,MAAM,EAAE,8BAA8B,CAAC;IAEjD,IAAI,CAACe,cAAc,GAAGd,QAAQ;IAC9B,IAAI,CAACe,YAAY,GAAGhB,MAAM;;EAG3ByB,WAAkB,GAAS;IAC1BtD,SAAS,CAAC,IAAI,CAAC6C,YAAY,EAAE,kCAAkC,CAAC;IAEhE,IAAI,CAACD,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAI;;EAGzBd,UAAkB,CACjBpB,IAAiB,EACjBiB,IAA6B,EAC7BS,OAAgC,EACvB;IACT,MAAMzB,EAAE,GAAGF,gBAAgB,CAACC,IAAI,CAAC;IACjC,IAAI,CAACuC,KAAK,CAACK,GAAG,CAAC3C,EAAE,EAAEgB,IAAI,CAAC;IACxB,IAAIjB,IAAI,KAAKH,WAAW,CAACM,MAAM,EAAE;MAChC,IAAI,CAACwB,WAAW,CAACiB,GAAG,CAAC3C,EAAE,EAAEyB,OAAO,CAAe;KAC/C,MAAM,IAAI1B,IAAI,KAAKH,WAAW,CAACO,MAAM,EAAE;MACvC,IAAI,CAACwB,WAAW,CAACgB,GAAG,CAAC3C,EAAE,EAAEyB,OAAO,CAAe;;IAEhD,OAAOzB,EAAE;;EAxGV4C,YAAmBxB,KAAmB,EAAE;IAPxC,KAAQkB,KAAK,GAAyC,IAAIO,GAAG,EAAE;IAC/D,KAAQnB,WAAW,GAA4B,IAAImB,GAAG,EAAE;IACxD,KAAQlB,WAAW,GAA4B,IAAIkB,GAAG,EAAE;IACxD,KAAQb,cAAc,GAAkB,IAAI;IAC5C,KAAQC,YAAY,GAAQ,IAAI;IAI/B,IAAI,CAACb,KAAK,GAAGA,KAAK", "names": ["asap", "invariant", "addSource", "addTarget", "removeSource", "remove<PERSON>arget", "validateSourceContract", "validateTargetContract", "validateType", "HandlerRole", "getNextUniqueId", "getNextHandlerId", "role", "id", "toString", "SOURCE", "TARGET", "Error", "parseRoleFromHandlerId", "handlerId", "mapContainsValue", "map", "searchValue", "entries", "isDone", "done", "value", "next", "HandlerRegistryImpl", "type", "source", "sourceId", "add<PERSON><PERSON><PERSON>", "store", "dispatch", "target", "targetId", "<PERSON><PERSON><PERSON><PERSON>", "handler", "dragSources", "dropTargets", "getSource", "include<PERSON><PERSON>ed", "isSourceId", "isPinned", "pinnedSourceId", "pinnedSource", "get", "get<PERSON><PERSON><PERSON>", "isTargetId", "getSourceType", "types", "getTargetType", "delete", "pinSource", "unpinSource", "set", "constructor", "Map"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\classes\\HandlerRegistryImpl.ts"], "sourcesContent": ["import { asap } from '@react-dnd/asap'\nimport { invariant } from '@react-dnd/invariant'\nimport type { Store } from 'redux'\n\nimport {\n\taddSource,\n\taddTarget,\n\tremoveSource,\n\tremoveTarget,\n} from '../actions/registry.js'\nimport {\n\tvalidateSourceContract,\n\tvalidateTargetContract,\n\tvalidateType,\n} from '../contracts.js'\nimport type {\n\tDragSource,\n\tDropTarget,\n\tHandlerRegistry,\n\tIdentifier,\n\tSourceType,\n\tTargetType,\n} from '../interfaces.js'\nimport { HandlerRole } from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport { getNextUniqueId } from '../utils/getNextUniqueId.js'\n\nfunction getNextHandlerId(role: HandlerRole): string {\n\tconst id = getNextUniqueId().toString()\n\tswitch (role) {\n\t\tcase HandlerRole.SOURCE:\n\t\t\treturn `S${id}`\n\t\tcase HandlerRole.TARGET:\n\t\t\treturn `T${id}`\n\t\tdefault:\n\t\t\tthrow new Error(`Unknown Handler Role: ${role}`)\n\t}\n}\n\nfunction parseRoleFromHandlerId(handlerId: string) {\n\tswitch (handlerId[0]) {\n\t\tcase 'S':\n\t\t\treturn HandlerRole.SOURCE\n\t\tcase 'T':\n\t\t\treturn HandlerRole.TARGET\n\t\tdefault:\n\t\t\tthrow new Error(`Cannot parse handler ID: ${handlerId}`)\n\t}\n}\n\nfunction mapContainsValue<T>(map: Map<string, T>, searchValue: T) {\n\tconst entries = map.entries()\n\tlet isDone = false\n\tdo {\n\t\tconst {\n\t\t\tdone,\n\t\t\tvalue: [, value],\n\t\t} = entries.next()\n\t\tif (value === searchValue) {\n\t\t\treturn true\n\t\t}\n\t\tisDone = !!done\n\t} while (!isDone)\n\treturn false\n}\n\nexport class HandlerRegistryImpl implements HandlerRegistry {\n\tprivate types: Map<string, SourceType | TargetType> = new Map()\n\tprivate dragSources: Map<string, DragSource> = new Map()\n\tprivate dropTargets: Map<string, DropTarget> = new Map()\n\tprivate pinnedSourceId: string | null = null\n\tprivate pinnedSource: any = null\n\tprivate store: Store<State>\n\n\tpublic constructor(store: Store<State>) {\n\t\tthis.store = store\n\t}\n\n\tpublic addSource(type: SourceType, source: DragSource): string {\n\t\tvalidateType(type)\n\t\tvalidateSourceContract(source)\n\n\t\tconst sourceId = this.addHandler(HandlerRole.SOURCE, type, source)\n\t\tthis.store.dispatch(addSource(sourceId))\n\t\treturn sourceId\n\t}\n\n\tpublic addTarget(type: TargetType, target: DropTarget): string {\n\t\tvalidateType(type, true)\n\t\tvalidateTargetContract(target)\n\n\t\tconst targetId = this.addHandler(HandlerRole.TARGET, type, target)\n\t\tthis.store.dispatch(addTarget(targetId))\n\t\treturn targetId\n\t}\n\n\tpublic containsHandler(handler: DragSource | DropTarget): boolean {\n\t\treturn (\n\t\t\tmapContainsValue(this.dragSources, handler) ||\n\t\t\tmapContainsValue(this.dropTargets, handler)\n\t\t)\n\t}\n\n\tpublic getSource(sourceId: string, includePinned = false): DragSource {\n\t\tinvariant(this.isSourceId(sourceId), 'Expected a valid source ID.')\n\t\tconst isPinned = includePinned && sourceId === this.pinnedSourceId\n\t\tconst source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId)\n\t\treturn source\n\t}\n\n\tpublic getTarget(targetId: string): DropTarget {\n\t\tinvariant(this.isTargetId(targetId), 'Expected a valid target ID.')\n\t\treturn this.dropTargets.get(targetId) as DropTarget\n\t}\n\n\tpublic getSourceType(sourceId: string): Identifier {\n\t\tinvariant(this.isSourceId(sourceId), 'Expected a valid source ID.')\n\t\treturn this.types.get(sourceId) as Identifier\n\t}\n\n\tpublic getTargetType(targetId: string): Identifier | Identifier[] {\n\t\tinvariant(this.isTargetId(targetId), 'Expected a valid target ID.')\n\t\treturn this.types.get(targetId) as Identifier | Identifier[]\n\t}\n\n\tpublic isSourceId(handlerId: string): boolean {\n\t\tconst role = parseRoleFromHandlerId(handlerId)\n\t\treturn role === HandlerRole.SOURCE\n\t}\n\n\tpublic isTargetId(handlerId: string): boolean {\n\t\tconst role = parseRoleFromHandlerId(handlerId)\n\t\treturn role === HandlerRole.TARGET\n\t}\n\n\tpublic removeSource(sourceId: string): void {\n\t\tinvariant(this.getSource(sourceId), 'Expected an existing source.')\n\t\tthis.store.dispatch(removeSource(sourceId))\n\t\tasap(() => {\n\t\t\tthis.dragSources.delete(sourceId)\n\t\t\tthis.types.delete(sourceId)\n\t\t})\n\t}\n\n\tpublic removeTarget(targetId: string): void {\n\t\tinvariant(this.getTarget(targetId), 'Expected an existing target.')\n\t\tthis.store.dispatch(removeTarget(targetId))\n\t\tthis.dropTargets.delete(targetId)\n\t\tthis.types.delete(targetId)\n\t}\n\n\tpublic pinSource(sourceId: string): void {\n\t\tconst source = this.getSource(sourceId)\n\t\tinvariant(source, 'Expected an existing source.')\n\n\t\tthis.pinnedSourceId = sourceId\n\t\tthis.pinnedSource = source\n\t}\n\n\tpublic unpinSource(): void {\n\t\tinvariant(this.pinnedSource, 'No source is pinned at the time.')\n\n\t\tthis.pinnedSourceId = null\n\t\tthis.pinnedSource = null\n\t}\n\n\tprivate addHandler(\n\t\trole: HandlerRole,\n\t\ttype: SourceType | TargetType,\n\t\thandler: DragSource | DropTarget,\n\t): string {\n\t\tconst id = getNextHandlerId(role)\n\t\tthis.types.set(id, type)\n\t\tif (role === HandlerRole.SOURCE) {\n\t\t\tthis.dragSources.set(id, handler as DragSource)\n\t\t} else if (role === HandlerRole.TARGET) {\n\t\t\tthis.dropTargets.set(id, handler as DropTarget)\n\t\t}\n\t\treturn id\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}