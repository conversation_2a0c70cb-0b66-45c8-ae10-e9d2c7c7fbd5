import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams, useNavigate, Navigate } from 'react-router';
import {
  Button,
  FormGroup,
  Input,
  Label,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { selectUsers } from './users-slice';
import * as models from 'api/models/auth';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { Link } from 'react-router-dom';
import { authApi } from 'api/auth-service';

class Permissions {
  constructor(private roles: string[]) {}

  can(role: models.Roles) {
    return this.roles.indexOf(role) !== -1;
  }

  update(role: models.Roles, include: boolean) {
    const roles = include
      ? this.roles.concat([role])
      : this.roles.filter((r) => r !== role);
    return new Permissions(roles);
  }

  getRoles() {
    return this.roles.map((r) => r);
  }
}

function Detail() {
  const navigate = useNavigate(),
    { isInRole, user: credentials } = useAuth(),
    { name } = useParams<{ name: string }>(),
    users = useSelector(selectUsers),
    [username, setUsername] = useState(''),
    [email, setEmail] = useState(''),
    [phone, setPhone] = useState(''),
    [showPasswordModal, setShowPasswordModal] = useState(false),
    [password, setPassword] = useState(''),
    user = users.find((u) => u.name === name),
    canUpdate = isInRole('admin:users'),
    [permissions, setPermissions] = useState(
      new Permissions(user?.roles || [])
    );

  useEffect(() => {
    if (user) {
      setUsername(user.name);
      setEmail(user.email || '');
      setPhone(user.phone || '');
    }
  }, [user]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPhone(e.target.value);
  };

  const handlePermissionChanged = (
    role: models.Roles,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setPermissions(permissions.update(role, e.target.checked));
  };

  const handleSaveClick = async () => {
    if (credentials && user) {
      const roles = permissions.getRoles(),
        model = { ...user, name: username, email, phone, roles },
        response = await authApi.updateUser(credentials, model);

      if (response) {
        navigate(routes.users.path);
      }
    }
  };

  const handleSetPasswordClick = () => {
    setShowPasswordModal(true);
  };

  const handleSetPasswordCancel = () => {
    setShowPasswordModal(false);
  };

  const handlePasswordModalOpened = () => {
    setPassword('');
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  const handleSetPasswordConfirm = async () => {
    if (credentials && user && password) {
      const model = { ...user },
        response = await authApi.updatePassword(credentials, model, password);

      if (response) {
        setShowPasswordModal(false);
        navigate(routes.users.path);
      }
    }
  };

  if (!user) {
    return <Navigate to={routes.users.path} />;
  }

  return (
    <div className="container d-grid gap-2">
      <div className="row">
        <div className="col">
          <Link to={routes.users.path}>
            <FontAwesomeIcon icon={['fat', 'chevron-left']} />
            &nbsp; Back to Zones List
          </Link>
        </div>
      </div>
      <h1>User {user.name}</h1>
      <div className="row">
        <div className="col-12 col-md-4">
          <label htmlFor="user-name">Name</label>
          <Input
            id="user-name"
            value={username}
            onChange={handleNameChange}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-4">
          <label htmlFor="user-email">Email</label>
          <Input
            id="user-email"
            type="email"
            value={email}
            onChange={handleEmailChange}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-4">
          <label htmlFor="user-phone">Phoone</label>
          <Input
            id="user-phone"
            value={phone}
            onChange={handlePhoneChange}
            disabled={!canUpdate}
          />
        </div>
      </div>
      <div className="row mt-4">
        <div className="col-12 col-md-4 py-2">
          <h2 className="border-bottom">
            <FontAwesomeIcon icon={['fat', 'seedling']} />
            &nbsp; Plants
          </h2>
          <FormGroup>
            <Input
              type="checkbox"
              id="view-plants"
              checked={permissions.can('view:plants')}
              onChange={(e) => handlePermissionChanged('view:plants', e)}
            />
            &nbsp;
            <Label check for="view-plants">
              View
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="create-plants"
              checked={permissions.can('create:plants')}
              onChange={(e) => handlePermissionChanged('create:plants', e)}
            />
            &nbsp;
            <Label check for="create-plants">
              Add
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="update-plants"
              checked={permissions.can('update:plants')}
              onChange={(e) => handlePermissionChanged('update:plants', e)}
            />
            &nbsp;
            <Label check for="update-plants">
              Edit
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="delete-plants"
              checked={permissions.can('delete:plants')}
              onChange={(e) => handlePermissionChanged('delete:plants', e)}
            />
            &nbsp;
            <Label check for="delete-plants">
              Delete
            </Label>
          </FormGroup>
        </div>
        <div className="col-12 col-md-4 py-2">
          <h2 className="border-bottom">
            <FontAwesomeIcon icon={['fat', 'user-tie']} />
            &nbsp; Customers
          </h2>
          <FormGroup>
            <Input
              type="checkbox"
              id="view-customers"
              checked={permissions.can('view:customers')}
              onChange={(e) => handlePermissionChanged('view:customers', e)}
            />
            &nbsp;
            <Label check for="view-customers">
              View
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="create-customers"
              checked={permissions.can('create:customers')}
              onChange={(e) => handlePermissionChanged('create:customers', e)}
            />
            &nbsp;
            <Label check for="create-customers">
              Add
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="update-customers"
              checked={permissions.can('update:customers')}
              onChange={(e) => handlePermissionChanged('update:customers', e)}
            />
            &nbsp;
            <Label check for="update-customers">
              Edit
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="delete-customers"
              checked={permissions.can('delete:customers')}
              onChange={(e) => handlePermissionChanged('delete:customers', e)}
            />
            &nbsp;
            <Label check for="delete-customers">
              Delete
            </Label>
          </FormGroup>
        </div>
        <div className="col-12 col-md-4 py-2">
          <h2 className="border-bottom">
            <FontAwesomeIcon icon={['fat', 'map-location-dot']} />
            &nbsp; Zones
          </h2>
          <FormGroup>
            <Input
              type="checkbox"
              id="view-zones"
              checked={permissions.can('view:zones')}
              onChange={(e) => handlePermissionChanged('view:zones', e)}
            />
            &nbsp;
            <Label check for="view-zones">
              View
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="create-zones"
              checked={permissions.can('create:zones')}
              onChange={(e) => handlePermissionChanged('create:zones', e)}
            />
            &nbsp;
            <Label check for="create-zones">
              Add
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="update-zones"
              checked={permissions.can('update:zones')}
              onChange={(e) => handlePermissionChanged('update:zones', e)}
            />
            &nbsp;
            <Label check for="update-zones">
              Edit
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="delete-zones"
              checked={permissions.can('delete:zones')}
              onChange={(e) => handlePermissionChanged('delete:zones', e)}
            />
            &nbsp;
            <Label check for="delete-zones">
              Delete
            </Label>
          </FormGroup>
        </div>
        <div className="col-12 col-md-4 py-2">
          <h2 className="border-bottom">
            <FontAwesomeIcon icon={['fat', 'file-invoice']} />
            &nbsp; Orders
          </h2>
          <FormGroup>
            <Input
              type="checkbox"
              id="view-orders"
              checked={permissions.can('view:orders')}
              onChange={(e) => handlePermissionChanged('view:orders', e)}
            />
            &nbsp;
            <Label check for="view-orders">
              View
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="create-orders"
              checked={permissions.can('create:orders')}
              onChange={(e) => handlePermissionChanged('create:orders', e)}
            />
            &nbsp;
            <Label check for="create-orders">
              Add
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="update-orders"
              checked={permissions.can('update:orders')}
              onChange={(e) => handlePermissionChanged('update:orders', e)}
            />
            &nbsp;
            <Label check for="update-orders">
              Edit
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="delete-orders"
              checked={permissions.can('delete:orders')}
              onChange={(e) => handlePermissionChanged('delete:orders', e)}
            />
            &nbsp;
            <Label check for="delete-orders">
              Delete
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="notify-orders"
              checked={permissions.can('notify:orders')}
              onChange={(e) => handlePermissionChanged('notify:orders', e)}
            />
            &nbsp;
            <Label check for="notify-orders">
              Notify on Change
            </Label>
          </FormGroup>
        </div>
        <div className="col-12 col-md-4 py-2">
          <h2 className="border-bottom">
            <FontAwesomeIcon icon={['fat', 'user']} />
            &nbsp; Users
          </h2>
          <FormGroup>
            <Input
              type="checkbox"
              id="administer-users"
              checked={permissions.can('admin:users')}
              onChange={(e) => handlePermissionChanged('admin:users', e)}
            />
            &nbsp;
            <Label check for="administer-users">
              Make Changes
            </Label>
          </FormGroup>
        </div>
        <div className="col-12 col-md-4 py-2">
          <h2 className="border-bottom">
            <FontAwesomeIcon icon={['fat', 'truck-fast']} />
            &nbsp; Driver Tasks
          </h2>
          <FormGroup>
            <Input
              type="checkbox"
              id="is-driver"
              checked={permissions.can('driver')}
              onChange={(e) => handlePermissionChanged('driver', e)}
            />
            &nbsp;
            <Label check for="is-driver">
              User is a Driver
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="view-driver-tasks"
              checked={permissions.can('view:driver-tasks')}
              onChange={(e) => handlePermissionChanged('view:driver-tasks', e)}
            />
            &nbsp;
            <Label check for="view-driver-tasks">
              View Driver Tasks
            </Label>
          </FormGroup>
          <FormGroup>
            <Input
              type="checkbox"
              id="create-driver-tasks"
              checked={permissions.can('create:driver-tasks')}
              onChange={(e) =>
                handlePermissionChanged('create:driver-tasks', e)
              }
            />
            &nbsp;
            <Label check for="create-driver-tasks">
              Create Driver Tasks
            </Label>
          </FormGroup>
        </div>
      </div>
      <div className="row sticky-bottom bg-white border-top py-2">
        {canUpdate && (
          <div className="col-auto me-auto">
            <Button onClick={handleSetPasswordClick}>
              <FontAwesomeIcon icon={['fat', 'key']} />
              &nbsp; Set Password
            </Button>
          </div>
        )}
        <div className="col text-end">
          <Button tag={Link} to={routes.users.path} outline size="lg">
            {canUpdate ? 'Cancel' : 'Close'}
          </Button>
          {canUpdate && (
            <>
              &nbsp;
              <Button onClick={handleSaveClick} color="success" size="lg">
                <FontAwesomeIcon icon={['fat', 'save']} />
                &nbsp; Save
              </Button>
            </>
          )}
        </div>
      </div>
      <Modal
        isOpen={showPasswordModal}
        toggle={handleSetPasswordCancel}
        onOpened={handlePasswordModalOpened}
        autoFocus={false}>
        <ModalHeader toggle={handleSetPasswordCancel}>Set Password</ModalHeader>
        <ModalBody>
          <FormGroup>
            <label htmlFor="password">New Password</label>
            <Input
              id="password"
              bsSize="lg"
              value={password}
              onChange={handlePasswordChange}
              autoFocus
            />
          </FormGroup>
        </ModalBody>
        <ModalFooter>
          <Button outline onClick={handleSetPasswordCancel}>
            Cancel
          </Button>
          <Button color="primary" outline onClick={handleSetPasswordConfirm}>
            <FontAwesomeIcon icon={['fat', 'key']} />
            &nbsp; Save
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}

export default Detail;
