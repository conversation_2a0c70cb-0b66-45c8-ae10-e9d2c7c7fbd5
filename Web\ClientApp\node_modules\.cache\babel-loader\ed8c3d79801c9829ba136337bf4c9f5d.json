{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\List-Item.tsx\",\n  _s = $RefreshSig$();\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport { routes } from \"app/routes\";\nimport { useRef } from \"react\";\nimport { useDrag, useDrop } from \"react-dnd\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function PlantListItem(_ref) {\n  _s();\n  let {\n    plant\n  } = _ref;\n  const ref = useRef(null),\n    [{\n      isDragging\n    }, drag] = useDrag(() => ({\n      type: 'plant',\n      item: {\n        id: '1'\n      },\n      collect: monitor => ({\n        isDragging: monitor.isDragging()\n      })\n    })),\n    [{\n      isOver\n    }, drop] = useDrop(() => ({\n      accept: 'plant',\n      drop: item => {\n        console.log('dropped', item.id);\n      },\n      collect: monitor => ({\n        isOver: monitor.isOver()\n      })\n    }));\n  drag(drop(ref));\n  return /*#__PURE__*/_jsxDEV(\"tr\", {\n    ref: ref,\n    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        // @ts-ignore\n        ref: drag,\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'grip-vertical']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: routes.plants.routes.detail.to(plant._id),\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'edit']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: plant.abbreviation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: plant.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.cuttingsPerPot\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.potsPerCase\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.hasLightsOut && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: ['fat', 'check-square']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.hasPinching && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: ['fat', 'check-square']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, plant._id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n}\n_s(PlantListItem, \"CPr1ARolI/Q6AZO9vkTDpDr5SX4=\", false, function () {\n  return [useDrag, useDrop];\n});\n_c = PlantListItem;\nvar _c;\n$RefreshReg$(_c, \"PlantListItem\");", "map": {"version": 3, "names": ["FontAwesomeIcon", "routes", "useRef", "useDrag", "useDrop", "Link", "PlantListItem", "plant", "ref", "isDragging", "drag", "type", "item", "id", "collect", "monitor", "isOver", "drop", "accept", "console", "log", "plants", "detail", "to", "_id", "abbreviation", "name", "cuttingsPerPot", "potsPerCase", "hasLightsOut", "hasPinching"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/List-Item.tsx"], "sourcesContent": ["import { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Plant } from \"api/models/plants\";\r\nimport { routes } from \"app/routes\";\r\nimport { useRef } from \"react\";\r\nimport { useDrag, useDrop } from \"react-dnd\";\r\nimport { Link } from \"react-router-dom\";\r\n\r\n\r\nexport type PlantListItemProps = {\r\n  plant: Plant;\r\n}\r\n\r\nexport function PlantListItem({plant}: PlantListItemProps) {\r\n  const ref = useRef<HTMLTableRowElement>(null),\r\n  [{isDragging}, drag] = useDrag(() => ({\r\n    type: 'plant',\r\n    item: {id: '1'},\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging(),\r\n    }),\r\n  })),\r\n  [{isOver}, drop] = useDrop(() => ({\r\n    accept: 'plant',\r\n    drop: (item: {id: string}) => {\r\n      console.log('dropped', item.id);\r\n    },\r\n    collect: (monitor) => ({\r\n      isOver: monitor.isOver(),\r\n    }),\r\n  }));\r\n\r\n  drag(drop(ref));\r\n\r\n  return (\r\n    <tr key={plant._id} ref={ref}>\r\n      <td>\r\n        <div \r\n          // @ts-ignore\r\n          ref={drag}>\r\n          <FontAwesomeIcon icon={['fat', 'grip-vertical']} />\r\n        </div>\r\n      </td>\r\n      <td>\r\n        <Link to={routes.plants.routes.detail.to(plant._id)}>\r\n          <FontAwesomeIcon icon={['fat', 'edit']} />\r\n        </Link>\r\n      </td>\r\n      <td>{plant.abbreviation}</td>\r\n      <td>{plant.name}</td>\r\n      <td className=\"text-center\">{plant.cuttingsPerPot}</td>\r\n      <td className=\"text-center\">{plant.potsPerCase}</td>\r\n      <td className=\"text-center\">\r\n        {plant.hasLightsOut &&\r\n          <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n        }\r\n      </td>\r\n      <td className=\"text-center\">\r\n        {plant.hasPinching &&\r\n          <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n        }\r\n      </td>\r\n    </tr>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,OAAO,EAAEC,OAAO,QAAQ,WAAW;AAC5C,SAASC,IAAI,QAAQ,kBAAkB;AAAC;AAOxC,OAAO,SAASC,aAAa,OAA8B;EAAA;EAAA,IAA7B;IAACC;EAAyB,CAAC;EACvD,MAAMC,GAAG,GAAGN,MAAM,CAAsB,IAAI,CAAC;IAC7C,CAAC;MAACO;IAAU,CAAC,EAAEC,IAAI,CAAC,GAAGP,OAAO,CAAC,OAAO;MACpCQ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE;QAACC,EAAE,EAAE;MAAG,CAAC;MACfC,OAAO,EAAGC,OAAO,KAAM;QACrBN,UAAU,EAAEM,OAAO,CAACN,UAAU;MAChC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,CAAC;MAACO;IAAM,CAAC,EAAEC,IAAI,CAAC,GAAGb,OAAO,CAAC,OAAO;MAChCc,MAAM,EAAE,OAAO;MACfD,IAAI,EAAGL,IAAkB,IAAK;QAC5BO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAER,IAAI,CAACC,EAAE,CAAC;MACjC,CAAC;MACDC,OAAO,EAAGC,OAAO,KAAM;QACrBC,MAAM,EAAED,OAAO,CAACC,MAAM;MACxB,CAAC;IACH,CAAC,CAAC,CAAC;EAEHN,IAAI,CAACO,IAAI,CAACT,GAAG,CAAC,CAAC;EAEf,oBACE;IAAoB,GAAG,EAAEA,GAAI;IAAA,wBAC3B;MAAA,uBACE;QACE;QACA,GAAG,EAAEE,IAAK;QAAA,uBACV,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,eAAe;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IAC/C;MAAA;MAAA;MAAA;IAAA,QACH,eACL;MAAA,uBACE,QAAC,IAAI;QAAC,EAAE,EAAET,MAAM,CAACoB,MAAM,CAACpB,MAAM,CAACqB,MAAM,CAACC,EAAE,CAAChB,KAAK,CAACiB,GAAG,CAAE;QAAA,uBAClD,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IACrC;MAAA;MAAA;MAAA;IAAA,QACJ,eACL;MAAA,UAAKjB,KAAK,CAACkB;IAAY;MAAA;MAAA;MAAA;IAAA,QAAM,eAC7B;MAAA,UAAKlB,KAAK,CAACmB;IAAI;MAAA;MAAA;MAAA;IAAA,QAAM,eACrB;MAAI,SAAS,EAAC,aAAa;MAAA,UAAEnB,KAAK,CAACoB;IAAc;MAAA;MAAA;MAAA;IAAA,QAAM,eACvD;MAAI,SAAS,EAAC,aAAa;MAAA,UAAEpB,KAAK,CAACqB;IAAW;MAAA;MAAA;MAAA;IAAA,QAAM,eACpD;MAAI,SAAS,EAAC,aAAa;MAAA,UACxBrB,KAAK,CAACsB,YAAY,iBACjB,QAAC,eAAe;QAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;MAAE;QAAA;QAAA;QAAA;MAAA;IAAG;MAAA;MAAA;MAAA;IAAA,QAEjD,eACL;MAAI,SAAS,EAAC,aAAa;MAAA,UACxBtB,KAAK,CAACuB,WAAW,iBAChB,QAAC,eAAe;QAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;MAAE;QAAA;QAAA;QAAA;MAAA;IAAG;MAAA;MAAA;MAAA;IAAA,QAEjD;EAAA,GA1BEvB,KAAK,CAACiB,GAAG;IAAA;IAAA;IAAA;EAAA,QA2Bb;AAET;AAAC,GAnDelB,aAAa;EAAA,QAEJH,OAAO,EAOXC,OAAO;AAAA;AAAA,KATZE,aAAa;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}