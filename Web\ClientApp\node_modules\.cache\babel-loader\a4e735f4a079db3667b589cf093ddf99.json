{"ast": null, "code": "import { useMemo } from 'react';\nexport function useOptionalFactory(arg, deps) {\n  const memoDeps = [...(deps || [])];\n  if (deps == null && typeof arg !== 'function') {\n    memoDeps.push(arg);\n  }\n  return useMemo(() => {\n    return typeof arg === 'function' ? arg() : arg;\n  }, memoDeps);\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAI/B,OAAO,SAASC,kBAAkB,CACjCC,GAAyB,EACzBC,IAAgB,EACZ;EACJ,MAAMC,QAAQ,GAAG,KAAKD,IAAI,IAAI,EAAE,EAAE;EAClC,IAAIA,IAAI,IAAI,IAAI,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;IAC9CE,QAAQ,CAACC,IAAI,CAACH,GAAG,CAAC;;EAEnB,OAAOF,OAAO,CAAI,MAAM;IACvB,OAAO,OAAOE,GAAG,KAAK,UAAU,GAAGA,GAAI,EAAc,GAAIA,GAAG;GAC5D,EAAEE,QAAQ,CAAC", "names": ["useMemo", "useOptionalFactory", "arg", "deps", "memoDeps", "push"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useOptionalFactory.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport type { FactoryOrInstance } from './types.js'\n\nexport function useOptionalFactory<T>(\n\targ: FactoryOrInstance<T>,\n\tdeps?: unknown[],\n): T {\n\tconst memoDeps = [...(deps || [])]\n\tif (deps == null && typeof arg !== 'function') {\n\t\tmemoDeps.push(arg)\n\t}\n\treturn useMemo<T>(() => {\n\t\treturn typeof arg === 'function' ? (arg as () => T)() : (arg as T)\n\t}, memoDeps)\n}\n"]}, "metadata": {}, "sourceType": "module"}