import React, { useCallback, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router';
import moment from 'moment';
import {
  Button,
  FormGroup,
  Input,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { notificationsApi } from 'api/notifications-service';
import {
  createOrder,
  createOrderNumber,
  Order,
  OrderVariety,
  SalesWeek,
} from 'api/models/orders';
import { Plant } from 'api/models/plants';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectPlants } from 'features/plants/plants-slice';
import { selectCustomers } from 'features/customers/customers-slice';
import { selectZones } from 'features/zones/zones-slice';
import { handleFocus } from 'utils/focus';
import {
  toWeekAndDay,
  formatNumber,
  parseWeekAndDay,
  toWeekAndYear,
} from 'utils/format';
import { deleteOrder, saveOrder, selectOrder, setOrder } from './detail-slice';
import { selectAllOrders } from './orders-slice';
import { Variety } from './Variety';
import { SalesWeekRow } from './SalesWeekRow';
import { guid } from 'utils/guid';

export function Detail() {
  const dispatch = useDispatch(),
    navigate = useNavigate(),
    { isInRole, user } = useAuth(),
    { id } = useParams<{ id: string }>(),
    orders = useSelector(selectAllOrders),
    order = useSelector(selectOrder),
    customers = useSelector(selectCustomers),
    zones = useSelector(selectZones),
    plants = useSelector(selectPlants),
    isNew = !order?._rev,
    [stickWeek, setStickWeek] = useState(''),
    [lightsOutWeek, setLightsOutWeek] = useState(''),
    [partialSpaceWeek, setPartialSpaceWeek] = useState(''),
    [fullSpaceWeek, setFullSpaceWeek] = useState(''),
    [pinchWeek, setPinchWeek] = useState(''),
    [flowerWeek, setFlowerWeek] = useState(''),
    [showNotificationModal, setShowNotificationModal] = useState(false),
    [notificationMessage, setNotificationMessage] = useState(''),
    hasSalesWeeks = Array.isArray(order?.salesWeeks),
    canUpdate =
      (isNew && isInRole('create:orders')) || isInRole('update:orders'),
    canDelete = isInRole('delete:orders'),
    salesWeekTotal = order?.salesWeeks?.reduce(
      (total, sw) => total + sw.cases,
      0
    );

  const onPlantChange = useCallback(
    (plant: Plant, order: Order) => {
      const orderNumber = createOrderNumber(
          plant.abbreviation,
          order.customer.abbreviation,
          order.stickDate
        ),
        update = { ...order, orderNumber, plant };

      update.hasLightsOut = plant.hasLightsOut;
      if (plant.hasLightsOut) {
        update.lightsOutDate = order.fullSpaceDate || order.stickDate;
        update.lightsOutZone = order.fullSpaceZone || order.stickZone;
      }

      update.hasPinching = plant.hasPinching;
      if (plant.hasPinching) {
        update.pinchDate =
          order.pinchDate ||
          moment(order.stickDate)
            .add(plant.daysToPinch || 0, 'days')
            .format('YYYY-MM-DD');
        setPinchWeek(toWeekAndDay(update.pinchDate));
      }

      if (plant.varieties) {
        update.varieties = [];
      } else {
        delete update.varieties;
      }

      dispatch(setOrder(update));
    },
    [dispatch]
  );

  useEffect(() => {
    const found = orders.find((o) => o._id === id);
    if (found && found._id !== order?._id) {
      dispatch(setOrder(found));
      setStickWeek(toWeekAndDay(found.stickDate));
      if (found.lightsOutDate) {
        setLightsOutWeek(toWeekAndDay(found.lightsOutDate));
      }
      if (found.partialSpaceDate) {
        setPartialSpaceWeek(toWeekAndDay(found.partialSpaceDate));
      }
      if (found.fullSpaceDate) {
        setFullSpaceWeek(toWeekAndDay(found.fullSpaceDate));
      }
      if (found.hasPinching) {
        setPinchWeek(toWeekAndDay(found.pinchDate));
      }
      setFlowerWeek(toWeekAndDay(found.flowerDate));
    } else if (id === 'new') {
      if (!order) {
        const newOrder = createOrder();
        if (zones.length) {
          newOrder.stickZone = zones[0];
          if (newOrder.partialSpaceDate) {
            newOrder.partialSpaceZone = zones[0];
          }
          if (newOrder.lightsOutDate) {
            newOrder.lightsOutZone = zones[0];
          }
          if (newOrder.fullSpaceDate) {
            newOrder.fullSpaceZone = zones[0];
          }
        }
        dispatch(setOrder(newOrder));
      } else if (
        plants.length &&
        !plants.some((p) => p._id === order.plant._id)
      ) {
        const firstPlant = plants[0];
        onPlantChange(firstPlant, order);
        setStickWeek(toWeekAndDay(order.stickDate));
        if (order.lightsOutDate) {
          setLightsOutWeek(toWeekAndDay(order.lightsOutDate));
        }
        if (order.partialSpaceDate) {
          setPartialSpaceWeek(toWeekAndDay(order.partialSpaceDate));
        }
        if (order.fullSpaceDate) {
          setFullSpaceWeek(toWeekAndDay(order.fullSpaceDate));
        }
        if (order.pinchDate) {
          setPinchWeek(toWeekAndDay(order.pinchDate));
        } else if (order.hasPinching) {
          setPinchWeek(
            toWeekAndDay(
              moment(order.stickDate)
                .add(firstPlant.daysToPinch || 0, 'days')
                .format('YYYY-MM-DD')
            )
          );
        }
        setFlowerWeek(toWeekAndDay(order.flowerDate));
      }
    }

    return function cleanup() {
      //dispatch(setOrder(null));
    };
  }, [dispatch, id, onPlantChange, order, orders, plants, zones]);

  const handleCustomerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const abbreviation = e.target.value,
      customer = customers.find((c) => c.abbreviation === abbreviation);

    if (order && customer) {
      const orderNumber = createOrderNumber(
          order.plant.abbreviation,
          customer.abbreviation,
          order.stickDate
        ),
        update = { ...order, orderNumber, customer };
      dispatch(setOrder(update));
    }
  };

  const handlePlantChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const id = e.target.value,
        plant = plants.find((p) => p._id === id);

      if (plant) {
        onPlantChange(plant, order);
      }
    }
  };

  const handleAddVarietyClick = () => {
    if (order?.varieties) {
      const varieties = order.varieties
          .map((v) => ({ ...v }))
          .concat([
            { name: '', cuttings: 0, pots: 0, cases: 0, comment: null },
          ]),
        update = { ...order, varieties };

      dispatch(setOrder(update));
    }
  };

  const handleCuttingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const cuttings = e.target.valueAsNumber || 0;
      onCuttingsChange(cuttings, order);
    }
  };

  const handlePotsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const pots = e.target.valueAsNumber || 0;
      onPotsChange(pots, order);
    }
  };

  const handleCasesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const cases = e.target.valueAsNumber || 0;
      onCasesChange(cases, order);
    }
  };

  const onCuttingsChange = (cuttings: number, order: Order) => {
    const { plant } = order,
      cuttingsPerPot = plant.cuttingsPerPot || 1,
      potsPerCase = plant.potsPerCase || 1,
      pots = Math.round(cuttings / cuttingsPerPot),
      cases = Math.round(pots / potsPerCase),
      cuttingsPerTableTight = plant.cuttingsPerTableTight || 1,
      tableCountTight = Math.round(cuttings / cuttingsPerTableTight),
      update = { ...order, cuttings, pots, cases, tableCountTight };

    if (order.hasPartialSpace) {
      const cuttingsPerTablePartiallySpaced =
          plant.cuttingsPerTablePartiallySpaced || 1,
        tableCountPartiallySpaced = Math.round(
          cuttings / cuttingsPerTablePartiallySpaced
        );

      update.tableCountPartiallySpaced = tableCountPartiallySpaced;
    }

    if (order.hasSpacing) {
      const cuttingsPerTableSpaced = plant.cuttingsPerTableSpaced || 1,
        tableCountSpaced = Math.round(cuttings / cuttingsPerTableSpaced);

      update.tableCountSpaced = tableCountSpaced;
    }

    dispatch(setOrder(update));
  };

  const onPotsChange = (pots: number, order: Order) => {
    const { plant } = order,
      potsPerCase = plant.potsPerCase || 1,
      cases = Math.round(pots / potsPerCase),
      update = { ...order, pots, cases };

    dispatch(setOrder(update));
  };

  const onCasesChange = (cases: number, order: Order) => {
    const update = { ...order, cases };

    dispatch(setOrder(update));
  };

  const handleSupplierPoNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const supplierPoNumber = e.target.value,
        update = { ...order, supplierPoNumber };

      dispatch(setOrder(update));
    }
  };

  const handleStickDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const stickDateValue = e.target.value,
        m = moment(stickDateValue);

      if (m.isValid()) {
        const stickDate = m.format('YYYY-MM-DD'),
          orderNumber = createOrderNumber(
            order.plant.abbreviation,
            order.customer.abbreviation,
            stickDate
          ),
          update = { ...order, stickDate, orderNumber };

        if (order.plant.daysToPinch) {
          const pinchDate = m
            .add(order.plant.daysToPinch, 'days')
            .format('YYYY-MM-DD');
          update.pinchDate = pinchDate;
          setPinchWeek(toWeekAndDay(pinchDate));
        }

        dispatch(setOrder(update));
        setStickWeek(toWeekAndDay(stickDate));
      }
    }
  };

  const handleStickWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const target = e.target.value,
        weekAndDay = parseWeekAndDay(target, true);

      setStickWeek(target);

      if (weekAndDay) {
        const date = moment(weekAndDay);

        if (date.isValid()) {
          if (date.isBefore()) {
            date.add(1, 'year');
          }
          const stickDate = date.format('YYYY-MM-DD'),
            orderNumber = createOrderNumber(
              order.plant.abbreviation,
              order.customer.abbreviation,
              stickDate
            ),
            update = { ...order, stickDate, orderNumber };

          dispatch(setOrder(update));
        }
      }
    }
  };

  const handleStickZoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const name = e.target.value,
        stickZone = zones.find((p) => p.name === name);

      if (stickZone) {
        const update = { ...order, stickZone };
        dispatch(setOrder(update));
      }
    }
  };

  const handleHasPartialSpaceChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const hasPartialSpace = e.target.checked,
        update = { ...order, hasPartialSpace };

      if (hasPartialSpace) {
        const partialSpaceDate = moment(order.stickDate)
          .add(1, 'week')
          .format('YYYY-MM-DD');
        update.partialSpaceDate = partialSpaceDate;
        update.partialSpaceZone = order.stickZone;
        update.tableCountPartiallySpaced = order.tableCountTight;
        setPartialSpaceWeek(toWeekAndDay(partialSpaceDate));
      } else {
        delete update.partialSpaceDate;
        delete update.partialSpaceZone;
        delete update.tableCountPartiallySpaced;
        setPartialSpaceWeek('');
      }

      dispatch(setOrder(update));
    }
  };

  const handlePartialSpaceWeekChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const weekAndDay = e.target.value,
        date = parseWeekAndDay(weekAndDay, true);

      setPartialSpaceWeek(weekAndDay);

      if (date) {
        const partialSpaceDate = moment(date).format('YYYY-MM-DD'),
          update = { ...order, partialSpaceDate };

        dispatch(setOrder(update));
      }
    }
  };

  const handlePartialSpaceDateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const partialSpaceDateValue = e.target.value,
        m = moment(partialSpaceDateValue);

      if (m.isValid()) {
        const partialSpaceDate = m.format('YYYY-MM-DD'),
          update = { ...order, partialSpaceDate };

        setPartialSpaceWeek(toWeekAndDay(partialSpaceDate));

        dispatch(setOrder(update));
      }
    }
  };

  const handlePartialSpaceZoneChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const name = e.target.value,
        partialSpaceZone = zones.find((p) => p.name === name);

      if (partialSpaceZone) {
        const update = { ...order, partialSpaceZone };
        dispatch(setOrder(update));
      }
    }
  };

  const handleTableCountPartiallySpacedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const tableCountPartiallySpaced = e.target.valueAsNumber || 0,
        update = { ...order, tableCountPartiallySpaced };
      dispatch(setOrder(update));
    }
  };

  const handleHasSpacingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const hasSpacing = e.target.checked,
        update = { ...order, hasSpacing };

      if (hasSpacing) {
        update.fullSpaceDate = update.stickDate;
        update.fullSpaceZone = update.stickZone;
        update.tableCountSpaced = update.tableCountTight;
        setFullSpaceWeek(update.stickDate);
      } else {
        delete update.fullSpaceDate;
        delete update.fullSpaceZone;
        delete update.tableCountSpaced;
        setFullSpaceWeek('');
      }

      dispatch(setOrder(update));
    }
  };

  const handleFullSpaceDateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const fullSpaceDateValue = e.target.value,
        m = moment(fullSpaceDateValue);

      if (m.isValid()) {
        const fullSpaceDate = m.format('YYYY-MM-DD'),
          update = { ...order, fullSpaceDate };

        if (order.hasLightsOut) {
          update.lightsOutDate = fullSpaceDate;
        }

        dispatch(setOrder(update));
        dispatch(toWeekAndDay(fullSpaceDate));
      }
    }
  };

  const handleFullSpaceWeekChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const weekAndDay = e.target.value,
        date = parseWeekAndDay(weekAndDay, true);

      setFullSpaceWeek(weekAndDay);

      if (date) {
        const fullSpaceDate = moment(date).format('YYYY-MM-DD'),
          update = { ...order, fullSpaceDate };

        dispatch(setOrder(update));
      }
    }
  };

  const handleFullSpaceZoneChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const name = e.target.value,
        fullSpaceZone = zones.find((p) => p.name === name);

      if (fullSpaceZone) {
        const update = { ...order, fullSpaceZone };

        if (order.hasLightsOut) {
          update.lightsOutZone = fullSpaceZone;
        }

        dispatch(setOrder(update));
      }
    }
  };

  const handleHasLightsOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const hasLightsOut = e.target.checked,
        update = { ...order, hasLightsOut };

      if (hasLightsOut) {
        update.lightsOutDate = order.fullSpaceDate || order.stickDate;
        update.lightsOutZone = order.fullSpaceZone || order.stickZone;
        setLightsOutWeek(update.lightsOutDate);
      } else {
        delete update.lightsOutDate;
        setLightsOutWeek('');
      }

      dispatch(setOrder(update));
    }
  };

  const handleLightsOutDateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const lightsOutDateValue = e.target.value,
        m = moment(lightsOutDateValue);

      if (m.isValid()) {
        const lightsOutDate = m.format('YYYY-MM-DD'),
          update = { ...order, lightsOutDate };

        dispatch(setOrder(update));
        setLightsOutWeek(toWeekAndDay(lightsOutDate));
      }
    }
  };

  const handleLightsOutWeekChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const weekAndDay = e.target.value,
        date = parseWeekAndDay(weekAndDay, true);

      setLightsOutWeek(weekAndDay);

      if (date) {
        const lightsOutDate = moment(date).format('YYYY-MM-DD'),
          update = { ...order, lightsOutDate };

        dispatch(setOrder(update));
      }
    }
  };

  const handleLightsOutZoneChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const name = e.target.value,
        lightsOutZone = zones.find((p) => p.name === name);

      if (lightsOutZone) {
        const update = { ...order, lightsOutZone };
        dispatch(setOrder(update));
      }
    }
  };

  const handleHasPinchingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const hasPinching = e.target.checked,
        update = { ...order, hasPinching };

      if (hasPinching) {
        const daysToPinch = order.plant?.daysToPinch || 0,
          pinchDate = moment(
            order.pinchDate ||
              moment(order.stickDate)
                .add(daysToPinch, 'days')
                .format('YYYY-MM-DD')
          ).format('YYYY-MM-DD');
        update.pinchDate = pinchDate;
        setPinchWeek(toWeekAndDay(pinchDate));
      } else {
        delete update.pinchDate;
        setPinchWeek('');
      }

      dispatch(setOrder(update));
    }
  };

  const handlePinchWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const weekAndDay = e.target.value,
        date = parseWeekAndDay(weekAndDay, true);

      setPinchWeek(weekAndDay);

      if (date) {
        const pinchDate = moment(date).format('YYYY-MM-DD'),
          update = { ...order, pinchDate };

        dispatch(setOrder(update));
      }
    }
  };

  const handlePinchDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const pinchDateValue = e.target.value,
        m = moment(pinchDateValue);

      if (m.isValid()) {
        const pinchDate = m.format('YYYY-MM-DD'),
          update = { ...order, pinchDate };

        setPinchWeek(toWeekAndDay(pinchDate));

        dispatch(setOrder(update));
      }
    }
  };

  const handleFlowerDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const flowerDateValue = e.target.value,
        m = moment(flowerDateValue);

      if (m.isValid()) {
        const flowerDate = m.format('YYYY-MM-DD'),
          update = { ...order, flowerDate };

        dispatch(setOrder(update));
        setFlowerWeek(toWeekAndDay(flowerDate));
      }
    }
  };

  const handleFlowerWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const weekAndDay = e.target.value,
        date = parseWeekAndDay(weekAndDay, true);

      setFlowerWeek(weekAndDay);

      if (date) {
        const flowerDate = moment(date).format('YYYY-MM-DD'),
          update = { ...order, flowerDate };

        dispatch(setOrder(update));
      }
    }
  };

  const handleTableCountTightChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const tableCountTight = e.target.valueAsNumber || 0,
        update = { ...order, tableCountTight };
      dispatch(setOrder(update));
    }
  };

  const handleTableCountSpacedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const tableCountSpaced = e.target.valueAsNumber || 0,
        update = { ...order, tableCountSpaced };

      dispatch(setOrder(update));
    }
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const notes = e.target.value,
        update = { ...order, notes };

      dispatch(setOrder(update));
    }
  };

  const handleVarietyChange = (name: string, variety: OrderVariety | null) => {
    if (order?.varieties) {
      const index = order.varieties.findIndex((v) => v.name === name);

      if (index !== -1) {
        const varieties = order.varieties.map((v) => ({ ...v }));
        if (variety) {
          varieties.splice(index, 1, variety);
        } else {
          varieties.splice(index, 1);
        }

        const cuttings = varieties.reduce((total, v) => total + v.cuttings, 0),
          update = { ...order, varieties, cuttings };

        onCuttingsChange(cuttings, update);
      }
    }
  };

  const handleSeasonChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const season = e.target.value,
        update = { ...order, season };

      dispatch(setOrder(update));
    }
  };

  const handleGoBackClick = () => goBack();

  const handleSaveClick = async () => {
    if (user) {
      if (order?.salesWeeks?.length && salesWeekTotal !== order.cases) {
        alert(
          'The total number of cases in the sales weeks must match the total number of cases in the order.'
        );
        return;
      }

      const result: any = await dispatch(saveOrder());

      if (!result.error) {
        if (!isNew) {
          setShowNotificationModal(true);
        } else {
          goBack();
        }
      }
    }
  };

  const handleDeleteClick = async () => {
    const result: any = await dispatch(deleteOrder());

    if (!result.error) {
      goBack();
    }
  };

  const goBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate(routes.orders.path);
    }
  };

  const handleNotificationMessageChanged = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setNotificationMessage(e.target.value);
  };

  const handleNotificationModalOpened = () => {
    setNotificationMessage('');
  };

  const handleSendNotificationCancel = () => {
    setShowNotificationModal(false);
    goBack();
  };

  const handleSendNotificationConfirm = async () => {
    if (order && user) {
      try {
        const link = window.location.href,
          model = {
            user: user.name,
            plant: order.plant.name,
            orderNumber: order.orderNumber,
            link,
            message: notificationMessage,
          },
          args = { user, model };

        await notificationsApi.orderChanged(args);

        goBack();
      } catch (e) {
        console.error(e);
      }
    }
  };

  const handleHasSalesWeeksChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (order) {
      const hasSalesWeeks = e.target.checked;

      if (hasSalesWeeks) {
        if (!Array.isArray(order.salesWeeks)) {
          const id = guid(),
            week = toWeekAndYear(order.flowerDate),
            salesWeek = { id, week, cases: order.cases },
            update = { ...order, salesWeeks: [salesWeek] };
          dispatch(setOrder(update));
        }
      } else {
        const update = { ...order };
        delete update.salesWeeks;
        dispatch(setOrder(update));
      }
    }
  };

  const handleAddSalesWeekClick = () => {
    if (order?.salesWeeks) {
      const id = guid(),
        salesWeeks = order.salesWeeks
          .map((w) => ({ ...w }))
          .concat([{ id, week: '', cases: 0 }]),
        update = { ...order, salesWeeks };

      dispatch(setOrder(update));
    }
  };

  const handleSalesWeekChange = (id: string, salesWeek: SalesWeek | null) => {
    if (order?.salesWeeks) {
      const salesWeeks = order.salesWeeks.map((sw) => ({ ...sw })),
        index = salesWeeks.findIndex((sw) => sw.id === id);

      if (index !== -1) {
        if (salesWeek) {
          salesWeeks.splice(index, 1, salesWeek);
        } else {
          salesWeeks.splice(index, 1);
        }
      }

      const update = { ...order, salesWeeks };

      dispatch(setOrder(update));
    }
  };

  return (
    <div className="container d-grid gap-2">
      <div className="row mt-2 sticky-top-navbar bg-white border-bottom pt-2">
        <div className="col-auto">
          <Button color="link" onClick={handleGoBackClick}>
            <FontAwesomeIcon icon={['fat', 'chevron-left']} />
            &nbsp; Back to Orders List
          </Button>
        </div>
        <h1 className="col">{isNew ? 'New Order' : order.orderNumber}</h1>
      </div>
      {!!order && (
        <>
          <div className="row p-2">
            <div className="col-12 col-md-3">
              <label htmlFor="order-customer">Customer</label>
              <Input
                id="order-customer"
                type="select"
                value={order.customer.abbreviation}
                onChange={handleCustomerChange}
                disabled={!canUpdate}>
                {customers.map((customer) => (
                  <option
                    key={customer.abbreviation}
                    value={customer.abbreviation}>
                    {customer.name}
                  </option>
                ))}
              </Input>
            </div>
            <div className="col-12 col-md-3">
              <label htmlFor="order-plant">Plant</label>
              <Input
                id="order-plant"
                type="select"
                value={order.plant._id}
                onChange={handlePlantChange}
                disabled={!canUpdate}>
                {plants.map((plant) => (
                  <option key={plant._id} value={plant._id}>
                    {plant.name}
                  </option>
                ))}
              </Input>
            </div>
          </div>
          {!order.varieties && (
            <div className="row p-2">
              <div className="col-12 col-md-3">
                <label htmlFor="order-cuttings">Cuttings</label>
                <Input
                  id="order-cuttings"
                  type="number"
                  value={order.cuttings}
                  onChange={handleCuttingsChange}
                  onFocus={handleFocus}
                  className="text-end"
                  disabled={!canUpdate}
                />
              </div>
              <div className="col-12 col-md-3">
                <label htmlFor="order-pots">Pots</label>
                <Input
                  id="order-pots"
                  type="number"
                  value={order.pots}
                  onChange={handlePotsChange}
                  onFocus={handleFocus}
                  className="text-end"
                  disabled={!canUpdate}
                />
              </div>
              <div className="col-12 col-md-3">
                <label htmlFor="order-cases">Cases</label>
                <Input
                  id="order-cases"
                  type="number"
                  value={order.cases}
                  onChange={handleCasesChange}
                  onFocus={handleFocus}
                  className="text-end"
                  disabled={!canUpdate}
                />
              </div>
            </div>
          )}
          <div className="row p-2 bg-light">
            <div className="col-12 col-md">
              <label htmlFor="order-stick-week">Stick Date</label>
              <Input
                id="order-stick-week"
                value={stickWeek}
                onChange={handleStickWeekChange}
                onFocus={handleFocus}
                disabled={!canUpdate}
              />
              <Input
                id="order-stick-date"
                type="date"
                value={order.stickDate}
                onChange={handleStickDateChange}
                tabIndex={-1}
                disabled={!canUpdate}
              />
            </div>
            <div className="col-12 col-md">
              <label htmlFor="order-stick-zone">Stick Zone</label>
              <Input
                id="order-stick-zone"
                type="select"
                value={order.stickZone.name}
                onChange={handleStickZoneChange}
                disabled={!canUpdate}>
                {zones.map((zone) => (
                  <option key={zone.name} value={zone.name}>
                    {zone.name}
                  </option>
                ))}
              </Input>
            </div>
            <div className="col-12 col-md">
              <label htmlFor="order-flower-week">Flower Date</label>
              <Input
                id="order-flower-week"
                value={flowerWeek}
                onChange={handleFlowerWeekChange}
                onFocus={handleFocus}
                disabled={!canUpdate}
              />
              <Input
                id="order-flower-date"
                type="date"
                value={order.flowerDate}
                onChange={handleFlowerDateChange}
                tabIndex={-1}
                disabled={!canUpdate}
              />
            </div>
            <div className="col-12 col-md">
              <label htmlFor="order-table-count-tight">Tables (Tight)</label>
              <Input
                id="order-table-count-tight"
                type="number"
                value={order.tableCountTight}
                onChange={handleTableCountTightChange}
                onFocus={handleFocus}
                disabled={!canUpdate}
              />
            </div>
          </div>
          <div className="row p-2">
            <div className="col-12 col-md">
              <FormGroup check>
                <Input
                  id="order-has-spacing-space"
                  type="checkbox"
                  checked={order.hasSpacing}
                  onChange={handleHasSpacingChange}
                  disabled={!canUpdate}
                />
                <label htmlFor="order-has-spacing-space">
                  Product is Spaced?
                </label>
              </FormGroup>
            </div>
            {order.hasSpacing && (
              <>
                <div className="col-12 col-md">
                  <label htmlFor="order-space-week">Space Date</label>
                  <Input
                    id="order-space-week"
                    value={fullSpaceWeek}
                    onChange={handleFullSpaceWeekChange}
                    onFocus={handleFocus}
                    disabled={!canUpdate}
                  />
                  <Input
                    id="order-space-date"
                    type="date"
                    value={order.fullSpaceDate}
                    onChange={handleFullSpaceDateChange}
                    tabIndex={-1}
                    disabled={!canUpdate}
                  />
                </div>
                <div className="col-12 col-md">
                  <label htmlFor="order-full-space-zone">Full Space Zone</label>
                  <Input
                    id="order-full-space-zone"
                    type="select"
                    value={order.fullSpaceZone?.name || ''}
                    onChange={handleFullSpaceZoneChange}
                    disabled={!canUpdate}>
                    {zones.map((zone) => (
                      <option key={zone.name} value={zone.name}>
                        {zone.name}
                      </option>
                    ))}
                  </Input>
                </div>
                <div className="col-12 col-md">
                  <label htmlFor="order-table-count-spaced">
                    Tables (Spaced)
                  </label>
                  <Input
                    id="order-table-count-spaced"
                    type="number"
                    value={order.tableCountSpaced}
                    onChange={handleTableCountSpacedChange}
                    onFocus={handleFocus}
                    disabled={!canUpdate}
                  />
                </div>
              </>
            )}
          </div>
          <div className="row p-2 bg-light">
            <div className="col-12 col-md">
              <FormGroup check>
                <Input
                  id="order-has-lights-out"
                  type="checkbox"
                  checked={order.hasLightsOut}
                  onChange={handleHasLightsOutChange}
                  disabled={!canUpdate}
                />
                <label htmlFor="order-has-lights-out">
                  Product has Lights-Out?
                </label>
              </FormGroup>
            </div>
            {order.hasLightsOut && (
              <>
                <div className="col-12 col-md">
                  <label htmlFor="order-lights-out-week">Lights-Out Date</label>
                  <Input
                    id="order-lights-out-week"
                    value={lightsOutWeek}
                    onChange={handleLightsOutWeekChange}
                    onFocus={handleFocus}
                    disabled={!canUpdate}
                  />
                  <Input
                    id="order-lights-out-date"
                    type="date"
                    value={order.lightsOutDate}
                    onChange={handleLightsOutDateChange}
                    tabIndex={-1}
                    disabled={!canUpdate}
                  />
                </div>
                <div className="col-12 col-md">
                  <label htmlFor="order-lights-out-zone">Lights-Out Zone</label>
                  <Input
                    id="order-lights-out-zone"
                    type="select"
                    value={order.lightsOutZone?.name || ''}
                    onChange={handleLightsOutZoneChange}
                    disabled={!canUpdate}>
                    {zones.map((zone) => (
                      <option key={zone.name} value={zone.name}>
                        {zone.name}
                      </option>
                    ))}
                  </Input>
                </div>
                <div className="col-md">&nbsp;</div>
              </>
            )}
          </div>
          <div className="row p-2">
            <div className="col-12 col-md">
              <FormGroup check>
                <Input
                  id="order-has-partial-space"
                  type="checkbox"
                  checked={order.hasPartialSpace}
                  onChange={handleHasPartialSpaceChange}
                  disabled={!canUpdate}
                />
                <label htmlFor="order-has-partial-space">
                  Product is Partial Spaced?
                </label>
              </FormGroup>
            </div>
            {order.hasPartialSpace && (
              <>
                <div className="col-12 col-md">
                  <label htmlFor="order-partial-space-week">
                    Partial Space Date
                  </label>
                  <Input
                    id="order-partial-space-week"
                    value={partialSpaceWeek}
                    onChange={handlePartialSpaceWeekChange}
                    onFocus={handleFocus}
                    disabled={!canUpdate}
                  />
                  <Input
                    id="order-partial-space-date"
                    type="date"
                    value={order.partialSpaceDate}
                    onChange={handlePartialSpaceDateChange}
                    tabIndex={-1}
                    disabled={!canUpdate}
                  />
                </div>
                <div className="col-12 col-md">
                  <label htmlFor="order-partial-space-zone">
                    Partial Space Zone
                  </label>
                  <Input
                    id="order-partial-space-zone"
                    type="select"
                    value={order.partialSpaceZone?.name || ''}
                    onChange={handlePartialSpaceZoneChange}
                    disabled={!canUpdate}>
                    {zones.map((zone) => (
                      <option key={zone.name} value={zone.name}>
                        {zone.name}
                      </option>
                    ))}
                  </Input>
                </div>
                <div className="col-12 col-md">
                  <label htmlFor="order-table-count-partially-spaced">
                    Tables (Partially Spaced)
                  </label>
                  <Input
                    id="order-table-count-partially-spaced"
                    type="number"
                    value={order.tableCountPartiallySpaced}
                    onChange={handleTableCountPartiallySpacedChange}
                    onFocus={handleFocus}
                    disabled={!canUpdate}
                  />
                </div>
              </>
            )}
          </div>
          <div className="row p-2 bg-light">
            <div className="col-12 col-md">
              <FormGroup check>
                <Input
                  id="order-has-pinching"
                  type="checkbox"
                  checked={order.hasPinching}
                  onChange={handleHasPinchingChange}
                  disabled={!canUpdate}
                />
                <label htmlFor="order-has-pinching">
                  Product Needs Pinching?
                </label>
              </FormGroup>
            </div>
            {order.hasPinching && (
              <>
                <div className="col-12 col-md">
                  <label htmlFor="order-pinch-week">Pinch Date</label>
                  <Input
                    id="order-pinch-week"
                    value={pinchWeek}
                    onChange={handlePinchWeekChange}
                    onFocus={handleFocus}
                    disabled={!canUpdate}
                  />
                  <Input
                    id="order-pinch-date"
                    type="date"
                    value={order.pinchDate}
                    onChange={handlePinchDateChange}
                    tabIndex={-1}
                    disabled={!canUpdate}
                  />
                </div>
                <div className="col-0 col-md">&nbsp;</div>
                <div className="col-0 col-md">&nbsp;</div>
              </>
            )}
          </div>
          <div className="row p-2">
            <div className="col-12 col-md">
              <label htmlFor="order-supplier-po-number">Supplier PO #</label>
              <Input
                id="order-supplier-po-number"
                value={order.supplierPoNumber}
                onChange={handleSupplierPoNumberChange}
                disabled={!canUpdate}
              />
            </div>
            <div className="col-12 col-md">
              <label htmlFor="order-notes">Notes</label>
              <Input
                id="order=notes"
                type="textarea"
                value={order.notes || ''}
                onChange={handleNotesChange}
                disabled={!canUpdate}
              />
            </div>
            <div className="col-12 col-md">
              <label htmlFor="order-season">Season</label>
              <Input
                id="order-season"
                value={order.season || ''}
                onChange={handleSeasonChange}
                disabled={!canUpdate}
              />
            </div>
          </div>
          {!!order.varieties && (
            <>
              <div className="row">
                <h4 className="col">Varieties</h4>
              </div>
              <div className="row fw-bold">
                <div className="col-12 col-md-2 offset-md-1 text-center">
                  Variety
                </div>
                <div className="col-12 col-md-2 text-center">Cuttings</div>
                <div className="col-12 col-md-2 text-center">Pots</div>
                <div className="col-12 col-md-2 text-center">Cases</div>
                <div className="col-12 col-md-3 text-center">Comment</div>
              </div>
              {order.varieties.map((variety) => (
                <Variety
                  key={variety.name}
                  variety={variety}
                  onChange={(v) => handleVarietyChange(variety.name, v)}
                />
              ))}
              <div className="row border-top-double">
                {canUpdate && (
                  <div className="col-1 text-center">
                    <Button
                      color="success"
                      size="sm"
                      outline
                      onClick={handleAddVarietyClick}
                      className="mt-1">
                      <FontAwesomeIcon icon={['fat', 'plus']} />
                    </Button>
                  </div>
                )}
                <div className="col-12 col-md-2 offset-md-2">
                  <Input
                    id="order-cuttings"
                    value={formatNumber(order.cuttings)}
                    readOnly
                    plaintext
                    className="bg-white text-end"
                  />
                </div>
                <div className="col-12 col-md-2">
                  <Input
                    id="order-pots"
                    value={formatNumber(order.pots)}
                    readOnly
                    plaintext
                    className="bg-white text-end"
                  />
                </div>
                <div className="col-12 col-md-2">
                  <Input
                    id="order-cases"
                    value={formatNumber(order.cases)}
                    readOnly
                    plaintext
                    className="bg-white text-end"
                  />
                </div>
              </div>
            </>
          )}
          <div className="row mt-5">
            <h4 className="col">Sales</h4>
          </div>
          <div className="row">
            <FormGroup check className="col">
              <Input
                id="order-has-sales-weeks"
                type="checkbox"
                checked={hasSalesWeeks}
                onChange={handleHasSalesWeeksChange}
                disabled={!canUpdate}
              />
              <label htmlFor="order-has-sales-weeks">
                Sells over multiple weeks
              </label>
            </FormGroup>
          </div>
          {hasSalesWeeks && (
            <>
              <div className="row fw-bold">
                <div className="col-12 col-md-2 offset-md-1 text-center">
                  Week
                </div>
                <div className="col-12 col-md-2 text-center">Cases</div>
              </div>
              {order.salesWeeks?.map((salesWeek) => (
                <SalesWeekRow
                  key={salesWeek.id}
                  salesWeek={salesWeek}
                  onChange={handleSalesWeekChange}
                />
              ))}
              <div className="row border-top-double">
                <div className="col-1 text-center">
                  {canUpdate && (
                    <Button
                      color="success"
                      size="sm"
                      outline
                      onClick={handleAddSalesWeekClick}
                      className="mt-1">
                      <FontAwesomeIcon icon={['fat', 'plus']} />
                    </Button>
                  )}
                </div>
                <div className="col-12 col-md-2 offset-md-2">
                  <Input
                    id="sales-week-totals"
                    value={formatNumber(salesWeekTotal)}
                    readOnly
                    plaintext
                    className="bg-white text-end"
                  />
                </div>
              </div>
            </>
          )}

          <div className="row mt-5 bg-white sticky-bottom border-top py-2">
            {!isNew && canDelete && (
              <div className="col-auto">
                <Button
                  onClick={handleDeleteClick}
                  outline
                  color="danger"
                  size="lg"
                  className="me-auto">
                  <FontAwesomeIcon icon={['fat', 'trash']} />
                  &nbsp; Delete
                </Button>
              </div>
            )}
            <div className="col text-end">
              <Button outline size="lg" onClick={handleGoBackClick}>
                {canUpdate ? 'Cancel' : 'Close'}
              </Button>
              {canUpdate && (
                <>
                  &nbsp;
                  <Button onClick={handleSaveClick} color="success" size="lg">
                    <FontAwesomeIcon icon={['fat', 'save']} />
                    &nbsp; Save
                  </Button>
                </>
              )}
            </div>
          </div>
        </>
      )}
      <Modal
        isOpen={showNotificationModal}
        toggle={handleSendNotificationCancel}
        onOpened={handleNotificationModalOpened}
        autoFocus={false}>
        <ModalHeader toggle={handleSendNotificationCancel}>
          Send Notification
        </ModalHeader>
        <ModalBody>
          <p className="text-center">
            Would you like to send a notification about this change?
          </p>

          <label htmlFor="notification-messge">Message:</label>
          <Input
            id="notification-messge"
            bsSize="lg"
            value={notificationMessage}
            onChange={handleNotificationMessageChanged}
            autoFocus
          />
        </ModalBody>
        <ModalFooter>
          <Button outline onClick={handleSendNotificationCancel}>
            Cancel
          </Button>
          <Button
            color="primary"
            outline
            onClick={handleSendNotificationConfirm}>
            <FontAwesomeIcon icon={['fat', 'paper-plane']} />
            &nbsp; Send
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}

export default Detail;
