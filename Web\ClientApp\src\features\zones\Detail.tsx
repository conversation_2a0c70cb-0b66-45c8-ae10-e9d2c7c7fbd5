import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router';
import { Link } from 'react-router-dom';
import { Button, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectZones } from './zones-slice';
import { deleteZone, saveZone, selectZone, setZone } from './detail-slice';
import { createZone } from 'api/models/zones';
import { handleFocus } from 'utils/focus';

export function Detail() {
  const dispatch = useDispatch(),
    navigate = useNavigate(),
    { isInRole } = useAuth(),
    { id } = useParams<{ id: string }>(),
    zones = useSelector(selectZones),
    zone = useSelector(selectZone),
    isNew = !zone._rev,
    canUpdate = (isNew && isInRole('create:zones')) || isInRole('update:zones'),
    canDelete = isInRole('delete:zones');

  useEffect(() => {
    const found = zones.find((p) => p._id === id);
    if (found && found._id !== zone._id) {
      dispatch(setZone(found));
    } else if (id === 'new' && zone._rev) {
      dispatch(setZone(createZone()));
    }
  }, [dispatch, id, zone, zones]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value,
      update = { ...zone, name };

    dispatch(setZone(update));
  };

  const handleIsOffsiteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isOffsite = e.target.checked,
      update = { ...zone, isOffsite };

    dispatch(setZone(update));
  };

  const handleTablesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tables = e.target.valueAsNumber,
      update = { ...zone, tables };

    dispatch(setZone(update));
  };

  const handleSaveClick = async () => {
    const result: any = await dispatch(saveZone());

    if (!result.error) {
      navigate(routes.zones.path);
    }
  };

  const handleDeleteClick = async () => {
    const result: any = await dispatch(deleteZone());

    if (!result.error) {
      navigate(routes.zones.path);
    }
  };

  return (
    <div className="container d-grid gap-2">
      <div className="row">
        <div className="col">
          <Link to={routes.zones.path}>
            <FontAwesomeIcon icon={['fat', 'chevron-left']} />
            &nbsp; Back to Zones List
          </Link>
        </div>
      </div>
      <h1>{isNew ? 'New Zone' : `Zone ${zone.name}`}</h1>
      <div className="row">
        <div className="col-12 col-md-4">
          <label htmlFor="zone-name">Name</label>
          <Input
            id="zone-name"
            value={zone.name}
            onChange={handleNameChange}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-4">
          <label htmlFor="zone-tables">Tables</label>
          <Input
            id="zone-tables"
            type="number"
            value={zone.tables}
            onChange={handleTablesChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-4">
          <label htmlFor="plant-has-lights-out" className="block">
            &nbsp;
          </label>
          <div className="form-check">
            <label htmlFor="zone-is-offsite">Offsite</label>
            <Input
              id="zone-is-offsite"
              type="checkbox"
              checked={zone.isOffsite}
              onChange={handleIsOffsiteChange}
              disabled={!canUpdate}
            />
          </div>
        </div>
      </div>
      <div className="row sticky-bottom bg-white border-top py-2">
        {!isNew && canDelete && (
          <div className="col-auto">
            <Button
              onClick={handleDeleteClick}
              outline
              color="danger"
              size="lg"
              className="me-auto">
              <FontAwesomeIcon icon={['fat', 'trash-alt']} />
              &nbsp; Delete
            </Button>
          </div>
        )}
        <div className="col text-end">
          <Button tag={Link} to={routes.zones.path} outline size="lg">
            {canUpdate ? 'Cancel' : 'Close'}
          </Button>
          {canUpdate && (
            <>
              &nbsp;
              <Button onClick={handleSaveClick} color="success" size="lg">
                <FontAwesomeIcon icon={['fat', 'save']} />
                &nbsp; Save
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default Detail;
