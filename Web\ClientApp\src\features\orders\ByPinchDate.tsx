import React, { Fragment, useCallback, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { Button, Input, InputGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Order } from 'api/models/orders';
import { orderApi } from 'api/order-service';
import { routes } from 'app/routes';
import { OrderRow } from './OrderRow';
import {
  downloadByPinchDate,
  selectDownloading,
  selectEndWeek,
  selectStartWeek,
  selectPlantName,
  setPlantName,
  selectStartDate,
  selectEndDate,
  setStartWeek as setSliceStartWeek,
  setEndWeek as setSliceEndWeek,
} from './orders-slice';
import { equals } from 'utils/equals';
import { parseWeekAndYear } from 'utils/format';
import { handleFocus } from 'utils/focus';
import { weekDisplay } from 'utils/weeks';

function ByPinchDate() {
  const dispatch = useDispatch(),
    downloading = useSelector(selectDownloading),
    startDate = useSelector(selectStartDate),
    endDate = useSelector(selectEndDate),
    sliceStartWeek = useSelector(selectStartWeek),
    [week1, year1] = sliceStartWeek.split('/'),
    sliceEndWeek = useSelector(selectEndWeek),
    [week2, year2] = sliceEndWeek.split('/'),
    plantName = useSelector(selectPlantName),
    [startWeek, setStartWeek] = useState(week1),
    [startYear, setStartYear] = useState(year1),
    [endWeek, setEndWeek] = useState(week2),
    [endYear, setEndYear] = useState(year2),
    [orders, setOrders] = useState<Order[]>([]),
    [plants, setPlants] = useState<string[]>([]);

  const refresh = useCallback(() => {
    if (startDate && endDate) {
      orderApi.byPinchDate(startDate, endDate).then((orders) => {
        const plantNames = orders
            .map((o) => o.plant.name)
            .reduce(
              (memo, p) => {
                if (memo.indexOf(p) === -1) {
                  memo.push(p);
                }
                return memo;
              },
              [plantName] as string[]
            )
            .filter((p) => !!p)
            .sort(),
          filtered = orders.filter(
            (o) => !plantName || equals(o.plant.name, plantName)
          );

        setPlants(plantNames);
        setOrders(filtered);
      });
    }
  }, [startDate, endDate, plantName]);

  useEffect(refresh, [refresh]);

  useEffect(() => {
    const [week1, year1] = sliceStartWeek.split('/'),
      [week2, year2] = sliceEndWeek.split('/');
    setStartWeek(week1);
    setStartYear(year1);
    setEndWeek(week2);
    setEndYear(year2);
  }, [sliceEndWeek, sliceStartWeek]);

  const handleStartWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const startWeek = e.target.value,
      weekAndYear = `${startWeek}/${startYear}`,
      startDate = parseWeekAndYear(weekAndYear);

    setStartWeek(startWeek);

    if (startDate) {
      dispatch(setSliceStartWeek(weekAndYear));
    }
  };

  const handleStartYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const startYear = e.target.value,
      weekAndYear = `${startWeek}/${startYear}`,
      startDate = parseWeekAndYear(weekAndYear);

    setStartYear(startYear);

    if (startDate) {
      dispatch(setSliceStartWeek(weekAndYear));
    }
  };

  const handleEndWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const endWeek = e.target.value,
      weekAndYear = `${endWeek}/${endYear}`,
      endDate = parseWeekAndYear(weekAndYear);

    setEndWeek(endWeek);

    if (endDate) {
      dispatch(setSliceEndWeek(weekAndYear));
    }
  };

  const handleEndYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const endYear = e.target.value,
      weekAndYear = `${endWeek}/${endYear}`,
      endDate = parseWeekAndYear(weekAndYear);

    setEndYear(endYear);

    if (endDate) {
      dispatch(setSliceEndWeek(weekAndYear));
    }
  };

  const handlePlantNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const plantName = e.target.value || null;
    dispatch(setPlantName(plantName));
  };

  const handleDownloadClick = () => {
    if (startDate && endDate) {
      dispatch(
        downloadByPinchDate({ from: startDate, to: endDate, plant: plantName })
      );
    }
  };

  return (
    <div className="container d-grid gap-2">
      <div className="row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow">
        <div className="col-12 row mt-2">
          <div className="col-auto">
            <Link to={routes.orders.path}>
              <FontAwesomeIcon icon={['fat', 'chevron-left']} />
              &nbsp; Back to Orders List
            </Link>
          </div>
          <h1 className="col">
            <FontAwesomeIcon icon={['fat', 'asl-interpreting']} />
            &nbsp; Orders: Pinch Date
          </h1>
          <div className="col-auto">
            <Button
              tag={Link}
              to={routes.orders.routes.byStickDate.path}
              color="link">
              <FontAwesomeIcon icon={['fat', 'seedling']} />
              &nbsp; By Stick Date
            </Button>
          </div>
          <div className="col-auto">
            <Button
              tag={Link}
              to={routes.orders.routes.bySpaceDate.path}
              color="link">
              <FontAwesomeIcon icon={['fat', 'ruler-horizontal']} />
              &nbsp; By Space Date
            </Button>
          </div>
          <div className="col-auto">
            <Button
              tag={Link}
              to={routes.orders.routes.byFlowerDate.path}
              color="link">
              <FontAwesomeIcon icon={['fat', 'flower-daffodil']} />
              &nbsp; By Flower Date
            </Button>
          </div>
        </div>
        <div className="col-12 row">
          <div className="col-auto">
            <label htmlFor="by-flower-date-from">From</label>
            <InputGroup>
              <Input
                id="by-flower-date-from"
                value={startWeek}
                onChange={handleStartWeekChange}
                onFocus={handleFocus}
                className="max-w-75px text-center"
              />
              <Input
                id="by-flower-date-from-year"
                value={startYear}
                onChange={handleStartYearChange}
                onFocus={handleFocus}
                className="max-w-100px text-center"
              />
            </InputGroup>
          </div>
          <div className="col-auto">
            <label htmlFor="by-flower-date-to">To</label>
            <InputGroup>
              <Input
                id="by-flower-date-to"
                value={endWeek}
                onChange={handleEndWeekChange}
                onFocus={handleFocus}
                className="max-w-75px text-center"
              />
              <Input
                id="by-flower-date-to-year"
                value={endYear}
                onChange={handleEndYearChange}
                onFocus={handleFocus}
                className="max-w-100px text-center"
              />
            </InputGroup>
          </div>
          <div className="col-auto">
            <label htmlFor="orders-list-plant-name">Plant</label>
            <Input
              id="orders-list-plant-name"
              type="select"
              value={plantName || ''}
              onChange={handlePlantNameChange}>
              <option value="">All Plants</option>
              {plants.map((plant) => (
                <option key={plant} value={plant}>
                  {plant}
                </option>
              ))}
            </Input>
          </div>
          <div className="col-auto ms-auto">
            <label className="invisible d-block">Download</label>
            <Button
              onClick={handleDownloadClick}
              outline
              color="info"
              disabled={downloading}>
              {downloading && (
                <FontAwesomeIcon icon={['fat', 'spinner']} spin />
              )}
              {!downloading && <FontAwesomeIcon icon={['fat', 'file-excel']} />}
              &nbsp; Download
            </Button>
          </div>
        </div>
      </div>
      <table className="table">
        <thead>
          <tr className="sticky-top bg-white" style={{ top: '201px' }}>
            <th>Batch</th>
            <th>&nbsp;</th>
            <th>Plant</th>
            <th className="text-center">Pots / Cases</th>
            <th className="text-center">
              Tables
              <br />
              (Spaced)
            </th>
            <th className="text-center">Stick Zone</th>
            <th className="text-center">Space Zone</th>
            <th className="text-center">Stick Date</th>
            <th className="text-center">Space Date</th>
            <th className="text-center">Pinch Date</th>
            <th className="text-center">Flower Date</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order, index) => (
            <Fragment key={order._id}>
              {weekDisplay(order.pinchDate) !==
                weekDisplay(orders[index - 1]?.pinchDate) && (
                <tr className="sticky-top" style={{ top: '255px' }}>
                  <th colSpan={12} className="table-light">
                    Pinch {weekDisplay(order.pinchDate)}
                  </th>
                </tr>
              )}
              <OrderRow
                key={order._id}
                order={order}
                showSpacedTables
                showPinchDate
              />
            </Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default ByPinchDate;
