{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n  return target;\n}\nimport { BEGIN_DRAG, DROP, END_DRAG, HOVER, INIT_COORDS } from '../actions/dragDrop/index.js';\nimport { areCoordsEqual } from '../utils/equality.js';\nconst initialState = {\n  initialSourceClientOffset: null,\n  initialClientOffset: null,\n  clientOffset: null\n};\nexport function reduce() {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  let action = arguments.length > 1 ? arguments[1] : undefined;\n  const {\n    payload\n  } = action;\n  switch (action.type) {\n    case INIT_COORDS:\n    case BEGIN_DRAG:\n      return {\n        initialSourceClientOffset: payload.sourceClientOffset,\n        initialClientOffset: payload.clientOffset,\n        clientOffset: payload.clientOffset\n      };\n    case HOVER:\n      if (areCoordsEqual(state.clientOffset, payload.clientOffset)) {\n        return state;\n      }\n      return _objectSpread({}, state, {\n        clientOffset: payload.clientOffset\n      });\n    case END_DRAG:\n    case DROP:\n      return initialState;\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SACCA,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,WAAW,QACL,8BAA8B;AAErC,SAASC,cAAc,QAAQ,sBAAsB;AAQrD,MAAMC,YAAY,GAAU;EAC3BC,yBAAyB,EAAE,IAAI;EAC/BC,mBAAmB,EAAE,IAAI;EACzBC,YAAY,EAAE;CACd;AAED,OAAO,SAASC,MAAM,GAMb;EAAA,IALRC,KAAY,uEAAGL,YAAY;EAAA,IAC3BM,MAGE;EAEF,MAAM;IAAEC;EAAO,CAAE,GAAGD,MAAM;EAC1B,QAAQA,MAAM,CAACE,IAAI;IAClB,KAAKV,WAAW;IAChB,KAAKJ,UAAU;MACd,OAAO;QACNO,yBAAyB,EAAEM,OAAO,CAACE,kBAAkB;QACrDP,mBAAmB,EAAEK,OAAO,CAACJ,YAAY;QACzCA,YAAY,EAAEI,OAAO,CAACJ;OACtB;IACF,KAAKN,KAAK;MACT,IAAIE,cAAc,CAACM,KAAK,CAACF,YAAY,EAAEI,OAAO,CAACJ,YAAY,CAAC,EAAE;QAC7D,OAAOE,KAAK;;MAEb,OAAOK,kBACHL,KAAK;QACRF,YAAY,EAAEI,OAAO,CAACJ;QACtB;IACF,KAAKP,QAAQ;IACb,KAAKD,IAAI;MACR,OAAOK,YAAY;IACpB;MACC,OAAOK,KAAK;EAAA", "names": ["BEGIN_DRAG", "DROP", "END_DRAG", "HOVER", "INIT_COORDS", "areCoordsEqual", "initialState", "initialSourceClientOffset", "initialClientOffset", "clientOffset", "reduce", "state", "action", "payload", "type", "sourceClientOffset", "_objectSpread"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\reducers\\dragOffset.ts"], "sourcesContent": ["import {\n\tBEGIN_DRAG,\n\tDROP,\n\t<PERSON>ND_DRAG,\n\tHOVER,\n\tINIT_COORDS,\n} from '../actions/dragDrop/index.js'\nimport type { Action, XYCoord } from '../interfaces.js'\nimport { areCoordsEqual } from '../utils/equality.js'\n\nexport interface State {\n\tinitialSourceClientOffset: XYCoord | null\n\tinitialClientOffset: XYCoord | null\n\tclientOffset: XYCoord | null\n}\n\nconst initialState: State = {\n\tinitialSourceClientOffset: null,\n\tinitialClientOffset: null,\n\tclientOffset: null,\n}\n\nexport function reduce(\n\tstate: State = initialState,\n\taction: Action<{\n\t\tsourceClientOffset: XYCoord\n\t\tclientOffset: XYCoord\n\t}>,\n): State {\n\tconst { payload } = action\n\tswitch (action.type) {\n\t\tcase INIT_COORDS:\n\t\tcase BEGIN_DRAG:\n\t\t\treturn {\n\t\t\t\tinitialSourceClientOffset: payload.sourceClientOffset,\n\t\t\t\tinitialClientOffset: payload.clientOffset,\n\t\t\t\tclientOffset: payload.clientOffset,\n\t\t\t}\n\t\tcase HOVER:\n\t\t\tif (areCoordsEqual(state.clientOffset, payload.clientOffset)) {\n\t\t\t\treturn state\n\t\t\t}\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tclientOffset: payload.clientOffset,\n\t\t\t}\n\t\tcase END_DRAG:\n\t\tcase DROP:\n\t\t\treturn initialState\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}