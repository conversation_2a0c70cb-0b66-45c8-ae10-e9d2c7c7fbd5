
import { AsyncThunk, createAction, createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { createColour, Colour } from 'api/models/colour';
import { colourApi } from 'api/colour-service';
import { RootState } from 'app/store';
import { ProblemDetails } from 'utils/problem-details';

interface ColourDetailState {
  isLoading: boolean;
  colour: Colour;
  error: ProblemDetails | null;
}

const initialState: ColourDetailState = {
  isLoading: false,
  colour: createColour("White", "#ffffff"),
  error: null
};

export const saveColour: AsyncThunk<Colour, void, {state: RootState}> = createAsyncThunk(
  'colour-detail/save-colour',
  async (_, {rejectWithValue, getState}) => {
    try {
      const colour = (getState() as RootState).colourDetail.colour,
        doc = {...colour};
      
      const updated = await colourApi.save(doc);
      return updated;
    } catch(e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const deleteColour: AsyncThunk<void, void, {state: RootState}> = createAsyncThunk(
  'colour-detail/delete-colour',
  async (_, {rejectWithValue, getState}) => {
    try {
      const colour = (getState() as RootState).colourDetail.colour,
        doc = {...colour};
      
      const updated = await colourApi.delete(doc);
      return updated;
    } catch(e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const savePending = createAction(saveColour.pending.type),
  saveFulfilled = createAction<Colour | undefined>(saveColour.fulfilled.type),
  saveRejected = createAction<ProblemDetails>(saveColour.rejected.type),
  deletePending = createAction(deleteColour.pending.type),
  deleteFulfilled = createAction(deleteColour.fulfilled.type),
  deleteRejected = createAction<ProblemDetails>(deleteColour.rejected.type);

export const colourDetailSlice = createSlice({
  name: 'colour-detail',
  initialState,
  reducers: {
    setColour(state, action: PayloadAction<Colour>) {
      state.colour = action.payload;
    }
  },
  extraReducers: builder =>
    builder
      .addCase(savePending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveFulfilled, (state, action) => {
        state.isLoading = false;
        if(action.payload) {
          state.colour = action.payload;
        }
      })
      .addCase(saveRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deletePending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteFulfilled, (state, action) => {
        state.isLoading = false;
        state.colour = createColour("", "");
      })
      .addCase(deleteRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
});

export const { setColour } = colourDetailSlice.actions;

export const selectColour = (state: RootState) => state.colourDetail.colour;
export const selectIsLoading = (state: RootState) => state.colourDetail.isLoading;
export const selectError = (state: RootState) => state.colourDetail.error;

export default colourDetailSlice.reducer;

