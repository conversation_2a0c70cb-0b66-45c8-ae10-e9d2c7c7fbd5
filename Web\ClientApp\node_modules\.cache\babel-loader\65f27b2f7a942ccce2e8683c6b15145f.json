{"ast": null, "code": "import { shallowEqual } from '@react-dnd/shallowequal';\nimport { isRef } from './isRef.js';\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js';\nexport class TargetConnector {\n  get connectTarget() {\n    return this.dropTarget;\n  }\n  reconnect() {\n    // if nothing has changed then don't resubscribe\n    const didChange = this.didHandlerIdChange() || this.didDropTargetChange() || this.didOptionsChange();\n    if (didChange) {\n      this.disconnectDropTarget();\n    }\n    const dropTarget = this.dropTarget;\n    if (!this.handlerId) {\n      return;\n    }\n    if (!dropTarget) {\n      this.lastConnectedDropTarget = dropTarget;\n      return;\n    }\n    if (didChange) {\n      this.lastConnectedHandlerId = this.handlerId;\n      this.lastConnectedDropTarget = dropTarget;\n      this.lastConnectedDropTargetOptions = this.dropTargetOptions;\n      this.unsubscribeDropTarget = this.backend.connectDropTarget(this.handlerId, dropTarget, this.dropTargetOptions);\n    }\n  }\n  receiveHandlerId(newHandlerId) {\n    if (newHandlerId === this.handlerId) {\n      return;\n    }\n    this.handlerId = newHandlerId;\n    this.reconnect();\n  }\n  get dropTargetOptions() {\n    return this.dropTargetOptionsInternal;\n  }\n  set dropTargetOptions(options) {\n    this.dropTargetOptionsInternal = options;\n  }\n  didHandlerIdChange() {\n    return this.lastConnectedHandlerId !== this.handlerId;\n  }\n  didDropTargetChange() {\n    return this.lastConnectedDropTarget !== this.dropTarget;\n  }\n  didOptionsChange() {\n    return !shallowEqual(this.lastConnectedDropTargetOptions, this.dropTargetOptions);\n  }\n  disconnectDropTarget() {\n    if (this.unsubscribeDropTarget) {\n      this.unsubscribeDropTarget();\n      this.unsubscribeDropTarget = undefined;\n    }\n  }\n  get dropTarget() {\n    return this.dropTargetNode || this.dropTargetRef && this.dropTargetRef.current;\n  }\n  clearDropTarget() {\n    this.dropTargetRef = null;\n    this.dropTargetNode = null;\n  }\n  constructor(backend) {\n    this.hooks = wrapConnectorHooks({\n      dropTarget: (node, options) => {\n        this.clearDropTarget();\n        this.dropTargetOptions = options;\n        if (isRef(node)) {\n          this.dropTargetRef = node;\n        } else {\n          this.dropTargetNode = node;\n        }\n        this.reconnect();\n      }\n    });\n    this.handlerId = null;\n    // The drop target may either be attached via ref or connect function\n    this.dropTargetRef = null;\n    this.dropTargetOptionsInternal = null;\n    this.lastConnectedHandlerId = null;\n    this.lastConnectedDropTarget = null;\n    this.lastConnectedDropTargetOptions = null;\n    this.backend = backend;\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,YAAY,QAAQ,yBAAyB;AAKtD,SAASC,KAAK,QAAQ,YAAY;AAElC,SAASC,kBAAkB,QAAQ,yBAAyB;AAE5D,OAAO,MAAMC,eAAe;EA8B3B,IAAWC,aAAa,GAAQ;IAC/B,OAAO,IAAI,CAACC,UAAU;;EAGvBC,SAAgB,GAAS;IACxB;IACA,MAAMC,SAAS,GACd,IAAI,CAACC,kBAAkB,EAAE,IACzB,IAAI,CAACC,mBAAmB,EAAE,IAC1B,IAAI,CAACC,gBAAgB,EAAE;IAExB,IAAIH,SAAS,EAAE;MACd,IAAI,CAACI,oBAAoB,EAAE;;IAG5B,MAAMN,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACO,SAAS,EAAE;MACpB;;IAED,IAAI,CAACP,UAAU,EAAE;MAChB,IAAI,CAACQ,uBAAuB,GAAGR,UAAU;MACzC;;IAGD,IAAIE,SAAS,EAAE;MACd,IAAI,CAACO,sBAAsB,GAAG,IAAI,CAACF,SAAS;MAC5C,IAAI,CAACC,uBAAuB,GAAGR,UAAU;MACzC,IAAI,CAACU,8BAA8B,GAAG,IAAI,CAACC,iBAAiB;MAE5D,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAC1D,IAAI,CAACP,SAAS,EACdP,UAAU,EACV,IAAI,CAACW,iBAAiB,CACtB;;;EAIHI,gBAAuB,CAACC,YAA+B,EAAQ;IAC9D,IAAIA,YAAY,KAAK,IAAI,CAACT,SAAS,EAAE;MACpC;;IAGD,IAAI,CAACA,SAAS,GAAGS,YAAY;IAC7B,IAAI,CAACf,SAAS,EAAE;;EAGjB,IAAWU,iBAAiB,GAAsB;IACjD,OAAO,IAAI,CAACM,yBAAyB;;EAEtC,IAAWN,iBAAiB,CAACO,OAA0B,EAAE;IACxD,IAAI,CAACD,yBAAyB,GAAGC,OAAO;;EAGzCf,kBAA0B,GAAY;IACrC,OAAO,IAAI,CAACM,sBAAsB,KAAK,IAAI,CAACF,SAAS;;EAGtDH,mBAA2B,GAAY;IACtC,OAAO,IAAI,CAACI,uBAAuB,KAAK,IAAI,CAACR,UAAU;;EAGxDK,gBAAwB,GAAY;IACnC,OAAO,CAACV,YAAY,CACnB,IAAI,CAACe,8BAA8B,EACnC,IAAI,CAACC,iBAAiB,CACtB;;EAGFL,oBAA2B,GAAG;IAC7B,IAAI,IAAI,CAACM,qBAAqB,EAAE;MAC/B,IAAI,CAACA,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,GAAGO,SAAS;;;EAIxC,IAAYnB,UAAU,GAAG;IACxB,OACC,IAAI,CAACoB,cAAc,IAAK,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,OAAO;;EAI1EC,eAAuB,GAAG;IACzB,IAAI,CAACF,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,cAAc,GAAG,IAAI;;EAvF3BI,YAAmBX,OAAgB,EAAE;IAzBrC,KAAOY,KAAK,GAAG5B,kBAAkB,CAAC;MACjCG,UAAU,EAAE,CAAC0B,IAAS,EAAER,OAA0B,KAAK;QACtD,IAAI,CAACK,eAAe,EAAE;QACtB,IAAI,CAACZ,iBAAiB,GAAGO,OAAO;QAChC,IAAItB,KAAK,CAAC8B,IAAI,CAAC,EAAE;UAChB,IAAI,CAACL,aAAa,GAAGK,IAAI;SACzB,MAAM;UACN,IAAI,CAACN,cAAc,GAAGM,IAAI;;QAE3B,IAAI,CAACzB,SAAS,EAAE;;KAEjB,CAAC;IAEF,KAAQM,SAAS,GAAsB,IAAI;IAC3C;IACA,KAAQc,aAAa,GAA0B,IAAI;IAEnD,KAAQJ,yBAAyB,GAA6B,IAAI;IAGlE,KAAQR,sBAAsB,GAAsB,IAAI;IACxD,KAAQD,uBAAuB,GAAQ,IAAI;IAC3C,KAAQE,8BAA8B,GAA6B,IAAI;IAItE,IAAI,CAACG,OAAO,GAAGA,OAAO", "names": ["shallowEqual", "isRef", "wrapConnectorHooks", "TargetConnector", "connectTarget", "drop<PERSON>ar<PERSON>", "reconnect", "<PERSON><PERSON><PERSON><PERSON>", "didHandlerIdChange", "didDropTargetChange", "didOptionsChange", "disconnectDropTarget", "handlerId", "lastConnectedDropTarget", "lastConnectedHandlerId", "lastConnectedDropTargetOptions", "dropTargetOptions", "unsubscribeDropTarget", "backend", "connectDropTarget", "receiveHandlerId", "newHandlerId", "dropTargetOptionsInternal", "options", "undefined", "dropTargetNode", "dropTargetRef", "current", "clearDropTarget", "constructor", "hooks", "node"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\internals\\TargetConnector.ts"], "sourcesContent": ["import { shallowEqual } from '@react-dnd/shallowequal'\nimport type { Backend, Identifier, Unsubscribe } from 'dnd-core'\nimport type { RefObject } from 'react'\n\nimport type { DropTargetOptions } from '../types/index.js'\nimport { isRef } from './isRef.js'\nimport type { Connector } from './SourceConnector.js'\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js'\n\nexport class TargetConnector implements Connector {\n\tpublic hooks = wrapConnectorHooks({\n\t\tdropTarget: (node: any, options: DropTargetOptions) => {\n\t\t\tthis.clearDropTarget()\n\t\t\tthis.dropTargetOptions = options\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dropTargetRef = node\n\t\t\t} else {\n\t\t\t\tthis.dropTargetNode = node\n\t\t\t}\n\t\t\tthis.reconnect()\n\t\t},\n\t})\n\n\tprivate handlerId: Identifier | null = null\n\t// The drop target may either be attached via ref or connect function\n\tprivate dropTargetRef: RefObject<any> | null = null\n\tprivate dropTargetNode: any\n\tprivate dropTargetOptionsInternal: DropTargetOptions | null = null\n\tprivate unsubscribeDropTarget: Unsubscribe | undefined\n\n\tprivate lastConnectedHandlerId: Identifier | null = null\n\tprivate lastConnectedDropTarget: any = null\n\tprivate lastConnectedDropTargetOptions: DropTargetOptions | null = null\n\tprivate readonly backend: Backend\n\n\tpublic constructor(backend: Backend) {\n\t\tthis.backend = backend\n\t}\n\n\tpublic get connectTarget(): any {\n\t\treturn this.dropTarget\n\t}\n\n\tpublic reconnect(): void {\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didDropTargetChange() ||\n\t\t\tthis.didOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDropTarget()\n\t\t}\n\n\t\tconst dropTarget = this.dropTarget\n\t\tif (!this.handlerId) {\n\t\t\treturn\n\t\t}\n\t\tif (!dropTarget) {\n\t\t\tthis.lastConnectedDropTarget = dropTarget\n\t\t\treturn\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDropTarget = dropTarget\n\t\t\tthis.lastConnectedDropTargetOptions = this.dropTargetOptions\n\n\t\t\tthis.unsubscribeDropTarget = this.backend.connectDropTarget(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdropTarget,\n\t\t\t\tthis.dropTargetOptions,\n\t\t\t)\n\t\t}\n\t}\n\n\tpublic receiveHandlerId(newHandlerId: Identifier | null): void {\n\t\tif (newHandlerId === this.handlerId) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.handlerId = newHandlerId\n\t\tthis.reconnect()\n\t}\n\n\tpublic get dropTargetOptions(): DropTargetOptions {\n\t\treturn this.dropTargetOptionsInternal\n\t}\n\tpublic set dropTargetOptions(options: DropTargetOptions) {\n\t\tthis.dropTargetOptionsInternal = options\n\t}\n\n\tprivate didHandlerIdChange(): boolean {\n\t\treturn this.lastConnectedHandlerId !== this.handlerId\n\t}\n\n\tprivate didDropTargetChange(): boolean {\n\t\treturn this.lastConnectedDropTarget !== this.dropTarget\n\t}\n\n\tprivate didOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDropTargetOptions,\n\t\t\tthis.dropTargetOptions,\n\t\t)\n\t}\n\n\tpublic disconnectDropTarget() {\n\t\tif (this.unsubscribeDropTarget) {\n\t\t\tthis.unsubscribeDropTarget()\n\t\t\tthis.unsubscribeDropTarget = undefined\n\t\t}\n\t}\n\n\tprivate get dropTarget() {\n\t\treturn (\n\t\t\tthis.dropTargetNode || (this.dropTargetRef && this.dropTargetRef.current)\n\t\t)\n\t}\n\n\tprivate clearDropTarget() {\n\t\tthis.dropTargetRef = null\n\t\tthis.dropTargetNode = null\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}