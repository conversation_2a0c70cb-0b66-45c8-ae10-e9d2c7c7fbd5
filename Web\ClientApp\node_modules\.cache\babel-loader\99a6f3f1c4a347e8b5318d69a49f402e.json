{"ast": null, "code": "import * as NativeTypes from '../NativeTypes.js';\nimport { getDataFromDataTransfer } from './getDataFromDataTransfer.js';\nexport const nativeTypesConfig = {\n  [NativeTypes.FILE]: {\n    exposeProperties: {\n      files: dataTransfer => Array.prototype.slice.call(dataTransfer.files),\n      items: dataTransfer => dataTransfer.items,\n      dataTransfer: dataTransfer => dataTransfer\n    },\n    matchesTypes: ['Files']\n  },\n  [NativeTypes.HTML]: {\n    exposeProperties: {\n      html: (dataTransfer, matchesTypes) => getDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n      dataTransfer: dataTransfer => dataTransfer\n    },\n    matchesTypes: ['Html', 'text/html']\n  },\n  [NativeTypes.URL]: {\n    exposeProperties: {\n      urls: (dataTransfer, matchesTypes) => getDataFromDataTransfer(dataTransfer, matchesTypes, '').split('\\n'),\n      dataTransfer: dataTransfer => dataTransfer\n    },\n    matchesTypes: ['Url', 'text/uri-list']\n  },\n  [NativeTypes.TEXT]: {\n    exposeProperties: {\n      text: (dataTransfer, matchesTypes) => getDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n      dataTransfer: dataTransfer => dataTransfer\n    },\n    matchesTypes: ['Text', 'text/plain']\n  }\n};", "map": {"version": 3, "mappings": "AAAA,YAAYA,WAAW,MAAM,mBAAmB;AAChD,SAASC,uBAAuB,QAAQ,8BAA8B;AActE,OAAO,MAAMC,iBAAiB,GAE1B;EACH,CAACF,WAAW,CAACG,IAAI,GAAG;IACnBC,gBAAgB,EAAE;MACjBC,KAAK,EAAGC,YAA0B,IACjCC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACJ,YAAY,CAACD,KAAK,CAAC;MAC/CM,KAAK,EAAGL,YAA0B,IACjCA,YAAY,CAACK,KAAK;MACnBL,YAAY,EAAGA,YAA0B,IAAmBA;KAC5D;IACDM,YAAY,EAAE,CAAC,OAAO;GACtB;EACD,CAACZ,WAAW,CAACa,IAAI,GAAG;IACnBT,gBAAgB,EAAE;MACjBU,IAAI,EAAE,CAACR,YAA0B,EAAEM,YAAsB,KACxDX,uBAAuB,CAACK,YAAY,EAAEM,YAAY,EAAE,EAAE,CAAC;MACxDN,YAAY,EAAGA,YAA0B,IAAmBA;KAC5D;IACDM,YAAY,EAAE,CAAC,MAAM,EAAE,WAAW;GAClC;EACD,CAACZ,WAAW,CAACe,GAAG,GAAG;IAClBX,gBAAgB,EAAE;MACjBY,IAAI,EAAE,CAACV,YAA0B,EAAEM,YAAsB,KACxDX,uBAAuB,CAACK,YAAY,EAAEM,YAAY,EAAE,EAAE,CAAC,CAACK,KAAK,CAAC,IAAI,CAAC;MACpEX,YAAY,EAAGA,YAA0B,IAAmBA;KAC5D;IACDM,YAAY,EAAE,CAAC,KAAK,EAAE,eAAe;GACrC;EACD,CAACZ,WAAW,CAACkB,IAAI,GAAG;IACnBd,gBAAgB,EAAE;MACjBe,IAAI,EAAE,CAACb,YAA0B,EAAEM,YAAsB,KACxDX,uBAAuB,CAACK,YAAY,EAAEM,YAAY,EAAE,EAAE,CAAC;MACxDN,YAAY,EAAGA,YAA0B,IAAmBA;KAC5D;IACDM,YAAY,EAAE,CAAC,MAAM,EAAE,YAAY;;CAEpC", "names": ["NativeTypes", "getDataFromDataTransfer", "nativeTypesConfig", "FILE", "exposeProperties", "files", "dataTransfer", "Array", "prototype", "slice", "call", "items", "matchesTypes", "HTML", "html", "URL", "urls", "split", "TEXT", "text"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd-html5-backend\\src\\NativeDragSources\\nativeTypesConfig.ts"], "sourcesContent": ["import * as NativeTypes from '../NativeTypes.js'\nimport { getDataFromDataTransfer } from './getDataFromDataTransfer.js'\n\nexport interface NativeItemConfigExposePropreties {\n\t[property: string]: (\n\t\tdataTransfer: DataTransfer,\n\t\tmatchesTypes: string[],\n\t) => any\n}\n\nexport interface NativeItemConfig {\n\texposeProperties: NativeItemConfigExposePropreties\n\tmatchesTypes: string[]\n}\n\nexport const nativeTypesConfig: {\n\t[key: string]: NativeItemConfig\n} = {\n\t[NativeTypes.FILE]: {\n\t\texposeProperties: {\n\t\t\tfiles: (dataTransfer: DataTransfer): File[] =>\n\t\t\t\tArray.prototype.slice.call(dataTransfer.files),\n\t\t\titems: (dataTransfer: DataTransfer): DataTransferItemList =>\n\t\t\t\tdataTransfer.items,\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Files'],\n\t},\n\t[NativeTypes.HTML]: {\n\t\texposeProperties: {\n\t\t\thtml: (dataTransfer: DataTransfer, matchesTypes: string[]): string =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Html', 'text/html'],\n\t},\n\t[NativeTypes.URL]: {\n\t\texposeProperties: {\n\t\t\turls: (dataTransfer: DataTransfer, matchesTypes: string[]): string[] =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, '').split('\\n'),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Url', 'text/uri-list'],\n\t},\n\t[NativeTypes.TEXT]: {\n\t\texposeProperties: {\n\t\t\ttext: (dataTransfer: DataTransfer, matchesTypes: string[]): string =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Text', 'text/plain'],\n\t},\n}\n"]}, "metadata": {}, "sourceType": "module"}