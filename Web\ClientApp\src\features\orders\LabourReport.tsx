import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalBody,
  ModalFooter,
  FormGroup,
  Input,
} from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { reportsApi } from 'api/reports-service';
import { Error } from 'features/error/Error';
import { createProblemDetails, ProblemDetails } from 'utils/problem-details';

interface LabourReportProps {
  show: boolean;
  onClose: () => void;
}

export function LabourReport({ show, onClose }: LabourReportProps) {
  const [year, setYear] = useState(new Date().getFullYear().toString()),
    [error, setError] = useState<ProblemDetails | null>(null);

  const handleYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setYear(e.target.value);
  };

  const handleDownloadClick = async () => {
    try {
      setError(null);
      const yearInt = parseInt(year);
      if (!yearInt) {
        return setError(createProblemDetails('Please choose a valid year.'));
      }

      await reportsApi.labourReport(parseInt(year));
      onClose();
    } catch (e) {
      setError(e as ProblemDetails);
    }
  };

  const handleCancelClick = () => {
    onClose();
  };

  const handleClearErrorCLick = () => {
    setError(null);
  };

  return (
    <Modal isOpen={show} toggle={handleCancelClick} autoFocus={false}>
      <ModalHeader toggle={handleCancelClick}>Labour Hours Report</ModalHeader>
      <ModalBody>
        <p className="text-center lead">
          This will download an Excel file comparing Labour Hours for the
          selected year with the prior year.
        </p>
        <FormGroup className="max-w-120px mx-auto">
          <label htmlFor="year">Year</label>
          <Input
            id="year"
            type="number"
            bsSize="lg"
            className="text-center"
            value={year}
            onChange={handleYearChange}
            autoFocus
          />
        </FormGroup>
        <Error error={error} clearError={handleClearErrorCLick} />
      </ModalBody>
      <ModalFooter>
        <Button outline onClick={handleCancelClick}>
          Cancel
        </Button>
        <Button color="primary" outline onClick={handleDownloadClick}>
          <FontAwesomeIcon icon={['fat', 'chart-line']} />
          &nbsp; Download
        </Button>
      </ModalFooter>
    </Modal>
  );
}
