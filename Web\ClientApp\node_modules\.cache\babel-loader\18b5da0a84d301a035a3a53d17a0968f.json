{"ast": null, "code": "import { RawTask } from './RawTask.js';\nexport class TaskFactory {\n  create(task) {\n    const tasks = this.freeTasks;\n    const t1 = tasks.length ? tasks.pop() : new RawTask(this.onError, t => tasks[tasks.length] = t);\n    t1.task = task;\n    return t1;\n  }\n  constructor(onError) {\n    this.onError = onError;\n    this.freeTasks = [];\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AAGtC,OAAO,MAAMC,WAAW;EAKvBC,MAAa,CAACC,IAAgB,EAAQ;IACrC,MAAMC,KAAK,GAAG,IAAI,CAACC,SAAS;IAC5B,MAAMC,EAAC,GAAGF,KAAK,CAACG,MAAM,GAClBH,KAAK,CAACI,GAAG,EAAE,GACZ,IAAIR,OAAO,CAAC,IAAI,CAACS,OAAO,EAAGH,CAAC,IAAMF,KAAK,CAACA,KAAK,CAACG,MAAM,CAAC,GAAGD,CAAC,CAAE;IAC9DA,EAAC,CAACH,IAAI,GAAGA,IAAI;IACb,OAAOG,EAAC;;EARTI,YAA2BD,OAA2B,EAAE;SAA7BA,OAA2B,GAA3BA,OAA2B;SAF9CJ,SAAS,GAAc,EAAE", "names": ["RawTask", "TaskFactory", "create", "task", "tasks", "freeTasks", "t", "length", "pop", "onError", "constructor"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\@react-dnd\\asap\\src\\TaskFactory.ts"], "sourcesContent": ["import { RawTask } from './RawTask.js'\nimport type { Task } from './types.js'\n\nexport class TaskFactory {\n\tprivate freeTasks: RawTask[] = []\n\n\tpublic constructor(private onError: (err: any) => void) {}\n\n\tpublic create(task: () => void): Task {\n\t\tconst tasks = this.freeTasks\n\t\tconst t = tasks.length\n\t\t\t? (tasks.pop() as RawTask)\n\t\t\t: new RawTask(this.onError, (t) => (tasks[tasks.length] = t))\n\t\tt.task = task\n\t\treturn t\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}