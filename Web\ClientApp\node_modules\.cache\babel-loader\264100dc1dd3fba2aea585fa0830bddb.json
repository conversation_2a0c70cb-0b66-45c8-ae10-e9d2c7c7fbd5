{"ast": null, "code": "import { useEffect, useMemo } from 'react';\nimport { DropTargetImpl } from './DropTargetImpl.js';\nexport function useDropTarget(spec, monitor) {\n  const dropTarget = useMemo(() => new DropTargetImpl(spec, monitor), [monitor]);\n  useEffect(() => {\n    dropTarget.spec = spec;\n  }, [spec]);\n  return dropTarget;\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAI1C,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,OAAO,SAASC,aAAa,CAC5BC,IAAiC,EACjCC,OAAgC,EAC/B;EACD,MAAMC,UAAU,GAAGL,OAAO,CAAC,MAAM,IAAIC,cAAc,CAACE,IAAI,EAAEC,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAC9EL,SAAS,CAAC,MAAM;IACfM,UAAU,CAACF,IAAI,GAAGA,IAAI;GACtB,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,OAAOE,UAAU", "names": ["useEffect", "useMemo", "DropTargetImpl", "useDropTarget", "spec", "monitor", "drop<PERSON>ar<PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrop\\useDropTarget.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react'\n\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\nimport { DropTargetImpl } from './DropTargetImpl.js'\n\nexport function useDropTarget<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n\tmonitor: DropTargetMonitor<O, R>,\n) {\n\tconst dropTarget = useMemo(() => new DropTargetImpl(spec, monitor), [monitor])\n\tuseEffect(() => {\n\t\tdropTarget.spec = spec\n\t}, [spec])\n\treturn dropTarget\n}\n"]}, "metadata": {}, "sourceType": "module"}