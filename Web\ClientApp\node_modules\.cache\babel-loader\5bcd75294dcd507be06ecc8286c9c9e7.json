{"ast": null, "code": "import { NativeDragSource } from './NativeDragSource.js';\nimport { nativeTypesConfig } from './nativeTypesConfig.js';\nexport function createNativeDragSource(type, dataTransfer) {\n  const config = nativeTypesConfig[type];\n  if (!config) {\n    throw new Error(`native type ${type} has no configuration`);\n  }\n  const result = new NativeDragSource(config);\n  result.loadDataTransfer(dataTransfer);\n  return result;\n}\nexport function matchNativeItemType(dataTransfer) {\n  if (!dataTransfer) {\n    return null;\n  }\n  const dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || []);\n  return Object.keys(nativeTypesConfig).filter(nativeItemType => {\n    const typeConfig = nativeTypesConfig[nativeItemType];\n    if (!(typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.matchesTypes)) {\n      return false;\n    }\n    return typeConfig.matchesTypes.some(t => dataTransferTypes.indexOf(t) > -1);\n  })[0] || null;\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,iBAAiB,QAAQ,wBAAwB;AAE1D,OAAO,SAASC,sBAAsB,CACrCC,IAAY,EACZC,YAA2B,EACR;EACnB,MAAMC,MAAM,GAAGJ,iBAAiB,CAACE,IAAI,CAAC;EACtC,IAAI,CAACE,MAAM,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAE,eAAcH,IAAK,uBAAsB,CAAC;;EAE5D,MAAMI,MAAM,GAAG,IAAIP,gBAAgB,CAACK,MAAM,CAAC;EAC3CE,MAAM,CAACC,gBAAgB,CAACJ,YAAY,CAAC;EACrC,OAAOG,MAAM;;AAGd,OAAO,SAASE,mBAAmB,CAClCL,YAAiC,EACjB;EAChB,IAAI,CAACA,YAAY,EAAE;IAClB,OAAO,IAAI;;EAGZ,MAAMM,iBAAiB,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACV,YAAY,CAACW,KAAK,IAAI,EAAE,CAAC;EAC9E,OACCC,MAAM,CAACC,IAAI,CAAChB,iBAAiB,CAAC,CAACiB,MAAM,CAAEC,cAAc,IAAK;IACzD,MAAMC,UAAU,GAAGnB,iBAAiB,CAACkB,cAAc,CAAC;IACpD,IAAI,EAACC,UAAU,aAAVA,UAAU,WAAc,GAAxBA,MAAwB,GAAxBA,UAAU,CAAEC,YAAY,GAAE;MAC9B,OAAO,KAAK;;IAEb,OAAOD,UAAU,CAACC,YAAY,CAACC,IAAI,CACjCC,CAAC,IAAKb,iBAAiB,CAACc,OAAO,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC,CACxC;GACD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI", "names": ["NativeDragSource", "nativeTypesConfig", "createNativeDragSource", "type", "dataTransfer", "config", "Error", "result", "loadDataTransfer", "matchNativeItemType", "dataTransferTypes", "Array", "prototype", "slice", "call", "types", "Object", "keys", "filter", "nativeItemType", "typeConfig", "matchesTypes", "some", "t", "indexOf"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd-html5-backend\\src\\NativeDragSources\\index.ts"], "sourcesContent": ["import { NativeDragSource } from './NativeDragSource.js'\nimport { nativeTypesConfig } from './nativeTypesConfig.js'\n\nexport function createNativeDragSource(\n\ttype: string,\n\tdataTransfer?: DataTransfer,\n): NativeDragSource {\n\tconst config = nativeTypesConfig[type]\n\tif (!config) {\n\t\tthrow new Error(`native type ${type} has no configuration`)\n\t}\n\tconst result = new NativeDragSource(config)\n\tresult.loadDataTransfer(dataTransfer)\n\treturn result\n}\n\nexport function matchNativeItemType(\n\tdataTransfer: DataTransfer | null,\n): string | null {\n\tif (!dataTransfer) {\n\t\treturn null\n\t}\n\n\tconst dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || [])\n\treturn (\n\t\tObject.keys(nativeTypesConfig).filter((nativeItemType) => {\n\t\t\tconst typeConfig = nativeTypesConfig[nativeItemType]\n\t\t\tif (!typeConfig?.matchesTypes) {\n\t\t\t\treturn false\n\t\t\t}\n\t\t\treturn typeConfig.matchesTypes.some(\n\t\t\t\t(t) => dataTransferTypes.indexOf(t) > -1,\n\t\t\t)\n\t\t})[0] || null\n\t)\n}\n"]}, "metadata": {}, "sourceType": "module"}