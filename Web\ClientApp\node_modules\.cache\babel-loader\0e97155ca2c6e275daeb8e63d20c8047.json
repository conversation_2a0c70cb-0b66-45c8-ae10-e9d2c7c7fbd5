{"ast": null, "code": "import { useEffect, useMemo } from 'react';\nimport { DragSourceImpl } from './DragSourceImpl.js';\nexport function useDragSource(spec, monitor, connector) {\n  const handler = useMemo(() => new DragSourceImpl(spec, monitor, connector), [monitor, connector]);\n  useEffect(() => {\n    handler.spec = spec;\n  }, [spec]);\n  return handler;\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAK1C,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,OAAO,SAASC,aAAa,CAC5BC,IAAiC,EACjCC,OAAgC,EAChCC,SAAoB,EACnB;EACD,MAAMC,OAAO,GAAGN,OAAO,CACtB,MAAM,IAAIC,cAAc,CAACE,IAAI,EAAEC,OAAO,EAAEC,SAAS,CAAC,EAClD,CAACD,OAAO,EAAEC,SAAS,CAAC,CACpB;EACDN,SAAS,CAAC,MAAM;IACfO,OAAO,CAACH,IAAI,GAAGA,IAAI;GACnB,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,OAAOG,OAAO", "names": ["useEffect", "useMemo", "DragSourceImpl", "useDragSource", "spec", "monitor", "connector", "handler"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrag\\useDragSource.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react'\n\nimport type { Connector } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragSourceHookSpec } from '../types.js'\nimport { DragSourceImpl } from './DragSourceImpl.js'\n\nexport function useDragSource<O, R, P>(\n\tspec: DragSourceHookSpec<O, R, P>,\n\tmonitor: DragSourceMonitor<O, R>,\n\tconnector: Connector,\n) {\n\tconst handler = useMemo(\n\t\t() => new DragSourceImpl(spec, monitor, connector),\n\t\t[monitor, connector],\n\t)\n\tuseEffect(() => {\n\t\thandler.spec = spec\n\t}, [spec])\n\treturn handler\n}\n"]}, "metadata": {}, "sourceType": "module"}