{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { plantApi } from 'api/plant-service';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const savePlants = createAsyncThunk('plants/save-plants', async (plants, _ref) => {\n  let {\n    rejectWithValue\n  } = _ref;\n  try {\n    const updatedPlants = await plantApi.saveAll(plants);\n    return updatedPlants;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    },\n    moveItem(state, action) {\n      const {\n        movingItem,\n        existingItem\n      } = action.payload;\n      console.log(\"moving item\", existingItem, movingItem);\n      if (existingItem.stickingSortOrder == null) {\n        // generate a new order for all items based on current index\n        state.plants = state.plants.sort(sortPlant).map((p, index) => ({\n          ...p,\n          stickingSortOrder: index\n        }));\n      }\n      const existingItemIndex = state.plants.findIndex(p => p._id === existingItem._id);\n\n      // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1\n      state.plants = state.plants.map(p => {\n        if (p._id === existingItem._id) {\n          return {\n            ...p,\n            stickingSortOrder: existingItemIndex + 1\n          };\n        } else if (p._id === movingItem._id) {\n          return {\n            ...p,\n            stickingSortOrder: existingItemIndex\n          };\n        } else if (p.stickingSortOrder != null && p.stickingSortOrder > 1) {\n          return {\n            ...p,\n            stickingSortOrder: p.stickingSortOrder + 1\n          };\n        }\n        return p;\n      });\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(savePlants.fulfilled, (state, action) => {\n      // Update the plants in state with the saved versions (which have updated _rev values)\n      const savedPlants = action.payload;\n      state.plants = state.plants.map(plant => {\n        const savedPlant = savedPlants.find(sp => sp._id === plant._id);\n        return savedPlant || plant;\n      });\n    });\n  }\n});\nexport const {\n  setPlants,\n  moveItem\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nfunction sortByStickingSortOrder(a, b) {\n  // Handle null values - place them last\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1; // b comes before a (non-null before null)\n\n  // Both are non-null, compare normally\n  return a.stickingSortOrder - b.stickingSortOrder;\n}\nfunction sortPlant(a, b) {\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "plantApi", "sortBy", "sortSizeName", "initialState", "plants", "savePlants", "rejectWithValue", "updatedPlants", "saveAll", "e", "plantsSlice", "name", "reducers", "setPlants", "state", "action", "payload", "moveItem", "movingItem", "existingItem", "console", "log", "stickingSortOrder", "sort", "sortPlant", "map", "p", "index", "existingItemIndex", "findIndex", "_id", "extraReducers", "builder", "addCase", "fulfilled", "savedPlants", "plant", "savedPlant", "find", "sp", "actions", "selectPlants", "reducer", "sortByCrop", "sortByStickingSortOrder", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { plantApi } from 'api/plant-service';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const savePlants = createAsyncThunk<Plant[], Plant[], { state: RootState }>(\r\n  'plants/save-plants',\r\n  async (plants, { rejectWithValue }) => {\r\n    try {\r\n      const updatedPlants = await plantApi.saveAll(plants);\r\n      return updatedPlants;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    },\r\n    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {\r\n      const { movingItem, existingItem } = action.payload;\r\n\r\n      console.log(\"moving item\", existingItem, movingItem);\r\n\r\n      if (existingItem.stickingSortOrder == null) {\r\n        // generate a new order for all items based on current index\r\n        state.plants = state.plants.sort(sortPlant).map((p, index) => ({ ...p, stickingSortOrder: index }));\r\n      }\r\n\r\n      const existingItemIndex = state.plants.findIndex(p => p._id === existingItem._id);\r\n\r\n      // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1\r\n      state.plants = state.plants.map(p => {\r\n        if (p._id === existingItem._id) {\r\n          return { ...p, stickingSortOrder: existingItemIndex + 1 };\r\n        } else if (p._id === movingItem._id) {\r\n          return { ...p, stickingSortOrder: existingItemIndex };\r\n        } else if (p.stickingSortOrder != null && p.stickingSortOrder > 1) {\r\n          return { ...p, stickingSortOrder: p.stickingSortOrder + 1 };\r\n        }\r\n        return p;\r\n      });\r\n    }\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder.addCase(savePlants.fulfilled, (state, action) => {\r\n      // Update the plants in state with the saved versions (which have updated _rev values)\r\n      const savedPlants = action.payload;\r\n      state.plants = state.plants.map(plant => {\r\n        const savedPlant = savedPlants.find(sp => sp._id === plant._id);\r\n        return savedPlant || plant;\r\n      });\r\n    });\r\n  }\r\n});\r\n\r\nexport const { setPlants, moveItem } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\n\r\nfunction sortByStickingSortOrder(a: Plant, b: Plant) {\r\n  // Handle null values - place them last\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\r\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1;  // b comes before a (non-null before null)\r\n\r\n  // Both are non-null, compare normally\r\n  return a.stickingSortOrder! - b.stickingSortOrder!;\r\n}\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,EAAiBC,gBAAgB,QAAQ,kBAAkB;AAE/E,SAASC,QAAQ,QAAQ,mBAAmB;AAE5C,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAOjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGN,gBAAgB,CACxC,oBAAoB,EACpB,OAAOK,MAAM,WAA0B;EAAA,IAAxB;IAAEE;EAAgB,CAAC;EAChC,IAAI;IACF,MAAMC,aAAa,GAAG,MAAMP,QAAQ,CAACQ,OAAO,CAACJ,MAAM,CAAC;IACpD,OAAOG,aAAa;EACtB,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAOH,eAAe,CAACG,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,WAAW,GAAGZ,WAAW,CAAC;EACrCa,IAAI,EAAE,QAAQ;EACdR,YAAY;EACZS,QAAQ,EAAE;IACRC,SAAS,CAACC,KAAK,EAAEC,MAA8B,EAAE;MAC/CD,KAAK,CAACV,MAAM,GAAGW,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDC,QAAQ,CAACH,KAAK,EAAEC,MAAiE,EAAE;MACjF,MAAM;QAAEG,UAAU;QAAEC;MAAa,CAAC,GAAGJ,MAAM,CAACC,OAAO;MAEnDI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,YAAY,EAAED,UAAU,CAAC;MAEpD,IAAIC,YAAY,CAACG,iBAAiB,IAAI,IAAI,EAAE;QAC1C;QACAR,KAAK,CAACV,MAAM,GAAGU,KAAK,CAACV,MAAM,CAACmB,IAAI,CAACC,SAAS,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,MAAM;UAAE,GAAGD,CAAC;UAAEJ,iBAAiB,EAAEK;QAAM,CAAC,CAAC,CAAC;MACrG;MAEA,MAAMC,iBAAiB,GAAGd,KAAK,CAACV,MAAM,CAACyB,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,GAAG,KAAKX,YAAY,CAACW,GAAG,CAAC;;MAEjF;MACAhB,KAAK,CAACV,MAAM,GAAGU,KAAK,CAACV,MAAM,CAACqB,GAAG,CAACC,CAAC,IAAI;QACnC,IAAIA,CAAC,CAACI,GAAG,KAAKX,YAAY,CAACW,GAAG,EAAE;UAC9B,OAAO;YAAE,GAAGJ,CAAC;YAAEJ,iBAAiB,EAAEM,iBAAiB,GAAG;UAAE,CAAC;QAC3D,CAAC,MAAM,IAAIF,CAAC,CAACI,GAAG,KAAKZ,UAAU,CAACY,GAAG,EAAE;UACnC,OAAO;YAAE,GAAGJ,CAAC;YAAEJ,iBAAiB,EAAEM;UAAkB,CAAC;QACvD,CAAC,MAAM,IAAIF,CAAC,CAACJ,iBAAiB,IAAI,IAAI,IAAII,CAAC,CAACJ,iBAAiB,GAAG,CAAC,EAAE;UACjE,OAAO;YAAE,GAAGI,CAAC;YAAEJ,iBAAiB,EAAEI,CAAC,CAACJ,iBAAiB,GAAG;UAAE,CAAC;QAC7D;QACA,OAAOI,CAAC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACDK,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CAACC,OAAO,CAAC5B,UAAU,CAAC6B,SAAS,EAAE,CAACpB,KAAK,EAAEC,MAAM,KAAK;MACvD;MACA,MAAMoB,WAAW,GAAGpB,MAAM,CAACC,OAAO;MAClCF,KAAK,CAACV,MAAM,GAAGU,KAAK,CAACV,MAAM,CAACqB,GAAG,CAACW,KAAK,IAAI;QACvC,MAAMC,UAAU,GAAGF,WAAW,CAACG,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACT,GAAG,KAAKM,KAAK,CAACN,GAAG,CAAC;QAC/D,OAAOO,UAAU,IAAID,KAAK;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEvB,SAAS;EAAEI;AAAS,CAAC,GAAGP,WAAW,CAAC8B,OAAO;AAE1D,OAAO,MAAMC,YAAY,GAAI3B,KAAgB,IAAKA,KAAK,CAACV,MAAM,CAACA,MAAM,CAACqB,GAAG,CAACC,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACH,IAAI,CAACC,SAAS,CAAC;AAExG,eAAed,WAAW,CAACgC,OAAO;AAElC,MAAMC,UAAU,GAAG1C,MAAM,CAAC,MAAM,CAAC;AAEjC,SAAS2C,uBAAuB,CAACC,CAAQ,EAAEC,CAAQ,EAAE;EACnD;EACA,IAAID,CAAC,CAACvB,iBAAiB,IAAI,IAAI,IAAIwB,CAAC,CAACxB,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC;EACxE,IAAIuB,CAAC,CAACvB,iBAAiB,IAAI,IAAI,IAAIwB,CAAC,CAACxB,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3E,IAAIuB,CAAC,CAACvB,iBAAiB,IAAI,IAAI,IAAIwB,CAAC,CAACxB,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAE;;EAE3E;EACA,OAAOuB,CAAC,CAACvB,iBAAiB,GAAIwB,CAAC,CAACxB,iBAAkB;AACpD;AAEA,SAASE,SAAS,CAACqB,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,uBAAuB,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIH,UAAU,CAACE,CAAC,EAAEC,CAAC,CAAC,IAAI5C,YAAY,CAAC2C,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AAC1F"}, "metadata": {}, "sourceType": "module"}