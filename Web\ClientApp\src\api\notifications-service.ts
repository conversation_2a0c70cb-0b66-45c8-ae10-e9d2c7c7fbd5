import { api } from './api-base';
import { getBasicAuth } from './auth-service';
import { UserInfo } from './models/auth';
import * as models from './models/notifications';

export interface OrderChangedArgs {
  model: models.OrderChangedModel;
  user: UserInfo;
}

class NotificationsServer {
  orderChanged({model, user}: OrderChangedArgs) {
    const authorization = getBasicAuth(user.name, user.password),
      headers = { Authorization: authorization};
      
    api.post('/notifications/order', model, { headers });
  }
}

export const notificationsApi = new NotificationsServer();
