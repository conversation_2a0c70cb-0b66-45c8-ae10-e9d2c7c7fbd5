import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  Button,
  Input,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledTooltip,
  UncontrolledDropdown,
} from 'reactstrap';
import moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { driverTasksApi } from 'api/driver-tasks-service';
import * as models from 'api/models/driver-tasks';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { classNames } from 'utils/class-names';
import { formatDate } from 'utils/format';
import { ProblemDetails } from 'utils/problem-details';
import { selectDrivers } from './driver-task-slice';
import { setError } from './driver-task-slice';

interface ListItemProps {
  task: models.DriverTask;
}

export function ListItem({ task }: ListItemProps) {
  const dispatch = useDispatch(),
    drivers = useSelector(selectDrivers),
    { user, isInRole } = useAuth(),
    canCreate = isInRole('create:driver-tasks'),
    isDriver = drivers.some((d) => d.name === user?.name),
    isUnassigned = !task.assignedTo,
    isAssignedToUser =
      isDriver && !isUnassigned && task.assignedTo?.name === user?.name,
    canAssign = canCreate || (isDriver && (isUnassigned || isAssignedToUser)),
    canSetStatus = canCreate || isAssignedToUser,
    today = moment().startOf('day'),
    dueDate = moment(task.dueDate),
    isToday = dueDate.isSame(today, 'day'),
    isOverdue = dueDate.isBefore(today),
    progressIcon: IconProp =
      task.status === 'Not Started'
        ? 'clock'
        : task.status === 'In Progress'
        ? 'person-running'
        : 'square-check';

  const handleAssignedToChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const name = e.target.value,
      assignedTo = name ? drivers.find((d) => d.name === name) || null : null,
      updated = { ...task, assignedTo };

    try {
      await driverTasksApi.updateDriverTask(updated);
    } catch (e) {
      dispatch(setError(e as ProblemDetails));
    }
  };

  const handleStatusChange = async (status: models.Status) => {
    const updated = { ...task, status };

    try {
      await driverTasksApi.updateDriverTask(updated);
    } catch (e) {
      dispatch(setError(e as ProblemDetails));
    }
  };

  return (
    <div className="col">
      <div className={classNames('card h-100', isOverdue && 'border-danger')}>
        <div className="card-header">
          <div className="row">
            <h5
              className={classNames(
                'card-title col',
                isToday && 'fw-bold',
                task.priority === 'Low' && 'text-muted'
              )}>
              {task.priority === 'High' && (
                <>
                  <UncontrolledTooltip
                    target={`driver-task-priority-${task._id}`}>
                    This task is high priority.
                  </UncontrolledTooltip>
                  <span
                    id={`driver-task-priority-${task._id}`}
                    className="cursor-pointer">
                    🔥&nbsp;
                  </span>
                </>
              )}
              {task.priority === 'Low' && (
                <>
                  <UncontrolledTooltip
                    target={`driver-task-priority-${task._id}`}>
                    This task is low priority.
                  </UncontrolledTooltip>
                  <span
                    id={`driver-task-priority-${task._id}`}
                    className="cursor-pointer">
                    <FontAwesomeIcon icon={['fal', 'arrow-down']} />
                  </span>
                </>
              )}
              {formatDate(task.dueDate, 'ddd, MMM D')}
            </h5>
            <div className="col-auto">
              <Link
                to={routes.driverTasks.detail.to(task._id)}
                className="d-flex align-self-end">
                Edit
              </Link>
            </div>
          </div>
        </div>
        <div className="card-body d-flex flex-column text-center">
          <h6 className="card-subtitle mb-2 text-muted text-capitalize">
            {!canSetStatus && (
              <>
                <UncontrolledTooltip target={`driver-task-status-${task._id}`}>
                  Task is {task.status}
                </UncontrolledTooltip>
                <Button
                  id={`driver-task-status-${task._id}`}
                  color="link"
                  className="btn-secondary py-1 mb-1">
                  <FontAwesomeIcon
                    icon={['fat', progressIcon]}
                    fixedWidth
                    className="cursor-pointer"
                  />
                </Button>
              </>
            )}
            {canSetStatus && (
              <UncontrolledDropdown className="d-inline">
                <DropdownToggle
                  color="primary"
                  outline
                  caret
                  className="py-1 mb-1">
                  <UncontrolledTooltip
                    target={`driver-task-status-${task._id}`}>
                    Task is {task.status}
                  </UncontrolledTooltip>
                  <FontAwesomeIcon
                    id={`driver-task-status-${task._id}`}
                    icon={['fat', progressIcon]}
                    fixedWidth
                    className="cursor-pointer"
                  />
                </DropdownToggle>
                <DropdownMenu>
                  <DropdownItem
                    onClick={() => handleStatusChange('Not Started')}>
                    <FontAwesomeIcon icon={['fat', 'clock']} fixedWidth />
                    &nbsp; Not Started
                  </DropdownItem>
                  <DropdownItem
                    onClick={() => handleStatusChange('In Progress')}>
                    <FontAwesomeIcon
                      icon={['fat', 'person-running']}
                      fixedWidth
                    />
                    &nbsp; In Progress
                  </DropdownItem>
                  <DropdownItem onClick={() => handleStatusChange('Complete')}>
                    <FontAwesomeIcon
                      icon={['fat', 'square-check']}
                      fixedWidth
                    />
                    &nbsp; Complete
                  </DropdownItem>
                </DropdownMenu>
              </UncontrolledDropdown>
            )}
            &nbsp;
            {!canAssign && (
              <Input
                value={task.assignedTo?.name || ''}
                readOnly
                plaintext
                className="w-auto d-inline-block text-capitalize"
              />
            )}
            {canAssign && (
              <Input
                value={task.assignedTo?.name || ''}
                onChange={handleAssignedToChange}
                type="select"
                className="w-auto d-inline-block">
                <option value="">Unassigned</option>
                {canCreate &&
                  drivers.map((driver) => (
                    <option
                      key={driver.name}
                      value={driver.name}
                      className="text-capitalize">
                      {driver.name}
                    </option>
                  ))}
                {!canCreate && user && (
                  <option value={user.name} className="text-capitalize">
                    {user.name}
                  </option>
                )}
              </Input>
            )}
          </h6>
          <p className="card-text text-start small flex flex-grow-1">
            {task.notes}
          </p>
          <div className="row">
            <div className="col text-end text-truncate">
              {task.fromLocation}
            </div>
            <div className="col-auto text-center">
              <FontAwesomeIcon icon={['fat', 'arrow-right']} />
            </div>
            <div className="col text-start text-truncate">
              {task.toLocation}
            </div>
          </div>
        </div>
        <div className="card-footer">
          <div className="row small text-muted">
            <div className="col text-capitalize text-end">
              Created by {task.createdBy}
            </div>
            <div className="col text-start">
              {`${formatDate(task.createdOn, 'MMM D')} @ ${formatDate(
                task.createdOn,
                'h:mm a'
              )}`}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
