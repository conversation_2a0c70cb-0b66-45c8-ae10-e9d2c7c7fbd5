{"ast": null, "code": "import { createContext } from 'react';\n/**\n * Create the React Context\n */\nexport const DndContext = createContext({\n  dragDropManager: undefined\n});", "map": {"version": 3, "mappings": "AACA,SAASA,aAAa,QAAQ,OAAO;AASrC;;;AAGA,OAAO,MAAMC,UAAU,GAAGD,aAAa,CAAiB;EACvDE,eAAe,EAAEC;CACjB,CAAC", "names": ["createContext", "DndContext", "dragDropManager", "undefined"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\core\\DndContext.ts"], "sourcesContent": ["import type { DragDropManager } from 'dnd-core'\nimport { createContext } from 'react'\n\n/**\n * The React context type\n */\nexport interface DndContextType {\n\tdragDropManager: DragDropManager | undefined\n}\n\n/**\n * Create the React Context\n */\nexport const DndContext = createContext<DndContextType>({\n\tdragDropManager: undefined,\n})\n"]}, "metadata": {}, "sourceType": "module"}