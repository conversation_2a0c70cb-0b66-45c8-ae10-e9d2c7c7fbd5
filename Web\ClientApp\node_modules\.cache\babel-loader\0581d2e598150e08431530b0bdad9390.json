{"ast": null, "code": "import { ADD_SOURCE, ADD_TARGET, REMOVE_SOURCE, REMOVE_TARGET } from '../actions/registry.js';\nexport function reduce() {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  let action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case ADD_SOURCE:\n    case ADD_TARGET:\n      return state + 1;\n    case REMOVE_SOURCE:\n    case REMOVE_TARGET:\n      return state - 1;\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SACCA,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,aAAa,QACP,wBAAwB;AAK/B,OAAO,SAASC,MAAM,GAA+C;EAAA,IAA9CC,KAAY,uEAAG,CAAC;EAAA,IAAEC,MAAmB;EAC3D,QAAQA,MAAM,CAACC,IAAI;IAClB,KAAKP,UAAU;IACf,KAAKC,UAAU;MACd,OAAOI,KAAK,GAAG,CAAC;IACjB,KAAKH,aAAa;IAClB,KAAKC,aAAa;MACjB,OAAOE,KAAK,GAAG,CAAC;IACjB;MACC,OAAOA,KAAK;EAAA", "names": ["ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "reduce", "state", "action", "type"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\reducers\\refCount.ts"], "sourcesContent": ["import {\n\tADD_SOURCE,\n\tADD_TARGET,\n\tREMOVE_SOURCE,\n\tREMOVE_TARGET,\n} from '../actions/registry.js'\nimport type { Action } from '../interfaces.js'\n\nexport type State = number\n\nexport function reduce(state: State = 0, action: Action<any>): State {\n\tswitch (action.type) {\n\t\tcase ADD_SOURCE:\n\t\tcase ADD_TARGET:\n\t\t\treturn state + 1\n\t\tcase REMOVE_SOURCE:\n\t\tcase REMOVE_TARGET:\n\t\t\treturn state - 1\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}