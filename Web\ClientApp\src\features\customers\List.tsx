import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectCustomers } from './customers-slice';

export function List() {
  const customers = useSelector(selectCustomers),
    {isInRole} = useAuth(),
    canCreate = isInRole('create:customers');

  return (
    <div className="container d-grid gap-2">
      <div className="row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow">
        <h1 className="col pt-2">
          <FontAwesomeIcon icon={['fat', 'user-tie']} />
          &nbsp;
          Customer List
        </h1>
        {canCreate &&
          <div className="col-auto pt-3">
            <Button tag={Link} to={routes.customers.routes.new()} outline color="success">
              <FontAwesomeIcon icon={['fat', 'plus']} />
              &nbsp;
              New Customer
            </Button>
          </div>
        }
      </div>
      <table className="table">
        <thead>
          <tr className="sticky-top bg-white" style={{top: '140px'}}>
            <th>&nbsp;</th>
            <th>Abbreviation</th>
            <th>Name</th>
          </tr>
        </thead>
        <tbody>
          {customers.map(customer =>
            <tr key={customer._id}>
              <td>
                <Link to={routes.customers.routes.detail.to(customer._id)}>
                  <FontAwesomeIcon icon={['fat', 'edit']} />
                </Link>
              </td>
              <td>{customer.abbreviation}</td>
              <td>{customer.name}</td>
            </tr>
            )}
        </tbody>
      </table>      
    </div>
  )
}

export default List;
