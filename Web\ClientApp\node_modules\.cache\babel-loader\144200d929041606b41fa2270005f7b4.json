{"ast": null, "code": "import { AsapQueue } from './AsapQueue.js';\nimport { TaskFactory } from './TaskFactory.js';\nconst asapQueue = new AsapQueue();\nconst taskFactory = new TaskFactory(asapQueue.registerPendingError);\n/**\n * Calls a task as soon as possible after returning, in its own event, with priority\n * over other events like animation, reflow, and repaint. An error thrown from an\n * event will not interrupt, nor even substantially slow down the processing of\n * other events, but will be rather postponed to a lower priority event.\n * @param {{call}} task A callable object, typically a function that takes no\n * arguments.\n */\nexport function asap(task) {\n  asapQueue.enqueueTask(taskFactory.create(task));\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAG9C,MAAMC,SAAS,GAAG,IAAIF,SAAS,EAAE;AACjC,MAAMG,WAAW,GAAG,IAAIF,WAAW,CAACC,SAAS,CAACE,oBAAoB,CAAC;AAEnE;;;;;;;;AAQA,OAAO,SAASC,IAAI,CAACC,IAAY,EAAE;EAClCJ,SAAS,CAACK,WAAW,CAACJ,WAAW,CAACK,MAAM,CAACF,IAAI,CAAC,CAAC", "names": ["AsapQueue", "TaskFactory", "asapQueue", "taskFactory", "registerPendingError", "asap", "task", "enqueueTask", "create"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\@react-dnd\\asap\\src\\asap.ts"], "sourcesContent": ["import { AsapQueue } from './AsapQueue.js'\nimport { TaskFactory } from './TaskFactory.js'\nimport type { TaskFn } from './types.js'\n\nconst asapQueue = new AsapQueue()\nconst taskFactory = new TaskFactory(asapQueue.registerPendingError)\n\n/**\n * Calls a task as soon as possible after returning, in its own event, with priority\n * over other events like animation, reflow, and repaint. An error thrown from an\n * event will not interrupt, nor even substantially slow down the processing of\n * other events, but will be rather postponed to a lower priority event.\n * @param {{call}} task A callable object, typically a function that takes no\n * arguments.\n */\nexport function asap(task: TaskFn) {\n\tasapQueue.enqueueTask(taskFactory.create(task))\n}\n"]}, "metadata": {}, "sourceType": "module"}