{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\n// suppress the useLayoutEffect warning on server side.\nexport const useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAElD;AACA,OAAO,MAAMC,yBAAyB,GACrC,OAAOC,MAAM,KAAK,WAAW,GAAGF,eAAe,GAAGD,SAAS", "names": ["useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "window"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useIsomorphicLayoutEffect.ts"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react'\n\n// suppress the useLayoutEffect warning on server side.\nexport const useIsomorphicLayoutEffect =\n\ttypeof window !== 'undefined' ? useLayoutEffect : useEffect\n"]}, "metadata": {}, "sourceType": "module"}