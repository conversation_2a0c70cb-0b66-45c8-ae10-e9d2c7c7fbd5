import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Plant } from "api/models/plants";
import { routes } from "app/routes";
import { useRef } from "react";
import { useDrag, useDrop } from "react-dnd";
import { Link } from "react-router-dom";
import { moveItem } from "./plants-slice";
import { useDispatch } from 'react-redux';

export type PlantListItemProps = {
  plant: Plant;
}

export function PlantListItem({plant}: PlantListItemProps) {
  const ref = useRef<HTMLTableRowElement>(null),
  dispatch = useDispatch(),
  [{isDragging}, drag] = useDrag(() => ({
    type: 'plant',
    item: plant,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })),
  [{isOver}, drop] = useDrop(() => ({
    accept: 'plant',
    drop: (droppedItem: Plant) => {
      dispatch(moveItem({ existingItem: plant, movingItem: droppedItem }));
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  drag(drop(ref));

  return (
    <tr key={plant._id} ref={ref}>
      <td>
        <div 
          // @ts-ignore
          ref={drag}>
          <FontAwesomeIcon icon={['fat', 'grip-vertical']} />
        </div>
      </td>
      <td>
        <Link to={routes.plants.routes.detail.to(plant._id)}>
          <FontAwesomeIcon icon={['fat', 'edit']} />
        </Link>
      </td>
      <td>{plant.abbreviation}</td>
      <td>{plant.name}</td>
      <td className="text-center">{plant.cuttingsPerPot}</td>
      <td className="text-center">{plant.potsPerCase}</td>
      <td className="text-center">
        {plant.hasLightsOut &&
          <FontAwesomeIcon icon={['fat', 'check-square']} />
        }
      </td>
      <td className="text-center">
        {plant.hasPinching &&
          <FontAwesomeIcon icon={['fat', 'check-square']} />
        }
      </td>
    </tr>
  )
}