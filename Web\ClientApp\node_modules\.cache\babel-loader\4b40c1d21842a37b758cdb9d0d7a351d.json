{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\List.tsx\",\n  _s = $RefreshSig$();\nimport { useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { Button } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectPlants } from './plants-slice';\nimport { DndProvider, useDrag, useDrop } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function List() {\n  _s();\n  const plants = useSelector(selectPlants),\n    {\n      isInRole\n    } = useAuth(),\n    canCreate = isInRole('create:plants'),\n    ref = useRef(null),\n    [{\n      isDragging\n    }, drag] = useDrag(() => ({\n      type: 'plant',\n      item: {\n        id: '1'\n      },\n      collect: monitor => ({\n        isDragging: monitor.isDragging()\n      })\n    })),\n    [{\n      isOver\n    }, drop] = useDrop(() => ({\n      accept: 'plant',\n      drop: item => {\n        console.log('dropped', item.id);\n      },\n      collect: monitor => ({\n        isOver: monitor.isOver()\n      })\n    }));\n  drag(drop(ref));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col pt-2\",\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'seedling']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), \"\\xA0 Plants List\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), canCreate && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto pt-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.plants.routes.new(),\n          outline: true,\n          color: \"success\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'plus']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this), \"\\xA0 New Plant\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DndProvider, {\n      backend: HTML5Backend,\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"sticky-top bg-white\",\n            style: {\n              top: '140px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: \"\\xA0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\xA0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Abbreviation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Cuttings/Pot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Pots/Case\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Lights Out?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Pinching?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: plants.map(plant => /*#__PURE__*/_jsxDEV(\"tr\", {\n            ref: ref,\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: routes.plants.routes.detail.to(plant._id),\n                children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: ['fat', 'edit']\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: plant.abbreviation\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: plant.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"text-center\",\n              children: plant.cuttingsPerPot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"text-center\",\n              children: plant.potsPerCase\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"text-center\",\n              children: plant.hasLightsOut && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'check-square']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"text-center\",\n              children: plant.hasPinching && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'check-square']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)]\n          }, plant._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_s(List, \"jZc1VNm+JCpXavIQArQyNRIRExU=\", false, function () {\n  return [useSelector, useAuth, useDrag, useDrop];\n});\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["useSelector", "Link", "<PERSON><PERSON>", "FontAwesomeIcon", "routes", "useAuth", "selectPlants", "DndProvider", "useDrag", "useDrop", "HTML5Backend", "useRef", "List", "plants", "isInRole", "canCreate", "ref", "isDragging", "drag", "type", "item", "id", "collect", "monitor", "isOver", "drop", "accept", "console", "log", "new", "top", "map", "plant", "detail", "to", "_id", "abbreviation", "name", "cuttingsPerPot", "potsPerCase", "hasLightsOut", "hasPinching"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/List.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport { Button } from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectPlants } from './plants-slice';\r\nimport { DndProvider, useDrag, useDrop } from 'react-dnd';\r\nimport { HTML5Backend } from 'react-dnd-html5-backend';\r\nimport { useRef } from 'react';\r\n\r\nexport function List() {\r\n  const plants = useSelector(selectPlants),\r\n    {isInRole} = useAuth(),\r\n    canCreate = isInRole('create:plants'),\r\n  ref = useRef<HTMLTableRowElement>(null),\r\n  [{isDragging}, drag] = useDrag(() => ({\r\n    type: 'plant',\r\n    item: {id: '1'},\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging(),\r\n    }),\r\n  })),\r\n  [{isOver}, drop] = useDrop(() => ({\r\n    accept: 'plant',\r\n    drop: (item: {id: string}) => {\r\n      console.log('dropped', item.id);\r\n    },\r\n    collect: (monitor) => ({\r\n      isOver: monitor.isOver(),\r\n    }),\r\n  }));\r\n\r\n  drag(drop(ref));\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <h1 className=\"col pt-2\">\r\n          <FontAwesomeIcon icon={['fat', 'seedling']} />\r\n          &nbsp;\r\n          Plants List\r\n        </h1>\r\n        {canCreate &&\r\n          <div className=\"col-auto pt-3\">\r\n            <Button tag={Link} to={routes.plants.routes.new()} outline color=\"success\">\r\n              <FontAwesomeIcon icon={['fat', 'plus']} />\r\n              &nbsp;\r\n              New Plant\r\n            </Button>\r\n          </div>\r\n        }\r\n      </div>\r\n      <DndProvider backend={HTML5Backend}>\r\n        <table className=\"table\">\r\n          <thead>\r\n            <tr className=\"sticky-top bg-white\" style={{top: '140px'}}>\r\n              <td>&nbsp;</td>\r\n              <th>&nbsp;</th>\r\n              <th>Abbreviation</th>\r\n              <th>Name</th>\r\n              <th className=\"text-center\">Cuttings/Pot</th>\r\n              <th className=\"text-center\">Pots/Case</th>\r\n              <th className=\"text-center\">Lights Out?</th>\r\n              <th className=\"text-center\">Pinching?</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {plants.map(plant =>\r\n              <tr key={plant._id} ref={ref}>\r\n                <td>\r\n                  <Link to={routes.plants.routes.detail.to(plant._id)}>\r\n                    <FontAwesomeIcon icon={['fat', 'edit']} />\r\n                  </Link>\r\n                </td>\r\n                <td>{plant.abbreviation}</td>\r\n                <td>{plant.name}</td>\r\n                <td className=\"text-center\">{plant.cuttingsPerPot}</td>\r\n                <td className=\"text-center\">{plant.potsPerCase}</td>\r\n                <td className=\"text-center\">\r\n                  {plant.hasLightsOut &&\r\n                    <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n                  }\r\n                </td>\r\n                <td className=\"text-center\">\r\n                  {plant.hasPinching &&\r\n                    <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n                  }\r\n                </td>\r\n              </tr>\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </DndProvider>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default List;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,EAAEC,OAAO,EAAEC,OAAO,QAAQ,WAAW;AACzD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,MAAM,QAAQ,OAAO;AAAC;AAE/B,OAAO,SAASC,IAAI,GAAG;EAAA;EACrB,MAAMC,MAAM,GAAGb,WAAW,CAACM,YAAY,CAAC;IACtC;MAACQ;IAAQ,CAAC,GAAGT,OAAO,EAAE;IACtBU,SAAS,GAAGD,QAAQ,CAAC,eAAe,CAAC;IACvCE,GAAG,GAAGL,MAAM,CAAsB,IAAI,CAAC;IACvC,CAAC;MAACM;IAAU,CAAC,EAAEC,IAAI,CAAC,GAAGV,OAAO,CAAC,OAAO;MACpCW,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE;QAACC,EAAE,EAAE;MAAG,CAAC;MACfC,OAAO,EAAGC,OAAO,KAAM;QACrBN,UAAU,EAAEM,OAAO,CAACN,UAAU;MAChC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,CAAC;MAACO;IAAM,CAAC,EAAEC,IAAI,CAAC,GAAGhB,OAAO,CAAC,OAAO;MAChCiB,MAAM,EAAE,OAAO;MACfD,IAAI,EAAGL,IAAkB,IAAK;QAC5BO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAER,IAAI,CAACC,EAAE,CAAC;MACjC,CAAC;MACDC,OAAO,EAAGC,OAAO,KAAM;QACrBC,MAAM,EAAED,OAAO,CAACC,MAAM;MACxB,CAAC;IACH,CAAC,CAAC,CAAC;EAEHN,IAAI,CAACO,IAAI,CAACT,GAAG,CAAC,CAAC;EAEf,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,+DAA+D;MAAA,wBAC5E;QAAI,SAAS,EAAC,UAAU;QAAA,wBACtB,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU;QAAE;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA,QAG3C,EACJD,SAAS,iBACR;QAAK,SAAS,EAAC,eAAe;QAAA,uBAC5B,QAAC,MAAM;UAAC,GAAG,EAAEd,IAAK;UAAC,EAAE,EAAEG,MAAM,CAACS,MAAM,CAACT,MAAM,CAACyB,GAAG,EAAG;UAAC,OAAO;UAAC,KAAK,EAAC,SAAS;UAAA,wBACxE,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAGnC;QAAA;QAAA;QAAA;MAAA,QACL;IAAA;MAAA;MAAA;MAAA;IAAA,QAEJ,eACN,QAAC,WAAW;MAAC,OAAO,EAAEnB,YAAa;MAAA,uBACjC;QAAO,SAAS,EAAC,OAAO;QAAA,wBACtB;UAAA,uBACE;YAAI,SAAS,EAAC,qBAAqB;YAAC,KAAK,EAAE;cAACoB,GAAG,EAAE;YAAO,CAAE;YAAA,wBACxD;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe,eACf;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe,eACf;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAqB,eACrB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAa,eACb;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAkB,eAC7C;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe,eAC1C;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAiB,eAC5C;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe;UAAA;YAAA;YAAA;YAAA;UAAA;QACvC;UAAA;UAAA;UAAA;QAAA,QACC,eACR;UAAA,UACGjB,MAAM,CAACkB,GAAG,CAACC,KAAK,iBACf;YAAoB,GAAG,EAAEhB,GAAI;YAAA,wBAC3B;cAAA,uBACE,QAAC,IAAI;gBAAC,EAAE,EAAEZ,MAAM,CAACS,MAAM,CAACT,MAAM,CAAC6B,MAAM,CAACC,EAAE,CAACF,KAAK,CAACG,GAAG,CAAE;gBAAA,uBAClD,QAAC,eAAe;kBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;gBAAE;kBAAA;kBAAA;kBAAA;gBAAA;cAAG;gBAAA;gBAAA;gBAAA;cAAA;YACrC;cAAA;cAAA;cAAA;YAAA,QACJ,eACL;cAAA,UAAKH,KAAK,CAACI;YAAY;cAAA;cAAA;cAAA;YAAA,QAAM,eAC7B;cAAA,UAAKJ,KAAK,CAACK;YAAI;cAAA;cAAA;cAAA;YAAA,QAAM,eACrB;cAAI,SAAS,EAAC,aAAa;cAAA,UAAEL,KAAK,CAACM;YAAc;cAAA;cAAA;cAAA;YAAA,QAAM,eACvD;cAAI,SAAS,EAAC,aAAa;cAAA,UAAEN,KAAK,CAACO;YAAW;cAAA;cAAA;cAAA;YAAA,QAAM,eACpD;cAAI,SAAS,EAAC,aAAa;cAAA,UACxBP,KAAK,CAACQ,YAAY,iBACjB,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA,QAEjD,eACL;cAAI,SAAS,EAAC,aAAa;cAAA,UACxBR,KAAK,CAACS,WAAW,iBAChB,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA,QAEjD;UAAA,GAnBET,KAAK,CAACG,GAAG;YAAA;YAAA;YAAA;UAAA,QAoBb;QACN;UAAA;UAAA;UAAA;QAAA,QACK;MAAA;QAAA;QAAA;QAAA;MAAA;IACF;MAAA;MAAA;MAAA;IAAA,QACI;EAAA;IAAA;IAAA;IAAA;EAAA,QACV;AAEV;AAAC,GArFevB,IAAI;EAAA,QACHZ,WAAW,EACXK,OAAO,EAGCG,OAAO,EAOXC,OAAO;AAAA;AAAA,KAZZG,IAAI;AAuFpB,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}