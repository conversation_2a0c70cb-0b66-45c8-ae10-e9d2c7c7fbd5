import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectZones } from './zones-slice';

export function List() {
  const zones = useSelector(selectZones),
    { isInRole } = useAuth(),
    canCreate = isInRole('create:zones');

  return (
    <div className="container d-grid gap-2">
      <div className="row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow">
        <div className="col-12 row mt-2">
          <h1 className="col">
            <FontAwesomeIcon icon={['fat', 'map-location-dot']} />
            &nbsp; Zones List
          </h1>
          {canCreate && (
            <div className="col-auto">
              <Button
                tag={Link}
                to={routes.zones.routes.new()}
                outline
                color="success">
                <FontAwesomeIcon icon={['fat', 'plus']} />
                &nbsp; New Zone
              </Button>
            </div>
          )}
        </div>
      </div>
      <table className="table w-auto">
        <thead>
          <tr className="sticky-top bg-white" style={{ top: '140px' }}>
            <th>&nbsp;</th>
            <th>Name</th>
            <th className="text-center">Tables</th>
            <th className="text-center">Offsite</th>
          </tr>
        </thead>
        <tbody>
          {zones.map((zone) => (
            <tr key={zone._id}>
              <td>
                <Link to={routes.zones.routes.detail.to(zone._id)}>
                  <FontAwesomeIcon icon={['fat', 'edit']} />
                </Link>
              </td>
              <td>{zone.name}</td>
              <td className="text-center">{zone.tables}</td>
              <td className="text-center">
                {zone.isOffsite && (
                  <FontAwesomeIcon icon={['fat', 'check-square']} />
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default List;
