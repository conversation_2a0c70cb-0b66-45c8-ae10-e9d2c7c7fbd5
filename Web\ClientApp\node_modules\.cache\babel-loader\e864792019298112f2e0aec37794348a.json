{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\List.tsx\",\n  _s = $RefreshSig$();\nimport { useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { Button } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectPlants } from './plants-slice';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { PlantListItem } from './List-Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function List() {\n  _s();\n  const plants = useSelector(selectPlants),\n    {\n      isInRole\n    } = useAuth(),\n    canCreate = isInRole('create:plants');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col pt-2\",\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'seedling']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), \"\\xA0 Plants List\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), canCreate && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto pt-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.plants.routes.new(),\n          outline: true,\n          color: \"success\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'plus']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 15\n          }, this), \"\\xA0 New Plant\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DndProvider, {\n      backend: HTML5Backend,\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"sticky-top bg-white\",\n            style: {\n              top: '140px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: \"\\xA0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\xA0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Abbreviation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Cuttings/Pot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Pots/Case\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Lights Out?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"text-center\",\n              children: \"Pinching?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: plants.map(plant => /*#__PURE__*/_jsxDEV(PlantListItem, {\n            plant: plant\n          }, plant._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_s(List, \"67AHeRXQdmHLwQI+MeiZFek7qts=\", false, function () {\n  return [useSelector, useAuth];\n});\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["useSelector", "Link", "<PERSON><PERSON>", "FontAwesomeIcon", "routes", "useAuth", "selectPlants", "DndProvider", "HTML5Backend", "PlantListItem", "List", "plants", "isInRole", "canCreate", "new", "top", "map", "plant", "_id"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/List.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport { Button } from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectPlants } from './plants-slice';\r\nimport { DndProvider } from 'react-dnd';\r\nimport { HTML5Backend } from 'react-dnd-html5-backend';\r\nimport { PlantListItem } from './List-Item';\r\n\r\nexport function List() {\r\n  const plants = useSelector(selectPlants),\r\n    {isInRole} = useAuth(),\r\n    canCreate = isInRole('create:plants');\r\n  \r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <h1 className=\"col pt-2\">\r\n          <FontAwesomeIcon icon={['fat', 'seedling']} />\r\n          &nbsp;\r\n          Plants List\r\n        </h1>\r\n        {canCreate &&\r\n          <div className=\"col-auto pt-3\">\r\n            <Button tag={Link} to={routes.plants.routes.new()} outline color=\"success\">\r\n              <FontAwesomeIcon icon={['fat', 'plus']} />\r\n              &nbsp;\r\n              New Plant\r\n            </Button>\r\n          </div>\r\n        }\r\n      </div>\r\n      <DndProvider backend={HTML5Backend}>\r\n        <table className=\"table\">\r\n          <thead>\r\n            <tr className=\"sticky-top bg-white\" style={{top: '140px'}}>\r\n              <td>&nbsp;</td>\r\n              <th>&nbsp;</th>\r\n              <th>Abbreviation</th>\r\n              <th>Name</th>\r\n              <th className=\"text-center\">Cuttings/Pot</th>\r\n              <th className=\"text-center\">Pots/Case</th>\r\n              <th className=\"text-center\">Lights Out?</th>\r\n              <th className=\"text-center\">Pinching?</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {plants.map(plant =>\r\n              <PlantListItem key={plant._id} plant={plant} />\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </DndProvider>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default List;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,aAAa,QAAQ,aAAa;AAAC;AAE5C,OAAO,SAASC,IAAI,GAAG;EAAA;EACrB,MAAMC,MAAM,GAAGX,WAAW,CAACM,YAAY,CAAC;IACtC;MAACM;IAAQ,CAAC,GAAGP,OAAO,EAAE;IACtBQ,SAAS,GAAGD,QAAQ,CAAC,eAAe,CAAC;EAEvC,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,+DAA+D;MAAA,wBAC5E;QAAI,SAAS,EAAC,UAAU;QAAA,wBACtB,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU;QAAE;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA,QAG3C,EACJC,SAAS,iBACR;QAAK,SAAS,EAAC,eAAe;QAAA,uBAC5B,QAAC,MAAM;UAAC,GAAG,EAAEZ,IAAK;UAAC,EAAE,EAAEG,MAAM,CAACO,MAAM,CAACP,MAAM,CAACU,GAAG,EAAG;UAAC,OAAO;UAAC,KAAK,EAAC,SAAS;UAAA,wBACxE,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAGnC;QAAA;QAAA;QAAA;MAAA,QACL;IAAA;MAAA;MAAA;MAAA;IAAA,QAEJ,eACN,QAAC,WAAW;MAAC,OAAO,EAAEN,YAAa;MAAA,uBACjC;QAAO,SAAS,EAAC,OAAO;QAAA,wBACtB;UAAA,uBACE;YAAI,SAAS,EAAC,qBAAqB;YAAC,KAAK,EAAE;cAACO,GAAG,EAAE;YAAO,CAAE;YAAA,wBACxD;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe,eACf;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe,eACf;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAqB,eACrB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAa,eACb;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAkB,eAC7C;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe,eAC1C;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAiB,eAC5C;cAAI,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAe;UAAA;YAAA;YAAA;YAAA;UAAA;QACvC;UAAA;UAAA;UAAA;QAAA,QACC,eACR;UAAA,UACGJ,MAAM,CAACK,GAAG,CAACC,KAAK,iBACf,QAAC,aAAa;YAAiB,KAAK,EAAEA;UAAM,GAAxBA,KAAK,CAACC,GAAG;YAAA;YAAA;YAAA;UAAA,QAAkB;QAChD;UAAA;UAAA;UAAA;QAAA,QACK;MAAA;QAAA;QAAA;QAAA;MAAA;IACF;MAAA;MAAA;MAAA;IAAA,QACI;EAAA;IAAA;IAAA;IAAA;EAAA,QACV;AAEV;AAAC,GA9CeR,IAAI;EAAA,QACHV,WAAW,EACXK,OAAO;AAAA;AAAA,KAFRK,IAAI;AAgDpB,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}