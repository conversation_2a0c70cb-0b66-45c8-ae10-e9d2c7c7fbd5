{"ast": null, "code": "import { memoize } from './utils/js_utils.js';\nexport const isFirefox = memoize(() => /firefox/i.test(navigator.userAgent));\nexport const isSafari = memoize(() => Boolean(window.safari));", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAS7C,OAAO,MAAMC,SAAS,GAAcD,OAAO,CAAC,MAC3C,WAAWE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,CACpC;AACD,OAAO,MAAMC,QAAQ,GAAcL,OAAO,CAAC,MAAMM,OAAO,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC", "names": ["memoize", "isFirefox", "test", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "safari"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd-html5-backend\\src\\BrowserDetector.ts"], "sourcesContent": ["import { memoize } from './utils/js_utils.js'\n\ndeclare global {\n\tinterface Window extends HTMLElement {\n\t\tsafari: any\n\t}\n}\n\nexport type Predicate = () => boolean\nexport const isFirefox: Predicate = memoize(() =>\n\t/firefox/i.test(navigator.userAgent),\n)\nexport const isSafari: Predicate = memoize(() => Boolean(window.safari))\n"]}, "metadata": {}, "sourceType": "module"}