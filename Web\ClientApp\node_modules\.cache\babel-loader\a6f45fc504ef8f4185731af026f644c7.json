{"ast": null, "code": "import { useMemo } from 'react';\nexport function useConnectDragSource(connector) {\n  return useMemo(() => connector.hooks.dragSource(), [connector]);\n}\nexport function useConnectDragPreview(connector) {\n  return useMemo(() => connector.hooks.dragPreview(), [connector]);\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAI/B,OAAO,SAASC,oBAAoB,CAACC,SAA0B,EAAE;EAChE,OAAOF,OAAO,CAAC,MAAME,SAAS,CAACC,KAAK,CAACC,UAAU,EAAE,EAAE,CAACF,SAAS,CAAC,CAAC;;AAGhE,OAAO,SAASG,qBAAqB,CAACH,SAA0B,EAAE;EACjE,OAAOF,OAAO,CAAC,MAAME,SAAS,CAACC,KAAK,CAACG,WAAW,EAAE,EAAE,CAACJ,SAAS,CAAC,CAAC", "names": ["useMemo", "useConnectDragSource", "connector", "hooks", "dragSource", "useConnectDragPreview", "dragPreview"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrag\\connectors.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport type { SourceConnector } from '../../internals/index.js'\n\nexport function useConnectDragSource(connector: SourceConnector) {\n\treturn useMemo(() => connector.hooks.dragSource(), [connector])\n}\n\nexport function useConnectDragPreview(connector: SourceConnector) {\n\treturn useMemo(() => connector.hooks.dragPreview(), [connector])\n}\n"]}, "metadata": {}, "sourceType": "module"}