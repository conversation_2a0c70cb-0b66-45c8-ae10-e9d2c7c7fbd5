{"ast": null, "code": "import { useMemo } from 'react';\nimport { DragSourceMonitorImpl } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nexport function useDragSourceMonitor() {\n  const manager = useDragDropManager();\n  return useMemo(() => new DragSourceMonitorImpl(manager), [manager]);\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAE/B,SAASC,qBAAqB,QAAQ,0BAA0B;AAEhE,SAASC,kBAAkB,QAAQ,0BAA0B;AAE7D,OAAO,SAASC,oBAAoB,GAAkC;EACrE,MAAMC,OAAO,GAAGF,kBAAkB,EAAE;EACpC,OAAOF,OAAO,CACb,MAAM,IAAIC,qBAAqB,CAACG,OAAO,CAAC,EACxC,CAACA,OAAO,CAAC,CACT", "names": ["useMemo", "DragSourceMonitorImpl", "useDragDropManager", "useDragSourceMonitor", "manager"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrag\\useDragSourceMonitor.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { DragSourceMonitorImpl } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\n\nexport function useDragSourceMonitor<O, R>(): DragSourceMonitor<O, R> {\n\tconst manager = useDragDropManager()\n\treturn useMemo<DragSourceMonitor<O, R>>(\n\t\t() => new DragSourceMonitorImpl(manager),\n\t\t[manager],\n\t)\n}\n"]}, "metadata": {}, "sourceType": "module"}