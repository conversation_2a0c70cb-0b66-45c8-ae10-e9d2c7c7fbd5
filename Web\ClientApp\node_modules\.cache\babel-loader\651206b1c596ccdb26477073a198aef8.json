{"ast": null, "code": "// Safari 6 and 6.1 for desktop, iPad, and iPhone are the only browsers that\n// have WebKitMutationObserver but not un-prefixed MutationObserver.\n// Must use `global` or `self` instead of `window` to work in both frames and web\n// workers. `global` is a provision of <PERSON><PERSON><PERSON><PERSON>, Mr, Mrs, or Mo<PERSON>.\n/* globals self */const scope = typeof global !== 'undefined' ? global : self;\nconst BrowserMutationObserver = scope.MutationObserver || scope.WebKitMutationObserver;\nexport function makeRequestCallFromTimer(callback) {\n  return function requestCall() {\n    // We dispatch a timeout with a specified delay of 0 for engines that\n    // can reliably accommodate that request. This will usually be snapped\n    // to a 4 milisecond delay, but once we're flushing, there's no delay\n    // between events.\n    const timeoutHandle = setTimeout(handleTimer, 0);\n    // However, since this timer gets frequently dropped in Firefox\n    // workers, we enlist an interval handle that will try to fire\n    // an event 20 times per second until it succeeds.\n    const intervalHandle = setInterval(handleTimer, 50);\n    function handleTimer() {\n      // Whichever timer succeeds will cancel both timers and\n      // execute the callback.\n      clearTimeout(timeoutHandle);\n      clearInterval(intervalHandle);\n      callback();\n    }\n  };\n}\n// To request a high priority event, we induce a mutation observer by toggling\n// the text of a text node between \"1\" and \"-1\".\nexport function makeRequestCallFromMutationObserver(callback) {\n  let toggle = 1;\n  const observer = new BrowserMutationObserver(callback);\n  const node = document.createTextNode('');\n  observer.observe(node, {\n    characterData: true\n  });\n  return function requestCall() {\n    toggle = -toggle;\n    node.data = toggle;\n  };\n}\nexport const makeRequestCall = typeof BrowserMutationObserver === 'function' ?\n// reliably everywhere they are implemented.\n// They are implemented in all modern browsers.\n//\n// - Android 4-4.3\n// - Chrome 26-34\n// - Firefox 14-29\n// - Internet Explorer 11\n// - iPad Safari 6-7.1\n// - iPhone Safari 7-7.1\n// - Safari 6-7\nmakeRequestCallFromMutationObserver :\n// task queue, are implemented in Internet Explorer 10, Safari 5.0-1, and Opera\n// 11-12, and in web workers in many engines.\n// Although message channels yield to any queued rendering and IO tasks, they\n// would be better than imposing the 4ms delay of timers.\n// However, they do not work reliably in Internet Explorer or Safari.\n// Internet Explorer 10 is the only browser that has setImmediate but does\n// not have MutationObservers.\n// Although setImmediate yields to the browser's renderer, it would be\n// preferrable to falling back to setTimeout since it does not have\n// the minimum 4ms penalty.\n// Unfortunately there appears to be a bug in Internet Explorer 10 Mobile (and\n// Desktop to a lesser extent) that renders both setImmediate and\n// MessageChannel useless for the purposes of ASAP.\n// https://github.com/kriskowal/q/issues/396\n// Timers are implemented universally.\n// We fall back to timers in workers in most engines, and in foreground\n// contexts in the following browsers.\n// However, note that even this simple case requires nuances to operate in a\n// broad spectrum of browsers.\n//\n// - Firefox 3-13\n// - Internet Explorer 6-9\n// - iPad Safari 4.3\n// - Lynx 2.8.7\nmakeRequestCallFromTimer;", "map": {"version": 3, "mappings": "AAAA;AACA;AACA;AACA;AAEA,kBACA,MAAMA,KAAK,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAGC,IAAI;AAC3D,MAAMC,uBAAuB,GAC5BH,KAAM,CAASI,gBAAgB,IAAIJ,KAAM,CAASK,sBAAsB;AAEzE,OAAO,SAASC,wBAAwB,CAACC,QAAoB,EAAE;EAC9D,OAAO,SAASC,WAAW,GAAG;IAC7B;IACA;IACA;IACA;IACA,MAAMC,aAAa,GAAGC,UAAU,CAACC,WAAW,EAAE,CAAC,CAAC;IAChD;IACA;IACA;IACA,MAAMC,cAAc,GAAGC,WAAW,CAACF,WAAW,EAAE,EAAE,CAAC;IAEnD,SAASA,WAAW,GAAG;MACtB;MACA;MACAG,YAAY,CAACL,aAAa,CAAC;MAC3BM,aAAa,CAACH,cAAc,CAAC;MAC7BL,QAAQ,EAAE;;GAEX;;AAGF;AACA;AACA,OAAO,SAASS,mCAAmC,CAACT,QAAoB,EAAE;EACzE,IAAIU,MAAM,GAAG,CAAC;EACd,MAAMC,QAAQ,GAAG,IAAIf,uBAAuB,CAACI,QAAQ,CAAC;EACtD,MAAMY,IAAI,GAAGC,QAAQ,CAACC,cAAc,CAAC,EAAE,CAAC;EACxCH,QAAQ,CAACI,OAAO,CAACH,IAAI,EAAE;IAAEI,aAAa,EAAE;GAAM,CAAC;EAC/C,OAAO,SAASf,WAAW,GAAG;IAC7BS,MAAM,GAAG,CAACA,MAAM;IACfE,IAAK,CAASK,IAAI,GAAGP,MAAM;GAC5B;;AAGF,OAAO,MAAMQ,eAAe,GAC3B,OAAOtB,uBAAuB,KAAK,UAAU;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAa,mCAAmC;AAEnC;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAV,wBAAwB", "names": ["scope", "global", "self", "BrowserMutationObserver", "MutationObserver", "WebKitMutationObserver", "makeRequestCallFromTimer", "callback", "requestCall", "timeoutH<PERSON>le", "setTimeout", "handleTimer", "intervalHandle", "setInterval", "clearTimeout", "clearInterval", "makeRequestCallFromMutationObserver", "toggle", "observer", "node", "document", "createTextNode", "observe", "characterData", "data", "makeRequestCall"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\@react-dnd\\asap\\src\\makeRequestCall.ts"], "sourcesContent": ["// Safari 6 and 6.1 for desktop, iPad, and iPhone are the only browsers that\n// have WebKitMutationObserver but not un-prefixed MutationObserver.\n// Must use `global` or `self` instead of `window` to work in both frames and web\n// workers. `global` is a provision of <PERSON><PERSON><PERSON><PERSON>, Mr, Mrs, or Mo<PERSON>.\n\n/* globals self */\nconst scope = typeof global !== 'undefined' ? global : self\nconst BrowserMutationObserver =\n\t(scope as any).MutationObserver || (scope as any).WebKitMutationObserver\n\nexport function makeRequestCallFromTimer(callback: () => void) {\n\treturn function requestCall() {\n\t\t// We dispatch a timeout with a specified delay of 0 for engines that\n\t\t// can reliably accommodate that request. This will usually be snapped\n\t\t// to a 4 milisecond delay, but once we're flushing, there's no delay\n\t\t// between events.\n\t\tconst timeoutHandle = setTimeout(handleTimer, 0)\n\t\t// However, since this timer gets frequently dropped in Firefox\n\t\t// workers, we enlist an interval handle that will try to fire\n\t\t// an event 20 times per second until it succeeds.\n\t\tconst intervalHandle = setInterval(handleTimer, 50)\n\n\t\tfunction handleTimer() {\n\t\t\t// Whichever timer succeeds will cancel both timers and\n\t\t\t// execute the callback.\n\t\t\tclearTimeout(timeoutHandle)\n\t\t\tclearInterval(intervalHandle)\n\t\t\tcallback()\n\t\t}\n\t}\n}\n\n// To request a high priority event, we induce a mutation observer by toggling\n// the text of a text node between \"1\" and \"-1\".\nexport function makeRequestCallFromMutationObserver(callback: () => void) {\n\tlet toggle = 1\n\tconst observer = new BrowserMutationObserver(callback)\n\tconst node = document.createTextNode('')\n\tobserver.observe(node, { characterData: true })\n\treturn function requestCall() {\n\t\ttoggle = -toggle\n\t\t;(node as any).data = toggle\n\t}\n}\n\nexport const makeRequestCall =\n\ttypeof BrowserMutationObserver === 'function'\n\t\t? // MutationObservers are desirable because they have high priority and work\n\t\t  // reliably everywhere they are implemented.\n\t\t  // They are implemented in all modern browsers.\n\t\t  //\n\t\t  // - Android 4-4.3\n\t\t  // - Chrome 26-34\n\t\t  // - Firefox 14-29\n\t\t  // - Internet Explorer 11\n\t\t  // - iPad Safari 6-7.1\n\t\t  // - iPhone Safari 7-7.1\n\t\t  // - Safari 6-7\n\t\t  makeRequestCallFromMutationObserver\n\t\t: // MessageChannels are desirable because they give direct access to the HTML\n\t\t  // task queue, are implemented in Internet Explorer 10, Safari 5.0-1, and Opera\n\t\t  // 11-12, and in web workers in many engines.\n\t\t  // Although message channels yield to any queued rendering and IO tasks, they\n\t\t  // would be better than imposing the 4ms delay of timers.\n\t\t  // However, they do not work reliably in Internet Explorer or Safari.\n\n\t\t  // Internet Explorer 10 is the only browser that has setImmediate but does\n\t\t  // not have MutationObservers.\n\t\t  // Although setImmediate yields to the browser's renderer, it would be\n\t\t  // preferrable to falling back to setTimeout since it does not have\n\t\t  // the minimum 4ms penalty.\n\t\t  // Unfortunately there appears to be a bug in Internet Explorer 10 Mobile (and\n\t\t  // Desktop to a lesser extent) that renders both setImmediate and\n\t\t  // MessageChannel useless for the purposes of ASAP.\n\t\t  // https://github.com/kriskowal/q/issues/396\n\n\t\t  // Timers are implemented universally.\n\t\t  // We fall back to timers in workers in most engines, and in foreground\n\t\t  // contexts in the following browsers.\n\t\t  // However, note that even this simple case requires nuances to operate in a\n\t\t  // broad spectrum of browsers.\n\t\t  //\n\t\t  // - Firefox 3-13\n\t\t  // - Internet Explorer 6-9\n\t\t  // - iPad Safari 4.3\n\t\t  // - Lynx 2.8.7\n\t\t  makeRequestCallFromTimer\n"]}, "metadata": {}, "sourceType": "module"}