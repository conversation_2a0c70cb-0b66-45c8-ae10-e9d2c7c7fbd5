{"ast": null, "code": "import { memo, useEffect } from 'react';\n/**\n * A utility for rendering a drag preview image\n */\nexport const DragPreviewImage = memo(function DragPreviewImage(_ref) {\n  let {\n    connect,\n    src\n  } = _ref;\n  useEffect(() => {\n    if (typeof Image === 'undefined') return;\n    let connected = false;\n    const img = new Image();\n    img.src = src;\n    img.onload = () => {\n      connect(img);\n      connected = true;\n    };\n    return () => {\n      if (connected) {\n        connect(null);\n      }\n    };\n  });\n  return null;\n});", "map": {"version": 3, "mappings": "AACA,SAASA,IAAI,EAAEC,SAAS,QAAQ,OAAO;AAQvC;;;AAGA,OAAO,MAAMC,gBAAgB,GAA8BF,IAAI,CAC9D,SAASE,gBAAgB,OAAmB;EAAA,IAAlB;IAAEC,OAAO;IAAEC;EAAG,CAAE;EACzCH,SAAS,CAAC,MAAM;IACf,IAAI,OAAOI,KAAK,KAAK,WAAW,EAAE;IAElC,IAAIC,SAAS,GAAG,KAAK;IACrB,MAAMC,GAAG,GAAG,IAAIF,KAAK,EAAE;IACvBE,GAAG,CAACH,GAAG,GAAGA,GAAG;IACbG,GAAG,CAACC,MAAM,GAAG,MAAM;MAClBL,OAAO,CAACI,GAAG,CAAC;MACZD,SAAS,GAAG,IAAI;KAChB;IACD,OAAO,MAAM;MACZ,IAAIA,SAAS,EAAE;QACdH,OAAO,CAAC,IAAI,CAAC;;KAEd;GACD,CAAC;EAEF,OAAO,IAAI;CACX,CACD", "names": ["memo", "useEffect", "DragPreviewImage", "connect", "src", "Image", "connected", "img", "onload"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\core\\DragPreviewImage.ts"], "sourcesContent": ["import type { FC } from 'react'\nimport { memo, useEffect } from 'react'\n\nimport type { ConnectDragPreview } from '../types/index.js'\n\nexport interface DragPreviewImageProps {\n\tconnect: ConnectDragPreview\n\tsrc: string\n}\n/**\n * A utility for rendering a drag preview image\n */\nexport const DragPreviewImage: FC<DragPreviewImageProps> = memo(\n\tfunction DragPreviewImage({ connect, src }) {\n\t\tuseEffect(() => {\n\t\t\tif (typeof Image === 'undefined') return\n\n\t\t\tlet connected = false\n\t\t\tconst img = new Image()\n\t\t\timg.src = src\n\t\t\timg.onload = () => {\n\t\t\t\tconnect(img)\n\t\t\t\tconnected = true\n\t\t\t}\n\t\t\treturn () => {\n\t\t\t\tif (connected) {\n\t\t\t\t\tconnect(null)\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\treturn null\n\t},\n)\n"]}, "metadata": {}, "sourceType": "module"}