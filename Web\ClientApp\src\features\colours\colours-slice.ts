import { Colour } from "api/models/colour";
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from 'app/store';

export interface ColourState {
  colours: Colour[];
}

const initialState: ColourState = {
  colours: []
};

export const coloursSlice = createSlice({
  name: 'colours',
  initialState,
  reducers: {
    setColours(state, action: PayloadAction<Colour[]>) {
      state.colours = action.payload;
    }
  }
});

export const { setColours } = coloursSlice.actions;

export const selectColours = (state: RootState) => state.colours.colours;

export default coloursSlice.reducer;
