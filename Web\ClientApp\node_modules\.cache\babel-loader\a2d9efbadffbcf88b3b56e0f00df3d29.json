{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\List-Item.tsx\",\n  _s = $RefreshSig$();\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport { routes } from \"app/routes\";\nimport { useRef } from \"react\";\nimport { useDrag, useDrop } from \"react-dnd\";\nimport { Link } from \"react-router-dom\";\nimport { moveItem } from \"./plants-slice\";\nimport { useDispatch } from 'react-redux';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function PlantListItem(_ref) {\n  _s();\n  let {\n    plant\n  } = _ref;\n  const ref = useRef(null),\n    dispatch = useDispatch(),\n    [{\n      isDragging\n    }, drag] = useDrag(() => ({\n      type: 'plant',\n      item: plant,\n      collect: monitor => ({\n        isDragging: monitor.isDragging()\n      })\n    })),\n    [{\n      isOver\n    }, drop] = useDrop(() => ({\n      accept: 'plant',\n      drop: droppedItem => {\n        dispatch(moveItem({\n          existingItem: plant,\n          movingItem: droppedItem\n        }));\n      },\n      collect: monitor => ({\n        isOver: monitor.isOver()\n      })\n    }));\n  drag(drop(ref));\n  return /*#__PURE__*/_jsxDEV(\"tr\", {\n    ref: ref,\n    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cursor-move\"\n        // @ts-ignore\n        ,\n        ref: drag,\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'grip-vertical']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: routes.plants.routes.detail.to(plant._id),\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'edit']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: plant.abbreviation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: plant.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.cuttingsPerPot\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.potsPerCase\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.hasLightsOut && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: ['fat', 'check-square']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.hasPinching && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: ['fat', 'check-square']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, plant._id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_s(PlantListItem, \"pwXOvoDMs+rAY8m97YoZFprZsdU=\", false, function () {\n  return [useDispatch, useDrag, useDrop];\n});\n_c = PlantListItem;\nvar _c;\n$RefreshReg$(_c, \"PlantListItem\");", "map": {"version": 3, "names": ["FontAwesomeIcon", "routes", "useRef", "useDrag", "useDrop", "Link", "moveItem", "useDispatch", "PlantListItem", "plant", "ref", "dispatch", "isDragging", "drag", "type", "item", "collect", "monitor", "isOver", "drop", "accept", "droppedItem", "existingItem", "movingItem", "plants", "detail", "to", "_id", "abbreviation", "name", "cuttingsPerPot", "potsPerCase", "hasLightsOut", "hasPinching"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/List-Item.tsx"], "sourcesContent": ["import { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Plant } from \"api/models/plants\";\r\nimport { routes } from \"app/routes\";\r\nimport { useRef } from \"react\";\r\nimport { useDrag, useDrop } from \"react-dnd\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { moveItem } from \"./plants-slice\";\r\nimport { useDispatch } from 'react-redux';\r\n\r\nexport type PlantListItemProps = {\r\n  plant: Plant;\r\n}\r\n\r\nexport function PlantListItem({plant}: PlantListItemProps) {\r\n  const ref = useRef<HTMLTableRowElement>(null),\r\n  dispatch = useDispatch(),\r\n  [{isDragging}, drag] = useDrag(() => ({\r\n    type: 'plant',\r\n    item: plant,\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging(),\r\n    }),\r\n  })),\r\n  [{isOver}, drop] = useDrop(() => ({\r\n    accept: 'plant',\r\n    drop: (droppedItem: Plant) => {\r\n      dispatch(moveItem({ existingItem: plant, movingItem: droppedItem }));\r\n    },\r\n    collect: (monitor) => ({\r\n      isOver: monitor.isOver(),\r\n    }),\r\n  }));\r\n\r\n  drag(drop(ref));\r\n\r\n  return (\r\n    <tr key={plant._id} ref={ref}>\r\n      <td>\r\n        <div \r\n          className=\"cursor-move\"\r\n          // @ts-ignore\r\n          ref={drag}>\r\n          <FontAwesomeIcon icon={['fat', 'grip-vertical']} />\r\n        </div>\r\n      </td>\r\n      <td>\r\n        <Link to={routes.plants.routes.detail.to(plant._id)}>\r\n          <FontAwesomeIcon icon={['fat', 'edit']} />\r\n        </Link>\r\n      </td>\r\n      <td>{plant.abbreviation}</td>\r\n      <td>{plant.name}</td>\r\n      <td className=\"text-center\">{plant.cuttingsPerPot}</td>\r\n      <td className=\"text-center\">{plant.potsPerCase}</td>\r\n      <td className=\"text-center\">\r\n        {plant.hasLightsOut &&\r\n          <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n        }\r\n      </td>\r\n      <td className=\"text-center\">\r\n        {plant.hasPinching &&\r\n          <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n        }\r\n      </td>\r\n    </tr>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,OAAO,EAAEC,OAAO,QAAQ,WAAW;AAC5C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,WAAW,QAAQ,aAAa;AAAC;AAM1C,OAAO,SAASC,aAAa,OAA8B;EAAA;EAAA,IAA7B;IAACC;EAAyB,CAAC;EACvD,MAAMC,GAAG,GAAGR,MAAM,CAAsB,IAAI,CAAC;IAC7CS,QAAQ,GAAGJ,WAAW,EAAE;IACxB,CAAC;MAACK;IAAU,CAAC,EAAEC,IAAI,CAAC,GAAGV,OAAO,CAAC,OAAO;MACpCW,IAAI,EAAE,OAAO;MACbC,IAAI,EAAEN,KAAK;MACXO,OAAO,EAAGC,OAAO,KAAM;QACrBL,UAAU,EAAEK,OAAO,CAACL,UAAU;MAChC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,CAAC;MAACM;IAAM,CAAC,EAAEC,IAAI,CAAC,GAAGf,OAAO,CAAC,OAAO;MAChCgB,MAAM,EAAE,OAAO;MACfD,IAAI,EAAGE,WAAkB,IAAK;QAC5BV,QAAQ,CAACL,QAAQ,CAAC;UAAEgB,YAAY,EAAEb,KAAK;UAAEc,UAAU,EAAEF;QAAY,CAAC,CAAC,CAAC;MACtE,CAAC;MACDL,OAAO,EAAGC,OAAO,KAAM;QACrBC,MAAM,EAAED,OAAO,CAACC,MAAM;MACxB,CAAC;IACH,CAAC,CAAC,CAAC;EAEHL,IAAI,CAACM,IAAI,CAACT,GAAG,CAAC,CAAC;EAEf,oBACE;IAAoB,GAAG,EAAEA,GAAI;IAAA,wBAC3B;MAAA,uBACE;QACE,SAAS,EAAC;QACV;QAAA;QACA,GAAG,EAAEG,IAAK;QAAA,uBACV,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,eAAe;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IAC/C;MAAA;MAAA;MAAA;IAAA,QACH,eACL;MAAA,uBACE,QAAC,IAAI;QAAC,EAAE,EAAEZ,MAAM,CAACuB,MAAM,CAACvB,MAAM,CAACwB,MAAM,CAACC,EAAE,CAACjB,KAAK,CAACkB,GAAG,CAAE;QAAA,uBAClD,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IACrC;MAAA;MAAA;MAAA;IAAA,QACJ,eACL;MAAA,UAAKlB,KAAK,CAACmB;IAAY;MAAA;MAAA;MAAA;IAAA,QAAM,eAC7B;MAAA,UAAKnB,KAAK,CAACoB;IAAI;MAAA;MAAA;MAAA;IAAA,QAAM,eACrB;MAAI,SAAS,EAAC,aAAa;MAAA,UAAEpB,KAAK,CAACqB;IAAc;MAAA;MAAA;MAAA;IAAA,QAAM,eACvD;MAAI,SAAS,EAAC,aAAa;MAAA,UAAErB,KAAK,CAACsB;IAAW;MAAA;MAAA;MAAA;IAAA,QAAM,eACpD;MAAI,SAAS,EAAC,aAAa;MAAA,UACxBtB,KAAK,CAACuB,YAAY,iBACjB,QAAC,eAAe;QAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;MAAE;QAAA;QAAA;QAAA;MAAA;IAAG;MAAA;MAAA;MAAA;IAAA,QAEjD,eACL;MAAI,SAAS,EAAC,aAAa;MAAA,UACxBvB,KAAK,CAACwB,WAAW,iBAChB,QAAC,eAAe;QAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;MAAE;QAAA;QAAA;QAAA;MAAA;IAAG;MAAA;MAAA;MAAA;IAAA,QAEjD;EAAA,GA3BExB,KAAK,CAACkB,GAAG;IAAA;IAAA;IAAA;EAAA,QA4Bb;AAET;AAAC,GArDenB,aAAa;EAAA,QAEhBD,WAAW,EACCJ,OAAO,EAOXC,OAAO;AAAA;AAAA,KAVZI,aAAa;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}