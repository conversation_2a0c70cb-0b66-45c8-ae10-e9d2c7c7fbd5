@import '../styles/variables';
@import '../../node_modules/bootstrap/scss/bootstrap.scss';

body {
  padding-top: $navbar-height;
}

header .navbar-brand > img {
  max-height: 40px;
}

.border-top-double {
  @extend .border-top;
  border-top-style: double !important;
  border-top-width: 5px !important;
}

.min-w-75px {
  min-width: 75px;
}
.min-w-100px {
  min-width: 100px;
}

.max-w-75px {
  max-width: 75px;
}
.max-w-100px {
  max-width: 100px;
}
.max-w-120px {
  max-width: 120px;
}

.w-100px {
  width: 100px;
}

.h-100-vh {
  height: calc(100vh - #{$navbar-height});
}

.sticky-top-navbar {
  @extend .sticky-top;
  top: $navbar-height - 9;
}

.sticky-bottom {
  @extend .sticky-top;
  top: auto;
  bottom: 0;
}

.sticky-left {
  @extend .sticky-top;
  top: auto;
  left: 0;
}

.bg-light-gray {
  background-color: lightgray !important;
}

.cursor-pointer {
  cursor: pointer;
}
