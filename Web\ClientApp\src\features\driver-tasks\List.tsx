import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { events, EventTypes } from 'app/events';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { Error } from 'features/error/Error';
import { Loading } from 'features/loading/Loading';
import {
  getDriverTasks,
  selectError,
  selectIsLoading,
  selectTaskDates,
  clearError,
  getDrivers,
} from './driver-task-slice';
import { ListDate } from './List-Date';
import { ListFilters } from './ListFilters';
import { classNames } from 'utils/class-names';

export function List() {
  const dispatch = useDispatch(),
    { isInRole, user } = useAuth(),
    [showFilterMobile, setShowFilterMobile] = useState(false),
    error = useSelector(selectError),
    isLoading = useSelector(selectIsLoading),
    taskDates = useSelector(selectTaskDates),
    canCreateDriverTasks = isInRole('create:driver-tasks');

  const refreshTasks = useCallback(() => {
    dispatch(getDriverTasks());
  }, [dispatch]);

  useEffect(() => {
    if (user) {
      dispatch(getDrivers(user));
    }
  }, [dispatch, user]);

  useEffect(() => {
    events.on(EventTypes.driverTasksUpdated, refreshTasks);

    refreshTasks();

    return function cleanup() {
      events.off(EventTypes.driverTasksUpdated);
    };
  }, [refreshTasks]);

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleToggleFilter = () => {
    setShowFilterMobile(!showFilterMobile);
  };

  return (
    <div className="container-fluid">
      <div className="bg-white sticky-top-navbar mb-4">
        <div className="container mx-auto row mt-2 py-2 border-bottom shadow">
          <div className="row my-2">
            <h1 className="col">
              <FontAwesomeIcon icon={['fat', 'truck-fast']} />
              &nbsp; Driver Tasks
            </h1>
            {canCreateDriverTasks && (
              <div className="col-auto">
                <Button
                  tag={Link}
                  to={routes.driverTasks.new.path}
                  outline
                  color="success"
                  className="d-block d-md-inline-block">
                  <FontAwesomeIcon icon={['fat', 'plus']} />
                  <span className="d-none d-md-inline">
                    &nbsp; New Driver Task
                  </span>
                </Button>
              </div>
            )}
          </div>
          <div className="row d-none d-md-flex">
            <ListFilters show={true} />
          </div>
          <div className="row d-md-none">
            <ListFilters show={showFilterMobile} />
          </div>
          <div
            className={classNames(
              'row d-md-none',
              showFilterMobile ? 'mt-4' : 'mt-2'
            )}>
            <div className="col-12">
              <Button
                color="secondary"
                size="sm"
                outline
                block
                onClick={handleToggleFilter}
                tabIndex={-1}>
                {`${showFilterMobile ? 'Hide' : 'Show'} filters`}
                &nbsp;
                <FontAwesomeIcon
                  icon={[
                    'fat',
                    showFilterMobile ? 'chevron-up' : 'chevron-down',
                  ]}
                />
              </Button>
            </div>
          </div>
        </div>
      </div>
      {!!error && (
        <div className="container row mx-auto">
          <div className="col">
            <Error error={error} clearError={handleClearErrorClick} />
          </div>
        </div>
      )}
      {!!isLoading && <Loading />}
      <div className="container mx-auto mb-4">
        <div className="row">
          {taskDates.map((date) => (
            <ListDate key={date} date={date} />
          ))}
        </div>
      </div>
    </div>
  );
}

export default List;
