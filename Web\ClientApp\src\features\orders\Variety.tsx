import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Button, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { OrderVariety } from 'api/models/orders';
import { useAuth } from 'features/auth/use-auth';
import { selectOrder } from './detail-slice';
import { handleFocus } from 'utils/focus';
import { equals } from 'utils/equals';

interface VarietyProps {
  variety: OrderVariety;
  onChange: (variety: OrderVariety | null) => void;
}

export function Variety({ variety, onChange }: VarietyProps) {
  const { isInRole } = useAuth(),
    [name, setName] = useState(''),
    [cuttings, setCuttings] = useState(0),
    [pots, setPots] = useState(0),
    [cases, setCases] = useState(0),
    [comment, setComment] = useState<string | null>(null),
    [isOther, setIsOther] = useState(false),
    order = useSelector(selectOrder),
    varieties = (order?.plant.varieties || []).map((v) => v.name),
    inUse = (order?.varieties || []).map((v) => v.name),
    unusedVarieties = varieties.filter((v) => inUse.indexOf(v) === -1),
    canUpdate = isInRole('update:orders');

  if (!isOther) {
    if (unusedVarieties.indexOf(name) === -1) {
      unusedVarieties.push(name);
    }
    unusedVarieties.sort();
    unusedVarieties.push('Other');
  }

  useEffect(() => {
    setName(variety.name);
    setCuttings(variety.cuttings);
    setPots(variety.pots);
    setCases(variety.cases);
    setComment(variety.comment);

    const isOther =
      !!variety.name &&
      !(order?.plant.varieties || []).find((v) => v.name === variety.name);
    setIsOther(isOther);
  }, [variety, order]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;

    if (name === 'Other') {
      setName('');
      setIsOther(true);
    } else {
      setName(name);
      const isOther = !varieties.find((v) => equals(v, name));
      setIsOther(isOther);
    }
  };

  const handleCuttingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const { plant } = order,
        cuttings = e.target.valueAsNumber || 0,
        cuttingsPerPot = plant.cuttingsPerPot || 1,
        potsPerCase = plant.potsPerCase || 1,
        pots = Math.round(cuttings / cuttingsPerPot),
        cases = Math.round(pots / potsPerCase);

      setCuttings(cuttings);
      setPots(pots);
      setCases(cases);
    }
  };

  const handlePotsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (order) {
      const { plant } = order,
        pots = e.target.valueAsNumber || 0,
        potsPerCase = plant.potsPerCase || 1,
        cases = Math.round(pots / potsPerCase);

      setPots(pots);
      setCases(cases);
    }
  };

  const handleCasesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cases = e.target.valueAsNumber || 0;

    setCases(cases);
  };

  const handleCommentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const comment = e.target.value || null;

    setComment(comment);
  };

  const handleBlur = () => {
    const update = { ...variety, name, cuttings, pots, cases, comment };
    onChange(update);
  };

  const handleDeleteClick = () => {
    onChange(null);
  };

  return (
    <div className="row pt-2 mt-2 border-top">
      {canUpdate && (
        <div className="col-12 col-md-1 text-center">
          <Button
            color="danger"
            outline
            size="sm"
            onClick={handleDeleteClick}
            className="mt-1">
            <FontAwesomeIcon icon={['fat', 'trash']} />
          </Button>
        </div>
      )}
      <div className="col-12 col-md-2">
        {isOther && (
          <Input
            value={name}
            onChange={handleNameChange}
            disabled={!canUpdate}
          />
        )}
        {!isOther && (
          <Input
            type="select"
            value={name}
            onChange={handleNameChange}
            disabled={!canUpdate}>
            {unusedVarieties.map((v) => (
              <option key={v} value={v}>
                {v}
              </option>
            ))}
          </Input>
        )}
      </div>
      <div className="col-12 col-md-2">
        <Input
          type="number"
          className="text-end"
          value={cuttings}
          onChange={handleCuttingsChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          disabled={!canUpdate}
        />
      </div>
      <div className="col-12 col-md-2">
        <Input
          type="number"
          className="text-end"
          value={pots}
          onChange={handlePotsChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          disabled={!canUpdate}
        />
      </div>
      <div className="col-12 col-md-2">
        <Input
          type="number"
          className="text-end"
          value={cases}
          onChange={handleCasesChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          disabled={!canUpdate}
        />
      </div>
      <div className="col-12 col-md-3">
        <Input
          value={comment || ''}
          onChange={handleCommentChange}
          onBlur={handleBlur}
          disabled={!canUpdate}
        />
      </div>
    </div>
  );
}
