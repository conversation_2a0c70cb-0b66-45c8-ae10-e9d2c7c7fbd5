{"ast": null, "code": "import { useEffect } from 'react';\nimport { useCollector } from './useCollector.js';\nimport { useDragDropManager } from './useDragDropManager.js';\n/**\n * useDragLayer Hook\n * @param collector The property collector\n */\nexport function useDragLayer(collect) {\n  const dragDropManager = useDragDropManager();\n  const monitor = dragDropManager.getMonitor();\n  const [collected, updateCollected] = useCollector(monitor, collect);\n  useEffect(() => monitor.subscribeToOffsetChange(updateCollected));\n  useEffect(() => monitor.subscribeToStateChange(updateCollected));\n  return collected;\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AAGjC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,kBAAkB,QAAQ,yBAAyB;AAE5D;;;;AAIA,OAAO,SAASC,YAAY,CAC3BC,OAAkE,EACjD;EACjB,MAAMC,eAAe,GAAGH,kBAAkB,EAAE;EAC5C,MAAMI,OAAO,GAAGD,eAAe,CAACE,UAAU,EAAE;EAC5C,MAAM,CAACC,SAAS,EAAEC,eAAe,CAAC,GAAGR,YAAY,CAACK,OAAO,EAAEF,OAAO,CAAC;EAEnEJ,SAAS,CAAC,MAAMM,OAAO,CAACI,uBAAuB,CAACD,eAAe,CAAC,CAAC;EACjET,SAAS,CAAC,MAAMM,OAAO,CAACK,sBAAsB,CAACF,eAAe,CAAC,CAAC;EAChE,OAAOD,SAAS", "names": ["useEffect", "useCollector", "useDragDropManager", "useDragLayer", "collect", "dragDropManager", "monitor", "getMonitor", "collected", "updateCollected", "subscribeToOffsetChange", "subscribeToStateChange"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDragLayer.ts"], "sourcesContent": ["import { useEffect } from 'react'\n\nimport type { DragLayerMonitor } from '../types/index.js'\nimport { useCollector } from './useCollector.js'\nimport { useDragDropManager } from './useDragDropManager.js'\n\n/**\n * useDragLayer Hook\n * @param collector The property collector\n */\nexport function useDragLayer<CollectedProps, DragObject = any>(\n\tcollect: (monitor: DragLayerMonitor<DragObject>) => CollectedProps,\n): CollectedProps {\n\tconst dragDropManager = useDragDropManager()\n\tconst monitor = dragDropManager.getMonitor()\n\tconst [collected, updateCollected] = useCollector(monitor, collect)\n\n\tuseEffect(() => monitor.subscribeToOffsetChange(updateCollected))\n\tuseEffect(() => monitor.subscribeToStateChange(updateCollected))\n\treturn collected\n}\n"]}, "metadata": {}, "sourceType": "module"}