import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectPlants } from './plants-slice';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { PlantListItem } from './List-Item';

export function List() {
  const plants = useSelector(selectPlants),
    {isInRole} = useAuth(),
    canCreate = isInRole('create:plants');
  
  return (
    <div className="container d-grid gap-2">
      <div className="row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow">
        <h1 className="col pt-2">
          <FontAwesomeIcon icon={['fat', 'seedling']} />
          &nbsp;
          Plants List
        </h1>
        {canCreate &&
          <div className="col-auto pt-3">
            <Button tag={Link} to={routes.plants.routes.new()} outline color="success">
              <FontAwesomeIcon icon={['fat', 'plus']} />
              &nbsp;
              New Plant
            </Button>
          </div>
        }
      </div>
      <DndProvider backend={HTML5Backend}>
        <table className="table">
          <thead>
            <tr className="sticky-top bg-white" style={{top: '140px'}}>
              <td>&nbsp;</td>
              <th>&nbsp;</th>
              <th>Abbreviation</th>
              <th>Name</th>
              <th className="text-center">Cuttings/Pot</th>
              <th className="text-center">Pots/Case</th>
              <th className="text-center">Lights Out?</th>
              <th className="text-center">Pinching?</th>
            </tr>
          </thead>
          <tbody>
            {plants.map(plant =>
              <PlantListItem key={plant._id} plant={plant} />
            )}
          </tbody>
        </table>
      </DndProvider>
    </div>
  )
}

export default List;
