import { useDispatch, useSelector } from 'react-redux';
import { Collapse, FormGroup, Input, Label } from 'reactstrap';
import * as models from 'api/models/driver-tasks';
import {
  selectFilter,
  setFilter,
  selectDrivers,
  UnassignedDriverKey,
  selectFromLocations,
  selectToLocations,
} from './driver-task-slice';

interface ListFiltersProps {
  show: boolean;
}

export function ListFilters({ show }: ListFiltersProps) {
  const dispatch = useDispatch(),
    filter = useSelector(selectFilter),
    drivers = useSelector(selectDrivers),
    fromLocations = useSelector(selectFromLocations),
    toLocations = useSelector(selectToLocations);

  const handleFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const from = e.target.value,
      updated = { ...filter, from };

    dispatch(setFilter(updated));
  };

  const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const to = e.target.value,
      updated = { ...filter, to };

    dispatch(setFilter(updated));
  };

  const handleShowCompleteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const showComplete = e.target.checked,
      updated = { ...filter, showComplete };

    dispatch(setFilter(updated));
  };

  const handleAssignedToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const assignedTo = e.target.value,
      updated = { ...filter, assignedTo };

    dispatch(setFilter(updated));
  };

  const handlePriorityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const priority = e.target.value as models.Priority,
      updated = { ...filter, priority };

    dispatch(setFilter(updated));
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const status = e.target.value as models.Status,
      updated = { ...filter, status };

    dispatch(setFilter(updated));
  };

  const handleFromLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fromLocation = e.target.value,
      updated = { ...filter, fromLocation };

    dispatch(setFilter(updated));
  };

  const handleToLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const toLocation = e.target.value,
      updated = { ...filter, toLocation };

    dispatch(setFilter(updated));
  };

  return (
    <Collapse isOpen={show}>
      <div className="row">
        <div className="col-6 col-lg-auto">
          <label htmlFor="driver-list-from-filter">From</label>
          <Input
            type="date"
            id="driver-list-from-filter"
            bsSize="sm"
            value={filter.from}
            onChange={handleFromChange}
          />
        </div>
        <div className="col-6 col-lg-auto">
          <label htmlFor="driver-list-to-filter">To</label>
          <Input
            type="date"
            id="driver-list-to-filter"
            bsSize="sm"
            value={filter.to}
            onChange={handleToChange}
          />
        </div>
        <div className="col-12 col-lg-auto">
          <label
            htmlFor="driver-list-show-complete-filter"
            className="invisible">
            Show Complete
          </label>
          <FormGroup switch>
            <Input
              type="switch"
              id="driver-list-show-complete-filter"
              checked={filter.showComplete}
              onChange={handleShowCompleteChange}
            />
            <Label htmlFor="driver-list-show-complete-filter" check>
              Show Complete
            </Label>
          </FormGroup>
        </div>
      </div>
      <div className="row">
        <div className="col-6 col-lg-auto">
          <label htmlFor="driver-list-assigned-to-filter">Driver</label>
          <Input
            type="select"
            id="driver-list-assigned-to-filter"
            bsSize="sm"
            value={filter.assignedTo}
            onChange={handleAssignedToChange}>
            <option value="">Show All</option>
            <option value={UnassignedDriverKey}>Show Unassigned</option>
            {drivers.map((driver) => (
              <option key={driver.name} value={driver.name}>
                {driver.name}
              </option>
            ))}
          </Input>
        </div>
        <div className="col-6 col-lg-auto">
          <label htmlFor="driver-list-status-filter">Status</label>
          <Input
            type="select"
            id="driver-list-status-filter"
            bsSize="sm"
            value={filter.status}
            onChange={handleStatusChange}>
            <option value="">Show All</option>
            <option value={models.NotStartedStatus}>Not Started</option>
            <option value={models.InProgressStatus}>In Progress</option>
            <option value={models.CompleteStatus}>Complete</option>
          </Input>
        </div>
        <div className="col-6 col-lg-auto">
          <label htmlFor="driver-list-priority-filter">Priority</label>
          <Input
            type="select"
            id="driver-list-priority-filter"
            bsSize="sm"
            value={filter.priority}
            onChange={handlePriorityChange}>
            <option value="">Show All</option>
            <option value={models.HighPriority}>High</option>
            <option value={models.NormalPriority}>Normal</option>
            <option value={models.LowPriority}>Low</option>
          </Input>
        </div>
        <div className="col-6 col-lg-auto">
          <label htmlFor="driver-list-from-location-filter">
            From Location
          </label>
          <Input
            type="select"
            id="driver-list-from-location-filter"
            bsSize="sm"
            value={filter.fromLocation}
            onChange={handleFromLocationChange}>
            <option value="">Show All</option>
            {fromLocations.map((f) => (
              <option key={f} value={f}>
                {f}
              </option>
            ))}
          </Input>
        </div>
        <div className="col-6 col-lg-auto">
          <label htmlFor="driver-list-to-location-filter">To Location</label>
          <Input
            type="select"
            id="driver-list-to-location-filter"
            bsSize="sm"
            value={filter.toLocation}
            onChange={handleToLocationChange}>
            <option value="">Show All</option>
            {toLocations.map((t) => (
              <option key={t} value={t}>
                {t}
              </option>
            ))}
          </Input>
        </div>
      </div>
    </Collapse>
  );
}
