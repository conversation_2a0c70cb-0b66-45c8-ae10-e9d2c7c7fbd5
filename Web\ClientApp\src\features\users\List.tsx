import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { getAllUsers, selectError, selectIsLoading, selectUsers } from './users-slice';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { Error } from 'features/error/Error';
import { Loading } from 'features/loading/Loading';

function List() {
  const dispatch = useDispatch(),
    users = useSelector(selectUsers),
    error = useSelector(selectError),
    isLoading = useSelector(selectIsLoading),
    {user} = useAuth();

  useEffect(() => {
    if(user) {
      dispatch(getAllUsers(user));
    }
  }, [user, dispatch]);

  return (
    <div className="container">
      <div className="row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow">
        <div className="col-12 row my-2">
          <h1 className="col">
            <FontAwesomeIcon icon={['fat', 'user']} />
            &nbsp;
            Users
          </h1>
        </div>
      </div>
      {isLoading &&
        <Loading />
      }
      <Error error={error} />
      {!isLoading && !error &&
        <table className="table">
            <thead>
              <tr className="sticky-top bg-white" style={{top: '145px'}}>
                <th>&nbsp;</th>
                <th>User</th>
                <th>Email</th>
                <th>Phone</th>
              </tr>
            </thead>
            <tbody>
              {users.map(user =>
                <tr key={user.name}>
                  <td>
                    <Link to={routes.users.routes.detail.to(user.name)}>
                      <FontAwesomeIcon icon={['fat', 'edit']} />
                    </Link>
                  </td>
                  <td>{user.name}</td>
                  <td>{user.email}</td>
                  <td>{user.phone}</td>
                </tr>
              )}
          </tbody>
        </table>
      }
    </div>
  );
}

export default List;
