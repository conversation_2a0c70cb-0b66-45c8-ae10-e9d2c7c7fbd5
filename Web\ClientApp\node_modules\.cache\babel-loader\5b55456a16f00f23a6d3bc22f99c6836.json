{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { useMemo } from 'react';\n/**\n * Internal utility hook to get an array-version of spec.accept.\n * The main utility here is that we aren't creating a new array on every render if a non-array spec.accept is passed in.\n * @param spec\n */\nexport function useAccept(spec) {\n  const {\n    accept\n  } = spec;\n  return useMemo(() => {\n    invariant(spec.accept != null, 'accept must be defined');\n    return Array.isArray(accept) ? accept : [accept];\n  }, [accept]);\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAEhD,SAASC,OAAO,QAAQ,OAAO;AAI/B;;;;;AAKA,OAAO,SAASC,SAAS,CACxBC,IAAiC,EAClB;EACf,MAAM;IAAEC;EAAM,CAAE,GAAGD,IAAI;EACvB,OAAOF,OAAO,CAAC,MAAM;IACpBD,SAAS,CAACG,IAAI,CAACC,MAAM,IAAI,IAAI,EAAE,wBAAwB,CAAC;IACxD,OAAOC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;GAChD,EAAE,CAACA,MAAM,CAAC,CAAC", "names": ["invariant", "useMemo", "useAccept", "spec", "accept", "Array", "isArray"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrop\\useAccept.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { Identifier } from 'dnd-core'\nimport { useMemo } from 'react'\n\nimport type { DropTargetHookSpec } from '../types.js'\n\n/**\n * Internal utility hook to get an array-version of spec.accept.\n * The main utility here is that we aren't creating a new array on every render if a non-array spec.accept is passed in.\n * @param spec\n */\nexport function useAccept<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n): Identifier[] {\n\tconst { accept } = spec\n\treturn useMemo(() => {\n\t\tinvariant(spec.accept != null, 'accept must be defined')\n\t\treturn Array.isArray(accept) ? accept : [accept]\n\t}, [accept])\n}\n"]}, "metadata": {}, "sourceType": "module"}