{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { getDifferenceFromInitialOffset, getSourceClientOffset } from '../utils/coords.js';\nimport { areDirty } from '../utils/dirtiness.js';\nimport { matchesType } from '../utils/matchesType.js';\nexport class DragDropMonitorImpl {\n  subscribeToStateChange(listener) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const {\n      handlerIds\n    } = options;\n    invariant(typeof listener === 'function', 'listener must be a function.');\n    invariant(typeof handlerIds === 'undefined' || Array.isArray(handlerIds), 'handlerIds, when specified, must be an array of strings.');\n    let prevStateId = this.store.getState().stateId;\n    const handleChange = () => {\n      const state = this.store.getState();\n      const currentStateId = state.stateId;\n      try {\n        const canSkipListener = currentStateId === prevStateId || currentStateId === prevStateId + 1 && !areDirty(state.dirtyHandlerIds, handlerIds);\n        if (!canSkipListener) {\n          listener();\n        }\n      } finally {\n        prevStateId = currentStateId;\n      }\n    };\n    return this.store.subscribe(handleChange);\n  }\n  subscribeToOffsetChange(listener) {\n    invariant(typeof listener === 'function', 'listener must be a function.');\n    let previousState = this.store.getState().dragOffset;\n    const handleChange = () => {\n      const nextState = this.store.getState().dragOffset;\n      if (nextState === previousState) {\n        return;\n      }\n      previousState = nextState;\n      listener();\n    };\n    return this.store.subscribe(handleChange);\n  }\n  canDragSource(sourceId) {\n    if (!sourceId) {\n      return false;\n    }\n    const source = this.registry.getSource(sourceId);\n    invariant(source, `Expected to find a valid source. sourceId=${sourceId}`);\n    if (this.isDragging()) {\n      return false;\n    }\n    return source.canDrag(this, sourceId);\n  }\n  canDropOnTarget(targetId) {\n    // undefined on initial render\n    if (!targetId) {\n      return false;\n    }\n    const target = this.registry.getTarget(targetId);\n    invariant(target, `Expected to find a valid target. targetId=${targetId}`);\n    if (!this.isDragging() || this.didDrop()) {\n      return false;\n    }\n    const targetType = this.registry.getTargetType(targetId);\n    const draggedItemType = this.getItemType();\n    return matchesType(targetType, draggedItemType) && target.canDrop(this, targetId);\n  }\n  isDragging() {\n    return Boolean(this.getItemType());\n  }\n  isDraggingSource(sourceId) {\n    // undefined on initial render\n    if (!sourceId) {\n      return false;\n    }\n    const source = this.registry.getSource(sourceId, true);\n    invariant(source, `Expected to find a valid source. sourceId=${sourceId}`);\n    if (!this.isDragging() || !this.isSourcePublic()) {\n      return false;\n    }\n    const sourceType = this.registry.getSourceType(sourceId);\n    const draggedItemType = this.getItemType();\n    if (sourceType !== draggedItemType) {\n      return false;\n    }\n    return source.isDragging(this, sourceId);\n  }\n  isOverTarget(targetId) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      shallow: false\n    };\n    // undefined on initial render\n    if (!targetId) {\n      return false;\n    }\n    const {\n      shallow\n    } = options;\n    if (!this.isDragging()) {\n      return false;\n    }\n    const targetType = this.registry.getTargetType(targetId);\n    const draggedItemType = this.getItemType();\n    if (draggedItemType && !matchesType(targetType, draggedItemType)) {\n      return false;\n    }\n    const targetIds = this.getTargetIds();\n    if (!targetIds.length) {\n      return false;\n    }\n    const index = targetIds.indexOf(targetId);\n    if (shallow) {\n      return index === targetIds.length - 1;\n    } else {\n      return index > -1;\n    }\n  }\n  getItemType() {\n    return this.store.getState().dragOperation.itemType;\n  }\n  getItem() {\n    return this.store.getState().dragOperation.item;\n  }\n  getSourceId() {\n    return this.store.getState().dragOperation.sourceId;\n  }\n  getTargetIds() {\n    return this.store.getState().dragOperation.targetIds;\n  }\n  getDropResult() {\n    return this.store.getState().dragOperation.dropResult;\n  }\n  didDrop() {\n    return this.store.getState().dragOperation.didDrop;\n  }\n  isSourcePublic() {\n    return Boolean(this.store.getState().dragOperation.isSourcePublic);\n  }\n  getInitialClientOffset() {\n    return this.store.getState().dragOffset.initialClientOffset;\n  }\n  getInitialSourceClientOffset() {\n    return this.store.getState().dragOffset.initialSourceClientOffset;\n  }\n  getClientOffset() {\n    return this.store.getState().dragOffset.clientOffset;\n  }\n  getSourceClientOffset() {\n    return getSourceClientOffset(this.store.getState().dragOffset);\n  }\n  getDifferenceFromInitialOffset() {\n    return getDifferenceFromInitialOffset(this.store.getState().dragOffset);\n  }\n  constructor(store, registry) {\n    this.store = store;\n    this.registry = registry;\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAYhD,SACCC,8BAA8B,EAC9BC,qBAAqB,QACf,oBAAoB;AAC3B,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,OAAO,MAAMC,mBAAmB;EAS/BC,sBAA6B,CAC5BC,QAAkB,EAEJ;IAAA,IADdC,OAAkC,uEAAG,EAAE;IAEvC,MAAM;MAAEC;IAAU,CAAE,GAAGD,OAAO;IAC9BR,SAAS,CAAC,OAAOO,QAAQ,KAAK,UAAU,EAAE,8BAA8B,CAAC;IACzEP,SAAS,CACR,OAAOS,UAAU,KAAK,WAAW,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAC9D,0DAA0D,CAC1D;IAED,IAAIG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,EAAE,CAACC,OAAO;IAC/C,MAAMC,YAAY,GAAG,MAAM;MAC1B,MAAMC,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACC,QAAQ,EAAE;MACnC,MAAMI,cAAc,GAAGD,KAAK,CAACF,OAAO;MACpC,IAAI;QACH,MAAMI,eAAe,GACpBD,cAAc,KAAKN,WAAW,IAC7BM,cAAc,KAAKN,WAAW,GAAG,CAAC,IAClC,CAACT,QAAQ,CAACc,KAAK,CAACG,eAAe,EAAEX,UAAU,CAAC;QAE9C,IAAI,CAACU,eAAe,EAAE;UACrBZ,QAAQ,EAAE;;OAEX,SAAS;QACTK,WAAW,GAAGM,cAAc;;KAE7B;IAED,OAAO,IAAI,CAACL,KAAK,CAACQ,SAAS,CAACL,YAAY,CAAC;;EAG1CM,uBAA8B,CAACf,QAAkB,EAAe;IAC/DP,SAAS,CAAC,OAAOO,QAAQ,KAAK,UAAU,EAAE,8BAA8B,CAAC;IAEzE,IAAIgB,aAAa,GAAG,IAAI,CAACV,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU;IACpD,MAAMR,YAAY,GAAG,MAAM;MAC1B,MAAMS,SAAS,GAAG,IAAI,CAACZ,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU;MAClD,IAAIC,SAAS,KAAKF,aAAa,EAAE;QAChC;;MAGDA,aAAa,GAAGE,SAAS;MACzBlB,QAAQ,EAAE;KACV;IAED,OAAO,IAAI,CAACM,KAAK,CAACQ,SAAS,CAACL,YAAY,CAAC;;EAG1CU,aAAoB,CAACC,QAA4B,EAAW;IAC3D,IAAI,CAACA,QAAQ,EAAE;MACd,OAAO,KAAK;;IAEb,MAAMC,MAAM,GAAG,IAAI,CAACC,QAAQ,CAACC,SAAS,CAACH,QAAQ,CAAC;IAChD3B,SAAS,CAAC4B,MAAM,EAAG,6CAA4CD,QAAS,EAAC,CAAC;IAE1E,IAAI,IAAI,CAACI,UAAU,EAAE,EAAE;MACtB,OAAO,KAAK;;IAGb,OAAOH,MAAM,CAACI,OAAO,CAAC,IAAI,EAAEL,QAAQ,CAAC;;EAGtCM,eAAsB,CAACC,QAA4B,EAAW;IAC7D;IACA,IAAI,CAACA,QAAQ,EAAE;MACd,OAAO,KAAK;;IAEb,MAAMC,MAAM,GAAG,IAAI,CAACN,QAAQ,CAACO,SAAS,CAACF,QAAQ,CAAC;IAChDlC,SAAS,CAACmC,MAAM,EAAG,6CAA4CD,QAAS,EAAC,CAAC;IAE1E,IAAI,CAAC,IAAI,CAACH,UAAU,EAAE,IAAI,IAAI,CAACM,OAAO,EAAE,EAAE;MACzC,OAAO,KAAK;;IAGb,MAAMC,UAAU,GAAG,IAAI,CAACT,QAAQ,CAACU,aAAa,CAACL,QAAQ,CAAC;IACxD,MAAMM,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;IAC1C,OACCrC,WAAW,CAACkC,UAAU,EAAEE,eAAe,CAAC,IAAIL,MAAM,CAACO,OAAO,CAAC,IAAI,EAAER,QAAQ,CAAC;;EAI5EH,UAAiB,GAAY;IAC5B,OAAOY,OAAO,CAAC,IAAI,CAACF,WAAW,EAAE,CAAC;;EAGnCG,gBAAuB,CAACjB,QAA4B,EAAW;IAC9D;IACA,IAAI,CAACA,QAAQ,EAAE;MACd,OAAO,KAAK;;IAEb,MAAMC,MAAM,GAAG,IAAI,CAACC,QAAQ,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,CAAC;IACtD3B,SAAS,CAAC4B,MAAM,EAAG,6CAA4CD,QAAS,EAAC,CAAC;IAE1E,IAAI,CAAC,IAAI,CAACI,UAAU,EAAE,IAAI,CAAC,IAAI,CAACc,cAAc,EAAE,EAAE;MACjD,OAAO,KAAK;;IAGb,MAAMC,UAAU,GAAG,IAAI,CAACjB,QAAQ,CAACkB,aAAa,CAACpB,QAAQ,CAAC;IACxD,MAAMa,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;IAC1C,IAAIK,UAAU,KAAKN,eAAe,EAAE;MACnC,OAAO,KAAK;;IAGb,OAAOZ,MAAM,CAACG,UAAU,CAAC,IAAI,EAAEJ,QAAQ,CAAC;;EAGzCqB,YAAmB,CAClBd,QAA4B,EAElB;IAAA,IADV1B,OAAO,uEAAG;MAAEyC,OAAO,EAAE;KAAO;IAE5B;IACA,IAAI,CAACf,QAAQ,EAAE;MACd,OAAO,KAAK;;IAGb,MAAM;MAAEe;IAAO,CAAE,GAAGzC,OAAO;IAC3B,IAAI,CAAC,IAAI,CAACuB,UAAU,EAAE,EAAE;MACvB,OAAO,KAAK;;IAGb,MAAMO,UAAU,GAAG,IAAI,CAACT,QAAQ,CAACU,aAAa,CAACL,QAAQ,CAAC;IACxD,MAAMM,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;IAC1C,IAAID,eAAe,IAAI,CAACpC,WAAW,CAACkC,UAAU,EAAEE,eAAe,CAAC,EAAE;MACjE,OAAO,KAAK;;IAGb,MAAMU,SAAS,GAAG,IAAI,CAACC,YAAY,EAAE;IACrC,IAAI,CAACD,SAAS,CAACE,MAAM,EAAE;MACtB,OAAO,KAAK;;IAGb,MAAMC,KAAK,GAAGH,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAAC;IACzC,IAAIe,OAAO,EAAE;MACZ,OAAOI,KAAK,KAAKH,SAAS,CAACE,MAAM,GAAG,CAAC;KACrC,MAAM;MACN,OAAOC,KAAK,GAAG,CAAC,CAAC;;;EAInBZ,WAAkB,GAAe;IAChC,OAAO,IAAI,CAAC5B,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACC,QAAQ;;EAGpDC,OAAc,GAAQ;IACrB,OAAO,IAAI,CAAC5C,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACG,IAAI;;EAGhDC,WAAkB,GAAkB;IACnC,OAAO,IAAI,CAAC9C,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAAC5B,QAAQ;;EAGpDwB,YAAmB,GAAa;IAC/B,OAAO,IAAI,CAACtC,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACL,SAAS;;EAGrDU,aAAoB,GAAQ;IAC3B,OAAO,IAAI,CAAC/C,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACM,UAAU;;EAGtDxB,OAAc,GAAY;IACzB,OAAO,IAAI,CAACxB,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAAClB,OAAO;;EAGnDQ,cAAqB,GAAY;IAChC,OAAOF,OAAO,CAAC,IAAI,CAAC9B,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACV,cAAc,CAAC;;EAGnEiB,sBAA6B,GAAmB;IAC/C,OAAO,IAAI,CAACjD,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAACuC,mBAAmB;;EAG5DC,4BAAmC,GAAmB;IACrD,OAAO,IAAI,CAACnD,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAACyC,yBAAyB;;EAGlEC,eAAsB,GAAmB;IACxC,OAAO,IAAI,CAACrD,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAAC2C,YAAY;;EAGrDjE,qBAA4B,GAAmB;IAC9C,OAAOA,qBAAqB,CAAC,IAAI,CAACW,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAAC;;EAG/DvB,8BAAqC,GAAmB;IACvD,OAAOA,8BAA8B,CAAC,IAAI,CAACY,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAAC;;EA9LxE4C,YAAmBvD,KAAmB,EAAEgB,QAAyB,EAAE;IAClE,IAAI,CAAChB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgB,QAAQ,GAAGA,QAAQ", "names": ["invariant", "getDifferenceFromInitialOffset", "getSourceClientOffset", "areDirty", "matchesType", "DragDropMonitorImpl", "subscribeToStateChange", "listener", "options", "handlerIds", "Array", "isArray", "prevStateId", "store", "getState", "stateId", "handleChange", "state", "currentStateId", "canSkipListener", "dirtyHandlerIds", "subscribe", "subscribeToOffsetChange", "previousState", "dragOffset", "nextState", "canDragSource", "sourceId", "source", "registry", "getSource", "isDragging", "canDrag", "canDropOnTarget", "targetId", "target", "get<PERSON><PERSON><PERSON>", "didDrop", "targetType", "getTargetType", "draggedItemType", "getItemType", "canDrop", "Boolean", "isDraggingSource", "isSourcePublic", "sourceType", "getSourceType", "isOverTarget", "shallow", "targetIds", "getTargetIds", "length", "index", "indexOf", "dragOperation", "itemType", "getItem", "item", "getSourceId", "getDropResult", "dropResult", "getInitialClientOffset", "initialClientOffset", "getInitialSourceClientOffset", "initialSourceClientOffset", "getClientOffset", "clientOffset", "constructor"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\classes\\DragDropMonitorImpl.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { Store } from 'redux'\n\nimport type {\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport {\n\tgetDifferenceFromInitialOffset,\n\tgetSourceClientOffset,\n} from '../utils/coords.js'\nimport { areDirty } from '../utils/dirtiness.js'\nimport { matchesType } from '../utils/matchesType.js'\n\nexport class DragDropMonitorImpl implements DragDropMonitor {\n\tprivate store: Store<State>\n\tpublic readonly registry: HandlerRegistry\n\n\tpublic constructor(store: Store<State>, registry: HandlerRegistry) {\n\t\tthis.store = store\n\t\tthis.registry = registry\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions: { handlerIds?: string[] } = {},\n\t): Unsubscribe {\n\t\tconst { handlerIds } = options\n\t\tinvariant(typeof listener === 'function', 'listener must be a function.')\n\t\tinvariant(\n\t\t\ttypeof handlerIds === 'undefined' || Array.isArray(handlerIds),\n\t\t\t'handlerIds, when specified, must be an array of strings.',\n\t\t)\n\n\t\tlet prevStateId = this.store.getState().stateId\n\t\tconst handleChange = () => {\n\t\t\tconst state = this.store.getState()\n\t\t\tconst currentStateId = state.stateId\n\t\t\ttry {\n\t\t\t\tconst canSkipListener =\n\t\t\t\t\tcurrentStateId === prevStateId ||\n\t\t\t\t\t(currentStateId === prevStateId + 1 &&\n\t\t\t\t\t\t!areDirty(state.dirtyHandlerIds, handlerIds))\n\n\t\t\t\tif (!canSkipListener) {\n\t\t\t\t\tlistener()\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tprevStateId = currentStateId\n\t\t\t}\n\t\t}\n\n\t\treturn this.store.subscribe(handleChange)\n\t}\n\n\tpublic subscribeToOffsetChange(listener: Listener): Unsubscribe {\n\t\tinvariant(typeof listener === 'function', 'listener must be a function.')\n\n\t\tlet previousState = this.store.getState().dragOffset\n\t\tconst handleChange = () => {\n\t\t\tconst nextState = this.store.getState().dragOffset\n\t\t\tif (nextState === previousState) {\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tpreviousState = nextState\n\t\t\tlistener()\n\t\t}\n\n\t\treturn this.store.subscribe(handleChange)\n\t}\n\n\tpublic canDragSource(sourceId: string | undefined): boolean {\n\t\tif (!sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tconst source = this.registry.getSource(sourceId)\n\t\tinvariant(source, `Expected to find a valid source. sourceId=${sourceId}`)\n\n\t\tif (this.isDragging()) {\n\t\t\treturn false\n\t\t}\n\n\t\treturn source.canDrag(this, sourceId)\n\t}\n\n\tpublic canDropOnTarget(targetId: string | undefined): boolean {\n\t\t// undefined on initial render\n\t\tif (!targetId) {\n\t\t\treturn false\n\t\t}\n\t\tconst target = this.registry.getTarget(targetId)\n\t\tinvariant(target, `Expected to find a valid target. targetId=${targetId}`)\n\n\t\tif (!this.isDragging() || this.didDrop()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetType = this.registry.getTargetType(targetId)\n\t\tconst draggedItemType = this.getItemType()\n\t\treturn (\n\t\t\tmatchesType(targetType, draggedItemType) && target.canDrop(this, targetId)\n\t\t)\n\t}\n\n\tpublic isDragging(): boolean {\n\t\treturn Boolean(this.getItemType())\n\t}\n\n\tpublic isDraggingSource(sourceId: string | undefined): boolean {\n\t\t// undefined on initial render\n\t\tif (!sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tconst source = this.registry.getSource(sourceId, true)\n\t\tinvariant(source, `Expected to find a valid source. sourceId=${sourceId}`)\n\n\t\tif (!this.isDragging() || !this.isSourcePublic()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst sourceType = this.registry.getSourceType(sourceId)\n\t\tconst draggedItemType = this.getItemType()\n\t\tif (sourceType !== draggedItemType) {\n\t\t\treturn false\n\t\t}\n\n\t\treturn source.isDragging(this, sourceId)\n\t}\n\n\tpublic isOverTarget(\n\t\ttargetId: string | undefined,\n\t\toptions = { shallow: false },\n\t): boolean {\n\t\t// undefined on initial render\n\t\tif (!targetId) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst { shallow } = options\n\t\tif (!this.isDragging()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetType = this.registry.getTargetType(targetId)\n\t\tconst draggedItemType = this.getItemType()\n\t\tif (draggedItemType && !matchesType(targetType, draggedItemType)) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetIds = this.getTargetIds()\n\t\tif (!targetIds.length) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst index = targetIds.indexOf(targetId)\n\t\tif (shallow) {\n\t\t\treturn index === targetIds.length - 1\n\t\t} else {\n\t\t\treturn index > -1\n\t\t}\n\t}\n\n\tpublic getItemType(): Identifier {\n\t\treturn this.store.getState().dragOperation.itemType as Identifier\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.store.getState().dragOperation.item\n\t}\n\n\tpublic getSourceId(): string | null {\n\t\treturn this.store.getState().dragOperation.sourceId\n\t}\n\n\tpublic getTargetIds(): string[] {\n\t\treturn this.store.getState().dragOperation.targetIds\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.store.getState().dragOperation.dropResult\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.store.getState().dragOperation.didDrop\n\t}\n\n\tpublic isSourcePublic(): boolean {\n\t\treturn Boolean(this.store.getState().dragOperation.isSourcePublic)\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.initialClientOffset\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.initialSourceClientOffset\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.clientOffset\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn getSourceClientOffset(this.store.getState().dragOffset)\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn getDifferenceFromInitialOffset(this.store.getState().dragOffset)\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}