{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n  return target;\n}\nimport { invariant } from '@react-dnd/invariant';\nimport { isObject } from '../../utils/js_utils.js';\nimport { DROP } from './types.js';\nexport function createDrop(manager) {\n  return function drop() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const monitor = manager.getMonitor();\n    const registry = manager.getRegistry();\n    verifyInvariants(monitor);\n    const targetIds = getDroppableTargets(monitor);\n    // Multiple actions are dispatched here, which is why this doesn't return an action\n    targetIds.forEach((targetId, index) => {\n      const dropResult = determineDropResult(targetId, index, registry, monitor);\n      const action = {\n        type: DROP,\n        payload: {\n          dropResult: _objectSpread({}, options, dropResult)\n        }\n      };\n      manager.dispatch(action);\n    });\n  };\n}\nfunction verifyInvariants(monitor) {\n  invariant(monitor.isDragging(), 'Cannot call drop while not dragging.');\n  invariant(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n  const target = registry.getTarget(targetId);\n  let dropResult = target ? target.drop(monitor, targetId) : undefined;\n  verifyDropResultType(dropResult);\n  if (typeof dropResult === 'undefined') {\n    dropResult = index === 0 ? {} : monitor.getDropResult();\n  }\n  return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n  invariant(typeof dropResult === 'undefined' || isObject(dropResult), 'Drop result must either be an object or undefined.');\n}\nfunction getDroppableTargets(monitor) {\n  const targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n  targetIds.reverse();\n  return targetIds;\n}", "map": {"version": 3, "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAUhD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,QAAQ,YAAY;AAEjC,OAAO,SAASC,UAAU,CAACC,OAAwB,EAAE;EACpD,OAAO,SAASC,IAAI,GAAqB;IAAA,IAApBC,OAAO,uEAAG,EAAE;IAChC,MAAMC,OAAO,GAAGH,OAAO,CAACI,UAAU,EAAE;IACpC,MAAMC,QAAQ,GAAGL,OAAO,CAACM,WAAW,EAAE;IACtCC,gBAAgB,CAACJ,OAAO,CAAC;IACzB,MAAMK,SAAS,GAAGC,mBAAmB,CAACN,OAAO,CAAC;IAE9C;IACAK,SAAS,CAACE,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MACtC,MAAMC,UAAU,GAAGC,mBAAmB,CAACH,QAAQ,EAAEC,KAAK,EAAEP,QAAQ,EAAEF,OAAO,CAAC;MAC1E,MAAMY,MAAM,GAAwB;QACnCC,IAAI,EAAElB,IAAI;QACVmB,OAAO,EAAE;UACRJ,UAAU,EAAEK,kBACRhB,OAAO,EACPW,UAAU;;OAGf;MACDb,OAAO,CAACmB,QAAQ,CAACJ,MAAM,CAAC;KACxB,CAAC;GACF;;AAGF,SAASR,gBAAgB,CAACJ,OAAwB,EAAE;EACnDP,SAAS,CAACO,OAAO,CAACiB,UAAU,EAAE,EAAE,sCAAsC,CAAC;EACvExB,SAAS,CACR,CAACO,OAAO,CAACkB,OAAO,EAAE,EAClB,mDAAmD,CACnD;;AAGF,SAASP,mBAAmB,CAC3BH,QAAoB,EACpBC,KAAa,EACbP,QAAyB,EACzBF,OAAwB,EACvB;EACD,MAAMmB,MAAM,GAAGjB,QAAQ,CAACkB,SAAS,CAACZ,QAAQ,CAAC;EAC3C,IAAIE,UAAU,GAAGS,MAAM,GAAGA,MAAM,CAACrB,IAAI,CAACE,OAAO,EAAEQ,QAAQ,CAAC,GAAGa,SAAS;EACpEC,oBAAoB,CAACZ,UAAU,CAAC;EAChC,IAAI,OAAOA,UAAU,KAAK,WAAW,EAAE;IACtCA,UAAU,GAAGD,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGT,OAAO,CAACuB,aAAa,EAAE;;EAExD,OAAOb,UAAU;;AAGlB,SAASY,oBAAoB,CAACZ,UAAe,EAAE;EAC9CjB,SAAS,CACR,OAAOiB,UAAU,KAAK,WAAW,IAAIhB,QAAQ,CAACgB,UAAU,CAAC,EACzD,oDAAoD,CACpD;;AAGF,SAASJ,mBAAmB,CAACN,OAAwB,EAAE;EACtD,MAAMK,SAAS,GAAGL,OAAO,CACvBwB,YAAY,EAAE,CACdC,MAAM,CAACzB,OAAO,CAAC0B,eAAe,EAAE1B,OAAO,CAAC;EAC1CK,SAAS,CAACsB,OAAO,EAAE;EACnB,OAAOtB,SAAS", "names": ["invariant", "isObject", "DROP", "createDrop", "manager", "drop", "options", "monitor", "getMonitor", "registry", "getRegistry", "verifyInvariants", "targetIds", "getDroppableTargets", "for<PERSON>ach", "targetId", "index", "dropResult", "determineDropResult", "action", "type", "payload", "_objectSpread", "dispatch", "isDragging", "didDrop", "target", "get<PERSON><PERSON><PERSON>", "undefined", "verifyDropResultType", "getDropResult", "getTargetIds", "filter", "canDropOnTarget", "reverse"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\actions\\dragDrop\\drop.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tDragDropManager,\n\tDragDropMonitor,\n\tDropPayload,\n\tHandlerRegistry,\n\tIdentifier,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { DROP } from './types.js'\n\nexport function createDrop(manager: DragDropManager) {\n\treturn function drop(options = {}): void {\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tverifyInvariants(monitor)\n\t\tconst targetIds = getDroppableTargets(monitor)\n\n\t\t// Multiple actions are dispatched here, which is why this doesn't return an action\n\t\ttargetIds.forEach((targetId, index) => {\n\t\t\tconst dropResult = determineDropResult(targetId, index, registry, monitor)\n\t\t\tconst action: Action<DropPayload> = {\n\t\t\t\ttype: DROP,\n\t\t\t\tpayload: {\n\t\t\t\t\tdropResult: {\n\t\t\t\t\t\t...options,\n\t\t\t\t\t\t...dropResult,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t}\n\t\t\tmanager.dispatch(action)\n\t\t})\n\t}\n}\n\nfunction verifyInvariants(monitor: DragDropMonitor) {\n\tinvariant(monitor.isDragging(), 'Cannot call drop while not dragging.')\n\tinvariant(\n\t\t!monitor.didDrop(),\n\t\t'Cannot call drop twice during one drag operation.',\n\t)\n}\n\nfunction determineDropResult(\n\ttargetId: Identifier,\n\tindex: number,\n\tregistry: HandlerRegistry,\n\tmonitor: DragDropMonitor,\n) {\n\tconst target = registry.getTarget(targetId)\n\tlet dropResult = target ? target.drop(monitor, targetId) : undefined\n\tverifyDropResultType(dropResult)\n\tif (typeof dropResult === 'undefined') {\n\t\tdropResult = index === 0 ? {} : monitor.getDropResult()\n\t}\n\treturn dropResult\n}\n\nfunction verifyDropResultType(dropResult: any) {\n\tinvariant(\n\t\ttypeof dropResult === 'undefined' || isObject(dropResult),\n\t\t'Drop result must either be an object or undefined.',\n\t)\n}\n\nfunction getDroppableTargets(monitor: DragDropMonitor) {\n\tconst targetIds = monitor\n\t\t.getTargetIds()\n\t\t.filter(monitor.canDropOnTarget, monitor)\n\ttargetIds.reverse()\n\treturn targetIds\n}\n"]}, "metadata": {}, "sourceType": "module"}