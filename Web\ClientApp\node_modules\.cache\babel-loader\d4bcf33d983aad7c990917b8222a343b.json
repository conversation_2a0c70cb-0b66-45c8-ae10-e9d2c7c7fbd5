{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    },\n    moveItem(state, action) {\n      const {\n        movingItem,\n        existingItem\n      } = action.payload;\n      console.log(\"moving item\", existingItem, movingItem);\n      if (existingItem.stickingSortOrder == null) {\n        // Create updated versions of the items instead of mutating payload\n        const updatedExistingItem = {\n          ...existingItem,\n          stickingSortOrder: 1\n        };\n        const updatedMovingItem = {\n          ...movingItem,\n          stickingSortOrder: 0\n        };\n\n        //move the rest up by 1\n        state.plants = state.plants.map(p => {\n          if (p._id === existingItem._id) {\n            return updatedExistingItem;\n          } else if (p._id === movingItem._id) {\n            return updatedMovingItem;\n          } else if (p.stickingSortOrder != null) {\n            return {\n              ...p,\n              stickingSortOrder: p.stickingSortOrder + 1\n            };\n          }\n          return p;\n        });\n      } else {\n        // set the moved item to the existing item's order\n        const updatedMovingItem = {\n          ...movingItem,\n          stickingSortOrder: existingItem.stickingSortOrder\n        };\n\n        // move the existing item and all items with a higher order up by 1\n        state.plants = state.plants.map(p => {\n          var _existingItem$stickin;\n          if (p._id === existingItem._id) {\n            return updatedMovingItem;\n          } else if (p._id === movingItem._id) {\n            return existingItem;\n          } else if (p.stickingSortOrder != null && p.stickingSortOrder > ((_existingItem$stickin = existingItem.stickingSortOrder) !== null && _existingItem$stickin !== void 0 ? _existingItem$stickin : 0)) {\n            return {\n              ...p,\n              stickingSortOrder: p.stickingSortOrder + 1\n            };\n          }\n          return p;\n        });\n      }\n    }\n  }\n});\nexport const {\n  setPlants,\n  moveItem\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nfunction sortByStickingSortOrder(a, b) {\n  // Handle null values - place them last\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1; // b comes before a (non-null before null)\n\n  // Both are non-null, compare normally\n  return a.stickingSortOrder - b.stickingSortOrder;\n}\nfunction sortPlant(a, b) {\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "sortBy", "sortSizeName", "initialState", "plants", "plantsSlice", "name", "reducers", "setPlants", "state", "action", "payload", "moveItem", "movingItem", "existingItem", "console", "log", "stickingSortOrder", "updatedExistingItem", "updatedMovingItem", "map", "p", "_id", "actions", "selectPlants", "sort", "sortPlant", "reducer", "sortByCrop", "sortByStickingSortOrder", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    },\r\n    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {\r\n      const { movingItem, existingItem } = action.payload;\r\n\r\n      console.log(\"moving item\", existingItem, movingItem);\r\n\r\n      if (existingItem.stickingSortOrder == null) {\r\n        // Create updated versions of the items instead of mutating payload\r\n        const updatedExistingItem = { ...existingItem, stickingSortOrder: 1 };\r\n        const updatedMovingItem = { ...movingItem, stickingSortOrder: 0 };\r\n\r\n        //move the rest up by 1\r\n        state.plants = state.plants.map(p => {\r\n          if (p._id === existingItem._id) {\r\n            return updatedExistingItem;\r\n          } else if (p._id === movingItem._id) {\r\n            return updatedMovingItem;\r\n          } else if (p.stickingSortOrder != null) {\r\n            return { ...p, stickingSortOrder: p.stickingSortOrder + 1 };\r\n          }\r\n          return p;\r\n        });\r\n      } else {\r\n        // set the moved item to the existing item's order\r\n        const updatedMovingItem = { ...movingItem, stickingSortOrder: existingItem.stickingSortOrder };\r\n\r\n        // move the existing item and all items with a higher order up by 1\r\n        state.plants = state.plants.map(p => {\r\n          if (p._id === existingItem._id) {\r\n            return updatedMovingItem;\r\n          } else if (p._id === movingItem._id) {\r\n            return existingItem;\r\n          } else if (p.stickingSortOrder != null && p.stickingSortOrder > (existingItem.stickingSortOrder ?? 0)) {\r\n            return { ...p, stickingSortOrder: p.stickingSortOrder + 1 };\r\n          }\r\n          return p;\r\n        });\r\n      }\r\n    }\r\n  }\r\n});\r\n\r\nexport const { setPlants, moveItem } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\n\r\nfunction sortByStickingSortOrder(a: Plant, b: Plant) {\r\n  // Handle null values - place them last\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\r\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1;  // b comes before a (non-null before null)\r\n\r\n  // Both are non-null, compare normally\r\n  return a.stickingSortOrder! - b.stickingSortOrder!;\r\n}\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAMjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGL,WAAW,CAAC;EACrCM,IAAI,EAAE,QAAQ;EACdH,YAAY;EACZI,QAAQ,EAAE;IACRC,SAAS,CAACC,KAAK,EAAEC,MAA8B,EAAE;MAC/CD,KAAK,CAACL,MAAM,GAAGM,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDC,QAAQ,CAACH,KAAK,EAAEC,MAAiE,EAAE;MACjF,MAAM;QAAEG,UAAU;QAAEC;MAAa,CAAC,GAAGJ,MAAM,CAACC,OAAO;MAEnDI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,YAAY,EAAED,UAAU,CAAC;MAEpD,IAAIC,YAAY,CAACG,iBAAiB,IAAI,IAAI,EAAE;QAC1C;QACA,MAAMC,mBAAmB,GAAG;UAAE,GAAGJ,YAAY;UAAEG,iBAAiB,EAAE;QAAE,CAAC;QACrE,MAAME,iBAAiB,GAAG;UAAE,GAAGN,UAAU;UAAEI,iBAAiB,EAAE;QAAE,CAAC;;QAEjE;QACAR,KAAK,CAACL,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACgB,GAAG,CAACC,CAAC,IAAI;UACnC,IAAIA,CAAC,CAACC,GAAG,KAAKR,YAAY,CAACQ,GAAG,EAAE;YAC9B,OAAOJ,mBAAmB;UAC5B,CAAC,MAAM,IAAIG,CAAC,CAACC,GAAG,KAAKT,UAAU,CAACS,GAAG,EAAE;YACnC,OAAOH,iBAAiB;UAC1B,CAAC,MAAM,IAAIE,CAAC,CAACJ,iBAAiB,IAAI,IAAI,EAAE;YACtC,OAAO;cAAE,GAAGI,CAAC;cAAEJ,iBAAiB,EAAEI,CAAC,CAACJ,iBAAiB,GAAG;YAAE,CAAC;UAC7D;UACA,OAAOI,CAAC;QACV,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMF,iBAAiB,GAAG;UAAE,GAAGN,UAAU;UAAEI,iBAAiB,EAAEH,YAAY,CAACG;QAAkB,CAAC;;QAE9F;QACAR,KAAK,CAACL,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACgB,GAAG,CAACC,CAAC,IAAI;UAAA;UACnC,IAAIA,CAAC,CAACC,GAAG,KAAKR,YAAY,CAACQ,GAAG,EAAE;YAC9B,OAAOH,iBAAiB;UAC1B,CAAC,MAAM,IAAIE,CAAC,CAACC,GAAG,KAAKT,UAAU,CAACS,GAAG,EAAE;YACnC,OAAOR,YAAY;UACrB,CAAC,MAAM,IAAIO,CAAC,CAACJ,iBAAiB,IAAI,IAAI,IAAII,CAAC,CAACJ,iBAAiB,6BAAIH,YAAY,CAACG,iBAAiB,yEAAI,CAAC,CAAC,EAAE;YACrG,OAAO;cAAE,GAAGI,CAAC;cAAEJ,iBAAiB,EAAEI,CAAC,CAACJ,iBAAiB,GAAG;YAAE,CAAC;UAC7D;UACA,OAAOI,CAAC;QACV,CAAC,CAAC;MACJ;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEb,SAAS;EAAEI;AAAS,CAAC,GAAGP,WAAW,CAACkB,OAAO;AAE1D,OAAO,MAAMC,YAAY,GAAIf,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAACA,MAAM,CAACgB,GAAG,CAACC,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,SAAS,CAAC;AAExG,eAAerB,WAAW,CAACsB,OAAO;AAElC,MAAMC,UAAU,GAAG3B,MAAM,CAAC,MAAM,CAAC;AAEjC,SAAS4B,uBAAuB,CAACC,CAAQ,EAAEC,CAAQ,EAAE;EACnD;EACA,IAAID,CAAC,CAACb,iBAAiB,IAAI,IAAI,IAAIc,CAAC,CAACd,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC;EACxE,IAAIa,CAAC,CAACb,iBAAiB,IAAI,IAAI,IAAIc,CAAC,CAACd,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3E,IAAIa,CAAC,CAACb,iBAAiB,IAAI,IAAI,IAAIc,CAAC,CAACd,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAE;;EAE3E;EACA,OAAOa,CAAC,CAACb,iBAAiB,GAAIc,CAAC,CAACd,iBAAkB;AACpD;AAEA,SAASS,SAAS,CAACI,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,uBAAuB,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIH,UAAU,CAACE,CAAC,EAAEC,CAAC,CAAC,IAAI7B,YAAY,CAAC4B,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AAC1F"}, "metadata": {}, "sourceType": "module"}