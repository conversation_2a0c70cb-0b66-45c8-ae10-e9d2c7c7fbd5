import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { routes } from "app/routes";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { Button } from "reactstrap";
import { selectColours } from "./colours-slice";
import { useAuth } from "features/auth/use-auth";

export function List() { 
  const colours = useSelector(selectColours),
    { isInRole } = useAuth(),
    canCreate = isInRole('create:colours');

  return (
    <div className="container d-grid gap-2">
      <div className="row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow">
        <h1 className="col pt-2">
          <FontAwesomeIcon icon={['fat', 'palette']} />
          &nbsp;
          Colours List
        </h1>
        {/*canCreate &&
          <div className="col-auto pt-3">
            <Button tag={Link} to={routes.colours.routes.new()} outline color="success">
              <FontAwesomeIcon icon={['fat', 'plus']} />
              &nbsp;
              New Colour
            </Button>
          </div>
        */}
        <div className="col-auto pt-3">
          <Button tag={Link} to={routes.colours.routes.new()} outline color="success">
            <FontAwesomeIcon icon={['fat', 'plus']} />
            &nbsp;
            New Colour
          </Button>
        </div>
      </div>
      <table className="table">
        <thead>
          <tr className="sticky-top bg-white" style={{top: '140px'}}>
            <th>&nbsp;</th>
            <th>Name</th>
            <th>Hex</th>
          </tr>
        </thead>
        <tbody>
          {colours.map(colour =>
            <tr key={colour._id}>
              <td>
                <Link to={routes.colours.routes.detail.to(colour._id)}>
                  <FontAwesomeIcon icon={['fat', 'edit']} />
                </Link>
              </td>
              <td>{colour.name}</td>
              <td>{colour.hex}</td>
            </tr>
            )}
        </tbody>
      </table>      
    </div>
  )
}

export default List;