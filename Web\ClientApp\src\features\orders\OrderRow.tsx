import { useState } from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Order } from 'api/models/orders';
import { routes } from 'app/routes';
import { formatNumber, formatDate } from 'utils/format';
import { Button, Collapse } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { selectZones } from './orders-slice';

interface OrderRowProps {
  order: Order;
  showTightTables?: boolean;
  showSpacedTables?: boolean;
  showPinchDate?: boolean;
}

export function OrderRow(props: OrderRowProps) {
  const { order, showTightTables, showSpacedTables, showPinchDate } = props,
    zones = useSelector(selectZones),
    [showVarieties, setShowVarieties] = useState(false),
    isOffsite =
      zones.find((z) => z._id === order.fullSpaceZone?._id)?.isOffsite || false;

  const handleShowVarietiesClick = () => {
    setShowVarieties(!showVarieties);
  };

  return (
    <tr key={order._id} style={{ backgroundColor: order.plant.colour || '' }}>
      <td className="bg-transparent">
        <Link to={routes.orders.routes.detail.to(order._id)}>
          {order.orderNumber}
        </Link>
      </td>
      <td className="mx-0 text-end bg-transparent">
        {!!order.varieties?.length && (
          <Button outline size="sm" onClick={handleShowVarietiesClick}>
            {showVarieties && <FontAwesomeIcon icon={['fat', 'minus']} />}
            {!showVarieties && <FontAwesomeIcon icon={['fat', 'plus']} />}
          </Button>
        )}
      </td>
      <td className="bg-transparent">
        {order.plant.size} {order.plant.crop}
        {!!order.varieties?.length && (
          <Collapse
            tag={'ul'}
            isOpen={showVarieties}
            className="list-unstyled ms-3 fst-italic">
            {order.varieties.map((variety) => (
              <li key={variety.name}>{variety.name}</li>
            ))}
          </Collapse>
        )}
        {!!order.notes && (
          <p className="fw-bold fst-italic mb-0">{order.notes}</p>
        )}
      </td>
      <td className="text-center text-nowrap bg-transparent">
        {formatNumber(order.pots)}
        {order.cases && (
          <>
            <span>&nbsp;/&nbsp;</span>
            {formatNumber(order.cases)}
          </>
        )}
        {!!order.varieties?.length && (
          <Collapse
            tag={'ul'}
            isOpen={showVarieties}
            className="list-unstyled ms-3 fst-italic">
            {order.varieties.map((variety) => (
              <li key={variety.name} className="text-nowrap">
                {formatNumber(variety.pots)}&nbsp;/&nbsp;
                {formatNumber(variety.cases)}
              </li>
            ))}
          </Collapse>
        )}
      </td>
      {showTightTables && (
        <td className="text-center bg-transparent">
          {formatNumber(order.tableCountTight)}
        </td>
      )}
      {showSpacedTables && (
        <td className="text-center bg-transparent">
          {isOffsite ? '-' : formatNumber(order.tableCountSpaced)}
        </td>
      )}
      <td className="text-center bg-transparent">{order.stickZone.name}</td>
      <td className="text-center bg-transparent">
        {order.fullSpaceZone?.name}
      </td>
      <td className="text-center text-nowrap bg-transparent">
        {formatDate(order.stickDate)}
      </td>
      <td className="text-center text-nowrap bg-transparent">
        {formatDate(order.fullSpaceDate)}
      </td>
      {showPinchDate && (
        <td className="text-center text-nowrap bg-transparent">
          {formatDate(order.pinchDate)}
        </td>
      )}
      <td className="text-center text-nowrap bg-transparent">
        {isOffsite ? '-' : formatDate(order.flowerDate)}
      </td>
    </tr>
  );
}
