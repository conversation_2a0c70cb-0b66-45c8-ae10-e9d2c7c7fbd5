{"ast": null, "code": "export const FILE = '__NATIVE_FILE__';\nexport const URL = '__NATIVE_URL__';\nexport const TEXT = '__NATIVE_TEXT__';\nexport const HTML = '__NATIVE_HTML__';", "map": {"version": 3, "mappings": "AAAA,OAAO,MAAMA,IAAI,GAAG,iBAAiB;AACrC,OAAO,MAAMC,GAAG,GAAG,gBAAgB;AACnC,OAAO,MAAMC,IAAI,GAAG,iBAAiB;AACrC,OAAO,MAAMC,IAAI,GAAG,iBAAiB", "names": ["FILE", "URL", "TEXT", "HTML"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd-html5-backend\\src\\NativeTypes.ts"], "sourcesContent": ["export const FILE = '__NATIVE_FILE__'\nexport const URL = '__NATIVE_URL__'\nexport const TEXT = '__NATIVE_TEXT__'\nexport const HTML = '__NATIVE_HTML__'\n"]}, "metadata": {}, "sourceType": "module"}