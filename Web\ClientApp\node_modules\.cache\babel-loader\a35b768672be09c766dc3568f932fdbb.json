{"ast": null, "code": "import { PUBLISH_DRAG_SOURCE } from './types.js';\nexport function createPublishDragSource(manager) {\n  return function publishDragSource() {\n    const monitor = manager.getMonitor();\n    if (monitor.isDragging()) {\n      return {\n        type: PUBLISH_DRAG_SOURCE\n      };\n    }\n    return;\n  };\n}", "map": {"version": 3, "mappings": "AACA,SAASA,mBAAmB,QAAQ,YAAY;AAEhD,OAAO,SAASC,uBAAuB,CAACC,OAAwB,EAAE;EACjE,OAAO,SAASC,iBAAiB,GAA+B;IAC/D,MAAMC,OAAO,GAAGF,OAAO,CAACG,UAAU,EAAE;IACpC,IAAID,OAAO,CAACE,UAAU,EAAE,EAAE;MACzB,OAAO;QAAEC,IAAI,EAAEP;OAAqB;;IAErC;GACA", "names": ["PUBLISH_DRAG_SOURCE", "createPublishDragSource", "manager", "publishDragSource", "monitor", "getMonitor", "isDragging", "type"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\actions\\dragDrop\\publishDragSource.ts"], "sourcesContent": ["import type { Drag<PERSON>rop<PERSON>ana<PERSON>, SentinelAction } from '../../interfaces.js'\nimport { PUBLISH_DRAG_SOURCE } from './types.js'\n\nexport function createPublishDragSource(manager: DragDropManager) {\n\treturn function publishDragSource(): SentinelAction | undefined {\n\t\tconst monitor = manager.getMonitor()\n\t\tif (monitor.isDragging()) {\n\t\t\treturn { type: PUBLISH_DRAG_SOURCE }\n\t\t}\n\t\treturn\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}