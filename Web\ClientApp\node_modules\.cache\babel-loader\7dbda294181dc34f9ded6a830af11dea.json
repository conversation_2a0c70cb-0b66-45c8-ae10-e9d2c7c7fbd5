{"ast": null, "code": "import { registerSource } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js';\nimport { useDragSource } from './useDragSource.js';\nimport { useDragType } from './useDragType.js';\nexport function useRegisteredDragSource(spec, monitor, connector) {\n  const manager = useDragDropManager();\n  const handler = useDragSource(spec, monitor, connector);\n  const itemType = useDragType(spec);\n  useIsomorphicLayoutEffect(function registerDragSource() {\n    if (itemType != null) {\n      const [handlerId, unregister] = registerSource(itemType, handler, manager);\n      monitor.receiveHandlerId(handlerId);\n      connector.receiveHandlerId(handlerId);\n      return unregister;\n    }\n    return;\n  }, [manager, monitor, connector, handler, itemType]);\n}", "map": {"version": 3, "mappings": "AACA,SAASA,cAAc,QAAQ,0BAA0B;AAGzD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,OAAO,SAASC,uBAAuB,CACtCC,IAAiC,EACjCC,OAAgC,EAChCC,SAA0B,EACnB;EACP,MAAMC,OAAO,GAAGR,kBAAkB,EAAE;EACpC,MAAMS,OAAO,GAAGP,aAAa,CAACG,IAAI,EAAEC,OAAO,EAAEC,SAAS,CAAC;EACvD,MAAMG,QAAQ,GAAGP,WAAW,CAACE,IAAI,CAAC;EAElCJ,yBAAyB,CACxB,SAASU,kBAAkB,GAAG;IAC7B,IAAID,QAAQ,IAAI,IAAI,EAAE;MACrB,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,GAAGd,cAAc,CAC7CW,QAAQ,EACRD,OAAO,EACPD,OAAO,CACP;MACDF,OAAO,CAACQ,gBAAgB,CAACF,SAAS,CAAC;MACnCL,SAAS,CAACO,gBAAgB,CAACF,SAAS,CAAC;MACrC,OAAOC,UAAU;;IAElB;GACA,EACD,CAACL,OAAO,EAAEF,OAAO,EAAEC,SAAS,EAAEE,OAAO,EAAEC,QAAQ,CAAC,CAChD", "names": ["registerSource", "useDragDropManager", "useIsomorphicLayoutEffect", "useDragSource", "useDragType", "useRegisteredDragSource", "spec", "monitor", "connector", "manager", "handler", "itemType", "registerDragSource", "handlerId", "unregister", "receiveHandlerId"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrag\\useRegisteredDragSource.ts"], "sourcesContent": ["import type { SourceConnector } from '../../internals/index.js'\nimport { registerSource } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragSourceHookSpec } from '../types.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\nimport { useDragSource } from './useDragSource.js'\nimport { useDragType } from './useDragType.js'\n\nexport function useRegisteredDragSource<O, R, P>(\n\tspec: DragSourceHookSpec<O, R, P>,\n\tmonitor: DragSourceMonitor<O, R>,\n\tconnector: SourceConnector,\n): void {\n\tconst manager = useDragDropManager()\n\tconst handler = useDragSource(spec, monitor, connector)\n\tconst itemType = useDragType(spec)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction registerDragSource() {\n\t\t\tif (itemType != null) {\n\t\t\t\tconst [handlerId, unregister] = registerSource(\n\t\t\t\t\titemType,\n\t\t\t\t\thandler,\n\t\t\t\t\tmanager,\n\t\t\t\t)\n\t\t\t\tmonitor.receiveHandlerId(handlerId)\n\t\t\t\tconnector.receiveHandlerId(handlerId)\n\t\t\t\treturn unregister\n\t\t\t}\n\t\t\treturn\n\t\t},\n\t\t[manager, monitor, connector, handler, itemType],\n\t)\n}\n"]}, "metadata": {}, "sourceType": "module"}