import React, { Fragment, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Button, Input, InputGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import {
  selectEndWeek,
  selectOrders,
  selectPlants,
  selectPlantName,
  setPlantName,
  selectStartWeek,
  setStartWeek as setSliceStartWeek,
  setEndWeek as setSliceEndWeek,
  downloadOrderSummary,
  selectDownloading,
  selectStartDate,
  selectEndDate,
} from './orders-slice';
import { OrderRow } from './OrderRow';
import { useAuth } from 'features/auth/use-auth';
import { parseWeekAndYear } from 'utils/format';
import { handleFocus } from 'utils/focus';
import { weekDisplay } from 'utils/weeks';
import { LabourReport } from './LabourReport';

export function List() {
  const dispatch = useDispatch(),
    orders = useSelector(selectOrders),
    downloading = useSelector(selectDownloading),
    startDate = useSelector(selectStartDate),
    endDate = useSelector(selectEndDate),
    plants = useSelector(selectPlants),
    plantName = useSelector(selectPlantName),
    { isInRole } = useAuth(),
    [week1, year1] = useSelector(selectStartWeek).split('/'),
    [week2, year2] = useSelector(selectEndWeek).split('/'),
    [startWeek, setStartWeek] = useState(week1),
    [startYear, setStartYear] = useState(year1),
    [endWeek, setEndWeek] = useState(week2),
    [endYear, setEndYear] = useState(year2),
    [showLabourReport, setShowLabourReport] = useState(false),
    canCreateOrders = isInRole('create:orders');

  const handleStartWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const startWeek = e.target.value,
      weekAndYear = `${startWeek}/${startYear}`,
      startDate = parseWeekAndYear(weekAndYear);

    setStartWeek(startWeek);

    if (startDate) {
      dispatch(setSliceStartWeek(weekAndYear));
    }
  };

  const handleStartYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const startYear = e.target.value,
      weekAndYear = `${startWeek}/${startYear}`,
      startDate = parseWeekAndYear(weekAndYear);

    setStartYear(startYear);

    if (startDate) {
      dispatch(setSliceStartWeek(weekAndYear));
    }
  };

  const handleEndWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const endWeek = e.target.value,
      weekAndYear = `${endWeek}/${endYear}`,
      endDate = parseWeekAndYear(weekAndYear);

    setEndWeek(endWeek);

    if (endDate) {
      dispatch(setSliceEndWeek(weekAndYear));
    }
  };

  const handleEndYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const endYear = e.target.value,
      weekAndYear = `${endWeek}/${endYear}`,
      endDate = parseWeekAndYear(weekAndYear);

    setEndYear(endYear);

    if (endDate) {
      dispatch(setSliceEndWeek(weekAndYear));
    }
  };

  const handlePlantNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const plantName = e.target.value || null;
    dispatch(setPlantName(plantName));
  };

  const handleDownloadClick = () => {
    if (startDate && endDate) {
      dispatch(
        downloadOrderSummary({ from: startDate, to: endDate, plant: plantName })
      );
    }
  };

  const handleLabourReportClick = () => {
    setShowLabourReport(true);
  };

  const handleLabourReportClose = () => {
    setShowLabourReport(false);
  };

  return (
    <div className="container-fluid">
      <div className="bg-white sticky-top-navbar">
        <div className="container mx-auto row mt-2 py-2 border-bottom shadow">
          <div className="col-12 row my-2">
            <h1 className="col">
              <FontAwesomeIcon icon={['fat', 'file-invoice']} />
              &nbsp; Orders
            </h1>
            <div className="col-auto">
              <Button
                tag={Link}
                to={routes.orders.routes.byStickDate.path}
                color="link">
                <FontAwesomeIcon icon={['fat', 'seedling']} />
                &nbsp; By Stick Date
              </Button>
            </div>
            <div className="col-auto">
              <Button
                tag={Link}
                to={routes.orders.routes.bySpaceDate.path}
                color="link">
                <FontAwesomeIcon icon={['fat', 'ruler-horizontal']} />
                &nbsp; By Space Date
              </Button>
            </div>
            <div className="col-auto">
              <Button
                tag={Link}
                to={routes.orders.routes.byPinchDate.path}
                color="link">
                <FontAwesomeIcon icon={['fat', 'hands-asl-interpreting']} />
                &nbsp; By Pinch Date
              </Button>
            </div>
            <div className="col-auto">
              <Button
                tag={Link}
                to={routes.orders.routes.byFlowerDate.path}
                color="link">
                <FontAwesomeIcon icon={['fat', 'flower-daffodil']} />
                &nbsp; By Flower Date
              </Button>
            </div>
          </div>
          <div className="col-12 row">
            <div className="col-auto">
              <label htmlFor="orders-list-from">From</label>
              <InputGroup>
                <Input
                  id="orders-list-from"
                  value={startWeek}
                  onChange={handleStartWeekChange}
                  onFocus={handleFocus}
                  className="max-w-75px text-center"
                />
                <Input
                  id="orders-list-from-year"
                  value={startYear}
                  onChange={handleStartYearChange}
                  onFocus={handleFocus}
                  className="max-w-100px text-center"
                />
              </InputGroup>
            </div>
            <div className="col-auto">
              <label htmlFor="orders-list-to">To</label>
              <InputGroup>
                <Input
                  id="orders-list-to"
                  value={endWeek}
                  onChange={handleEndWeekChange}
                  onFocus={handleFocus}
                  className="max-w-75px text-center"
                />
                <Input
                  id="orders-list-to-year"
                  value={endYear}
                  onChange={handleEndYearChange}
                  onFocus={handleFocus}
                  className="max-w-100px text-center"
                />
              </InputGroup>
            </div>
            <div className="col-auto">
              <label htmlFor="orders-list-plant-name">Plant</label>
              <Input
                id="orders-list-plant-name"
                type="select"
                value={plantName || ''}
                onChange={handlePlantNameChange}>
                <option value="">All Plants</option>
                {plants.map((plant) => (
                  <option key={plant} value={plant}>
                    {plant}
                  </option>
                ))}
              </Input>
            </div>
            <div className="col-auto ms-auto">
              <label className="invisible d-block">Download</label>
              <Button
                onClick={handleDownloadClick}
                outline
                color="info"
                disabled={downloading}>
                {downloading && (
                  <FontAwesomeIcon icon={['fat', 'spinner']} spin />
                )}
                {!downloading && (
                  <FontAwesomeIcon icon={['fat', 'file-excel']} />
                )}
                &nbsp; Download
              </Button>
            </div>
            <div className="col-auto">
              <label className="invisible d-block">Labour Report</label>
              <Button
                onClick={handleLabourReportClick}
                outline
                color="secondary">
                <FontAwesomeIcon icon={['fat', 'chart-line']} />
                &nbsp; Labour Report
              </Button>
            </div>
            {canCreateOrders && (
              <div className="col-auto">
                <label className="invisible d-block">New Order</label>
                <Button
                  tag={Link}
                  to={routes.orders.routes.new()}
                  outline
                  color="success">
                  <FontAwesomeIcon icon={['fat', 'plus']} />
                  &nbsp; New Order
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
      <table className="table">
        <thead>
          <tr className="sticky-top bg-white" style={{ top: '210px' }}>
            <th>Batch</th>
            <th>&nbsp;</th>
            <th>Plant</th>
            <th className="text-center">Pots / Cases</th>
            <th className="text-center">
              Tables
              <br />
              (Tight)
            </th>
            <th className="text-center">
              Tables
              <br />
              (Spaced)
            </th>
            <th className="text-center">Stick Zone</th>
            <th className="text-center">Space Zone</th>
            <th className="text-center">Stick Date</th>
            <th className="text-center">Space Date</th>
            <th className="text-center">Pinch Date</th>
            <th className="text-center">Flower Date</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order, index) => (
            <Fragment key={order._id}>
              {weekDisplay(order.flowerDate) !==
                weekDisplay(orders[index - 1]?.flowerDate) && (
                <tr className="sticky-top" style={{ top: '275px' }}>
                  <th colSpan={12} className="table-light">
                    Flower {weekDisplay(order.flowerDate)}
                  </th>
                </tr>
              )}
              <OrderRow
                key={order._id}
                order={order}
                showTightTables
                showSpacedTables
                showPinchDate
              />
            </Fragment>
          ))}
        </tbody>
      </table>
      <LabourReport show={showLabourReport} onClose={handleLabourReportClose} />
    </div>
  );
}

export default List;
