{"ast": null, "code": "import equal from 'fast-deep-equal';\nimport { useCallback, useState } from 'react';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js';\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\nexport function useCollector(monitor, collect, onUpdate) {\n  const [collected, setCollected] = useState(() => collect(monitor));\n  const updateCollected = useCallback(() => {\n    const nextValue = collect(monitor);\n    // This needs to be a deep-equality check because some monitor-collected values\n    // include XYCoord objects that may be equivalent, but do not have instance equality.\n    if (!equal(collected, nextValue)) {\n      setCollected(nextValue);\n      if (onUpdate) {\n        onUpdate();\n      }\n    }\n  }, [collected, monitor, onUpdate]);\n  // update the collected properties after react renders.\n  // Note that the \"Dustbin Stress Test\" fails if this is not\n  // done when the component updates\n  useIsomorphicLayoutEffect(updateCollected);\n  return [collected, updateCollected];\n}", "map": {"version": 3, "mappings": "AAAA,OAAOA,KAAK,MAAM,iBAAiB;AACnC,SAASC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAE7C,SAASC,yBAAyB,QAAQ,gCAAgC;AAE1E;;;;;;AAMA,OAAO,SAASC,YAAY,CAC3BC,OAAU,EACVC,OAA0B,EAC1BC,QAAqB,EACH;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,MAAMI,OAAO,CAACD,OAAO,CAAC,CAAC;EAElE,MAAMK,eAAe,GAAGT,WAAW,CAAC,MAAM;IACzC,MAAMU,SAAS,GAAGL,OAAO,CAACD,OAAO,CAAC;IAClC;IACA;IACA,IAAI,CAACL,KAAK,CAACQ,SAAS,EAAEG,SAAS,CAAC,EAAE;MACjCF,YAAY,CAACE,SAAS,CAAC;MACvB,IAAIJ,QAAQ,EAAE;QACbA,QAAQ,EAAE;;;GAGZ,EAAE,CAACC,SAAS,EAAEH,OAAO,EAAEE,QAAQ,CAAC,CAAC;EAElC;EACA;EACA;EACAJ,yBAAyB,CAACO,eAAe,CAAC;EAE1C,OAAO,CAACF,SAAS,EAAEE,eAAe,CAAC", "names": ["equal", "useCallback", "useState", "useIsomorphicLayoutEffect", "useCollector", "monitor", "collect", "onUpdate", "collected", "setCollected", "updateCollected", "nextValue"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useCollector.ts"], "sourcesContent": ["import equal from 'fast-deep-equal'\nimport { useCallback, useState } from 'react'\n\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js'\n\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\nexport function useCollector<T, S>(\n\tmonitor: T,\n\tcollect: (monitor: T) => S,\n\tonUpdate?: () => void,\n): [S, () => void] {\n\tconst [collected, setCollected] = useState(() => collect(monitor))\n\n\tconst updateCollected = useCallback(() => {\n\t\tconst nextValue = collect(monitor)\n\t\t// This needs to be a deep-equality check because some monitor-collected values\n\t\t// include XYCoord objects that may be equivalent, but do not have instance equality.\n\t\tif (!equal(collected, nextValue)) {\n\t\t\tsetCollected(nextValue)\n\t\t\tif (onUpdate) {\n\t\t\t\tonUpdate()\n\t\t\t}\n\t\t}\n\t}, [collected, monitor, onUpdate])\n\n\t// update the collected properties after react renders.\n\t// Note that the \"Dustbin Stress Test\" fails if this is not\n\t// done when the component updates\n\tuseIsomorphicLayoutEffect(updateCollected)\n\n\treturn [collected, updateCollected]\n}\n"]}, "metadata": {}, "sourceType": "module"}