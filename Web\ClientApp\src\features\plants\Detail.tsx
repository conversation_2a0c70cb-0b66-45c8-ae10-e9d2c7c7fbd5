import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router';
import { Link } from 'react-router-dom';
import {
  Button,
  FormGroup,
  Input,
  InputGroup,
  InputGroupText,
  Label,
  UncontrolledTooltip,
  Dropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectPlants } from './plants-slice';
import { deletePlant, savePlant, selectPlant, setPlant } from './detail-slice';
import { createPlant, Variety } from 'api/models/plants';
import { handleFocus } from 'utils/focus';
import { selectColours } from 'features/colours/colours-slice';



export function Detail() {
  const dispatch = useDispatch(),
    navigate = useNavigate(),
    { isInRole } = useAuth(),
    { id } = useParams<{ id: string }>(),
    plants = useSelector(selectPlants),
    plant = useSelector(selectPlant),
    colours = useSelector(selectColours),
    [hasVarieties, setHasVarieties] = useState(false),
    [newVariety, setNewVariety] = useState(''),
    [varietyDropdownOpen, setVarietyDropdownOpen] = useState<{[key: string]: boolean}>({}),
    isNew = !plant._rev,
    canUpdate =
      (isNew && isInRole('create:plants')) || isInRole('update:plants'),
    canDelete = isInRole('delete:plants');

  useEffect(() => {
    const found = plants.find((p) => p._id === id);
    if (found && found._id !== plant._id) {
      dispatch(setPlant(found));
      setHasVarieties(!!found.varieties);
      setNewVariety('');
    } else if (id === 'new' && plant._rev) {
      dispatch(setPlant(createPlant()));
      setHasVarieties(false);
      setNewVariety('');
    }
  }, [dispatch, id, plant, plants]);

  useEffect(() => {
    return function cleanup() {
      dispatch(setPlant(createPlant()));
    };
  }, [dispatch]);

  const handleSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const size = e.target.value,
      name = `${size} ${plant.crop}`,
      update = { ...plant, size, name };

    dispatch(setPlant(update));
  };

  const handleCropChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const crop = e.target.value,
      name = `${plant.size} ${crop}`,
      update = { ...plant, crop, name };

    dispatch(setPlant(update));
  };

  const handleAbbreviationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const abbreviation = e.target.value,
      update = { ...plant, abbreviation };

    dispatch(setPlant(update));
  };

  const handleCuttingsPerPotChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const cuttingsPerPot = e.target.valueAsNumber,
      update = { ...plant, cuttingsPerPot };

    dispatch(setPlant(update));
  };

  const handleCuttingsPerTableTightChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const cuttingsPerTableTight = e.target.valueAsNumber,
      update = { ...plant, cuttingsPerTableTight };

    dispatch(setPlant(update));
  };

  const handleCuttingsPerTableSpacedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const cuttingsPerTableSpaced = e.target.valueAsNumber,
      update = { ...plant, cuttingsPerTableSpaced };

    dispatch(setPlant(update));
  };

  const handleCuttingsPerTablePartiallySpacedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const cuttingsPerTablePartiallySpaced = e.target.valueAsNumber,
      update = { ...plant, cuttingsPerTablePartiallySpaced };

    dispatch(setPlant(update));
  };

  const handlePotsPerCaseChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const potsPerCase = e.target.valueAsNumber,
      update = { ...plant, potsPerCase };

    dispatch(setPlant(update));
  };

  const handleHasLightsOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const hasLightsOut = e.target.checked,
      update = { ...plant, hasLightsOut };

    dispatch(setPlant(update));
  };

  const handleHasPinchingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const hasPinching = e.target.checked,
      update = { ...plant, hasPinching };

    if (!hasPinching) {
      update.pinchingPotsPerHour = 0;
      delete update.daysToPinch;
    } else {
      if (!update.daysToPinch) {
        update.daysToPinch = 1;
      }
    }

    dispatch(setPlant(update));
  };

  const handleDaysToPinchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const daysToPinch = e.target.valueAsNumber || 0,
      update = { ...plant, daysToPinch };

    dispatch(setPlant(update));
  };

  const handlePinchingingPotsPerHourChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const pinchingPotsPerHour = e.target.valueAsNumber,
      update = { ...plant, pinchingPotsPerHour };

    dispatch(setPlant(update));
  };

  const handleColourChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const colour = e.target.value,
      update = { ...plant };

    console.log(colour);

    if (colour) {
      update.colour = colour;
    } else {
      delete plant.colour;
    }

    dispatch(setPlant(update));
  };

  const handleClearColourClick = () => {
    const update = { ...plant };
    delete update.colour;
    dispatch(setPlant(update));
  };

  const handleAddColourClick = () => {
    const update = { ...plant, colour: '#ffffff' };
    dispatch(setPlant(update));
  };

  const handleHasVarietiesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const hasVarieties = e.target.checked,
      update = { ...plant };

    if (hasVarieties) {
      update.varieties = [];
    } else {
      delete update.varieties;
    }

    dispatch(setPlant(update));

    setHasVarieties(hasVarieties);
  };

  const handleStickingCuttingsPerHourChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const stickingCuttingsPerHour = e.target.valueAsNumber,
      update = { ...plant, stickingCuttingsPerHour };

    dispatch(setPlant(update));
  };

  const handleSpacingPotsPerHourChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const spacingPotsPerHour = e.target.valueAsNumber,
      update = { ...plant, spacingPotsPerHour };

    dispatch(setPlant(update));
  };

  const handlePackingCasesPerHourChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const packingCasesPerHour = e.target.valueAsNumber,
      update = { ...plant, packingCasesPerHour };

    dispatch(setPlant(update));
  };

  const handleNewVarietyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVariety = e.target.value;
    setNewVariety(newVariety);
  };

  const handleNewVarietyKeyUp = (e: React.KeyboardEvent) => {
    if (newVariety && e.key === 'Enter') {
      handleAddNewVarietyClick();
    }
  };

  const handleAddNewVarietyClick = () => {
    if (newVariety) {
      const update = { ...plant },
        varieties = (update.varieties || []).map((v) => ({ ...v }));

      varieties.push({ name: newVariety });

      update.varieties = varieties;
      dispatch(setPlant(update));
      setNewVariety('');
    }
  };

  const handleVarietyNameChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    variety: Variety
  ) => {
    const update = { ...plant },
      varieties = (update.varieties || []).map((v) => ({ ...v })),
      index = varieties.findIndex((v) => v.name === variety.name);

    if (index !== -1) {
      const name = e.target.value,
        updated = { ...varieties[index], name };
      varieties.splice(index, 1, updated);
    }

    update.varieties = varieties;

    dispatch(setPlant(update));
  };



  const handleDeleteVarietyClick = (variety: Variety) => {
    const update = { ...plant },
      varieties = (update.varieties || []).map((v) => ({ ...v })),
      index = varieties.findIndex((v) => v.name === variety.name);

    if (index !== -1) {
      varieties.splice(index, 1);
    }

    update.varieties = varieties;

    dispatch(setPlant(update));
  };

  const toggleVarietyDropdown = (varietyName: string) => {
    setVarietyDropdownOpen(prev => ({
      ...prev,
      [varietyName]: !prev[varietyName]
    }));
  };

  const handleVarietyColourSelect = (variety: Variety, colourName: string) => {
    const colour = colourName ? colours.find((c) => c.name === colourName) || null : null;
    const update = { ...plant },
      varieties = (update.varieties || []).map((v) => ({ ...v })),
      index = varieties.findIndex((v) => v.name === variety.name);

    if (index !== -1) {
      const updated = { ...varieties[index], colour };
      varieties.splice(index, 1, updated);
    }

    update.varieties = varieties;
    dispatch(setPlant(update));

    // Close the dropdown
    setVarietyDropdownOpen(prev => ({
      ...prev,
      [variety.name]: false
    }));
  };

  const handleSaveClick = async () => {
    const result: any = await dispatch(savePlant());

    if (!result.error) {
      navigate(routes.plants.path);
    }
  };

  const handleDeleteClick = async () => {
    const result: any = await dispatch(deletePlant());

    if (!result.error) {
      navigate(routes.plants.path);
    }
  };

  return (
    <div className="container d-grid gap-3">
      <div className="row sticky-top-navbar my-2 py-2 bg-white shadow">
        <div className="col-auto pt-3">
          <Link to={routes.plants.path}>
            <FontAwesomeIcon icon={['fat', 'chevron-left']} />
            &nbsp; Back to Plants List
          </Link>
        </div>
        <h1 className="col">{isNew ? 'New Plant' : plant.name}</h1>
      </div>
      <div className="row">
        <div className="col-12 col-md-3">
          <label htmlFor="plant-size">Size</label>
          <Input
            id="plant-size"
            value={plant.size}
            onChange={handleSizeChange}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-crop">Crop</label>
          <Input
            id="plant-crop"
            value={plant.crop}
            onChange={handleCropChange}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-abbreviation">Abbreviation</label>
          <Input
            id="plant-abbreviation"
            value={plant.abbreviation}
            onChange={handleAbbreviationChange}
            disabled={!canUpdate}
          />
        </div>
      </div>
      <div className="row">
        <div className="col-12 col-md-3">
          <label htmlFor="plant-cuttings-per-pot">Cuttings per Pot</label>
          <Input
            id="plant-cuttings-per-pot"
            type="number"
            value={plant.cuttingsPerPot}
            onChange={handleCuttingsPerPotChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-pots-per-case">Pots Per Case</label>
          <Input
            id="plant-pots-per-case"
            type="number"
            value={plant.potsPerCase}
            onChange={handlePotsPerCaseChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
      </div>
      <div className="row">
        <div className="col-12 col-md-3">
          <label htmlFor="plant-cuttings-per-table-tight">
            Cuttings Per Table: Tight
          </label>
          <Input
            id="plant-cuttings-per-table-tight"
            type="number"
            value={plant.cuttingsPerTableTight}
            onChange={handleCuttingsPerTableTightChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-cuttings-per-table-partially-spaced">
            Cuttings Per Table: Partially Spaced
          </label>
          <Input
            id="plant-cuttings-per-table-partially-spaced"
            type="number"
            value={plant.cuttingsPerTablePartiallySpaced}
            onChange={handleCuttingsPerTablePartiallySpacedChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-cuttings-per-table-spaced">
            Cuttings Per Table: Spaced
          </label>
          <Input
            id="plant-cuttings-per-table-spaced"
            type="number"
            value={plant.cuttingsPerTableSpaced}
            onChange={handleCuttingsPerTableSpacedChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
      </div>
      <div className="row">
        <div className="col-12 col-md-3">
          <label htmlFor="plant-sticking-cuttings-per-hour">
            Sticking: Cuttings per Hour
          </label>
          <Input
            id="plant-sticking-cuttings-per-hour"
            type="number"
            value={plant.stickingCuttingsPerHour}
            onChange={handleStickingCuttingsPerHourChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-spacing-pots-per-hour">
            Spacing: Pots per Hour
          </label>
          <Input
            id="plant-spacing-pots-per-hour"
            type="number"
            value={plant.spacingPotsPerHour}
            onChange={handleSpacingPotsPerHourChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-packing-cases-per-hour">
            Packing: Cases per Hour
          </label>
          <Input
            id="plant-packing-cases-per-hour"
            type="number"
            value={plant.packingCasesPerHour}
            onChange={handlePackingCasesPerHourChange}
            onFocus={handleFocus}
            disabled={!canUpdate}
          />
        </div>
        {!!plant.hasPinching && (
          <div className="col-12 col-md-3">
            <label htmlFor="plant-pinching-pots-per-hour">
              Pinching: Pots per Hour
            </label>
            <Input
              id="plant-pinching-pots-per-hour"
              type="number"
              value={plant.pinchingPotsPerHour}
              onChange={handlePinchingingPotsPerHourChange}
              onFocus={handleFocus}
              disabled={!canUpdate}
            />
          </div>
        )}
      </div>
      <div className="row my-3">
        <div className="col-12 col-md-3">
          <div className="form-check">
            <Input
              id="plant-has-lights-out"
              type="checkbox"
              checked={plant.hasLightsOut}
              onChange={handleHasLightsOutChange}
              disabled={!canUpdate}
            />
            <label htmlFor="plant-has-lights-out">Has Lights Out</label>
          </div>
        </div>
        <div className="col-12 col-md-3">
          <div className="form-check">
            <Input
              id="plant-needs-pinching"
              type="checkbox"
              checked={plant.hasPinching}
              onChange={handleHasPinchingChange}
              disabled={!canUpdate}
            />
            <label htmlFor="plant-needs-pinching">Needs Pinching</label>
          </div>
          {!!plant.hasPinching && (
            <FormGroup>
              <label htmlFor="plant-pinching-pots-per-hour">
                Days after sticking to pinch
              </label>
              <Input
                id="plant-pinching-pots-per-hour"
                type="number"
                value={plant.daysToPinch || ''}
                onChange={handleDaysToPinchChange}
                onFocus={handleFocus}
                disabled={!canUpdate}
              />
            </FormGroup>
          )}
        </div>
        <div className="col-12 col-md-3">
          <label htmlFor="plant-colour">Colour</label>
          {!!plant.colour && (
            <InputGroup>
              <Input
                id="plant-colour"
                type="color"
                value={plant.colour || ''}
                onChange={handleColourChange}
                disabled={!canUpdate}
              />
              {canUpdate && (
                <Button
                  size="sm"
                  color="danger"
                  outline
                  onClick={handleClearColourClick}>
                  <FontAwesomeIcon icon={['fat', 'trash']} />
                </Button>
              )}
            </InputGroup>
          )}
          {!plant.colour && (
            <InputGroup>
              <InputGroupText>No Colour</InputGroupText>
              {canUpdate && (
                <>
                  <Button
                    id="add-colour"
                    size="sm"
                    color="success"
                    outline
                    onClick={handleAddColourClick}>
                    <FontAwesomeIcon icon={['fat', 'palette']} />
                  </Button>
                  <UncontrolledTooltip target="add-colour">
                    Add Colour
                  </UncontrolledTooltip>
                </>
              )}
            </InputGroup>
          )}
        </div>
      </div>
      <div className="row">
        <div className="col-12 col-md-3">
          <div className="form-check">
            <Input
              id="plant-has-varieties"
              type="checkbox"
              checked={hasVarieties}
              onChange={handleHasVarietiesChange}
              disabled={!canUpdate}
            />
            <label htmlFor="plant-has-varieties">Has Varieties</label>
          </div>
        </div>
        {hasVarieties && (
          <div className="col-6">
            {canUpdate && (
              <div className="row">
                <div className="col-5">
                  <FormGroup floating>
                    <Input
                      id="new-variety-name"
                      value={newVariety}
                      onChange={handleNewVarietyChange}
                      onKeyUp={handleNewVarietyKeyUp}
                      placeholder="Add Variety"
                    />
                    <Label htmlFor="new-variety-name">Add Variety</Label>
                  </FormGroup>
                </div>
                <div className="col-4">
                  <Button
                    outline
                    color="success"
                    onClick={handleAddNewVarietyClick}
                    disabled={!newVariety}
                    size="sm">
                    <FontAwesomeIcon icon={['fat', 'plus']} />
                  </Button>
                </div>
              </div>
            )}
            {plant.varieties?.map((variety) => (
              <div className="row">
                <div className="col-5">
                  <Input
                    value={variety.name}
                    onChange={(e) => handleVarietyNameChange(e, variety)}
                    disabled={!canUpdate}
                  />
                </div>
                <div className="col-3">
                  <Dropdown
                    isOpen={varietyDropdownOpen[variety.name] || false}
                    toggle={() => toggleVarietyDropdown(variety.name)}
                    disabled={!canUpdate}>
                    <DropdownToggle
                      caret
                      className="w-100 text-start d-flex align-items-center justify-content-between"
                      style={{
                        backgroundColor: 'white',
                        borderColor: '#ced4da',
                        color: '#495057'
                      }}>
                      <div className="d-flex align-items-center">
                        {variety.colour ? (
                          <>
                            <div
                              className='w16 h16 border rounded'
                            />
                            {variety.colour.name}
                          </>
                        ) : (
                          '---'
                        )}
                      </div>
                    </DropdownToggle>
                    <DropdownMenu className="w-100">
                      <DropdownItem onClick={() => handleVarietyColourSelect(variety, '')}>
                        ---
                      </DropdownItem>
                      {colours.map((c) => (
                        <DropdownItem
                          key={c.name}
                          onClick={() => handleVarietyColourSelect(variety, c.name)}
                          className="d-flex align-items-center">
                          <div
                            style={{
                              width: '16px',
                              height: '16px',
                              backgroundColor: c.hex,
                              border: '1px solid #ccc',
                              marginRight: '8px',
                              borderRadius: '2px'
                            }}
                          />
                          {c.name}
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                </div>
                {canUpdate && (
                  <div className="col-4">
                    <Button
                      outline
                      color="danger"
                      onClick={() => handleDeleteVarietyClick(variety)}
                      size="sm">
                      <FontAwesomeIcon icon={['fat', 'trash-alt']} />
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
      <div className="row sticky-bottom bg-white border-top py-2">
        {!isNew && canDelete && (
          <div className="col-auto">
            <Button
              onClick={handleDeleteClick}
              outline
              color="danger"
              size="lg"
              className="me-auto">
              <FontAwesomeIcon icon={['fat', 'trash-alt']} />
              &nbsp; Delete
            </Button>
          </div>
        )}
        <div className="col text-end">
          <Button tag={Link} to={routes.plants.path} outline size="lg">
            {canUpdate ? 'Cancel' : 'Close'}
          </Button>
          {canUpdate && (
            <>
              &nbsp;
              <Button onClick={handleSaveClick} color="success" size="lg">
                <FontAwesomeIcon icon={['fat', 'save']} />
                &nbsp; Save
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default Detail;
