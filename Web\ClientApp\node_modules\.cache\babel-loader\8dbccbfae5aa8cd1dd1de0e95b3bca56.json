{"ast": null, "code": "export {};", "map": {"version": 3, "mappings": "AAAA", "names": [], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\types\\monitors.ts"], "sourcesContent": ["import type { Identifier, Unsubscribe } from 'dnd-core'\n\nexport interface XYCoord {\n\tx: number\n\ty: number\n}\n\nexport interface HandlerManager {\n\treceiveHandlerId: (handlerId: Identifier | null) => void\n\tgetHandlerId: () => Identifier | null\n}\n\nexport interface DragSourceMonitor<DragObject = unknown, DropResult = unknown>\n\textends HandlerManager,\n\t\tMonitorEventEmitter {\n\t/**\n\t * Returns true if no drag operation is in progress, and the owner's canDrag() returns true or is not defined.\n\t */\n\tcanDrag(): boolean\n\n\t/**\n\t *  Returns true if a drag operation is in progress, and either the owner initiated the drag, or its isDragging() is defined and returns true.\n\t */\n\tisDragging(): boolean\n\n\t/**\n\t * Returns a string or a symbol identifying the type of the current dragged item. Returns null if no item is being dragged.\n\t */\n\tgetItemType(): Identifier | null\n\n\t/**\n\t * Returns a plain object representing the currently dragged item. Every drag source must specify it by returning an object from its beginDrag() method.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetItem<T = DragObject>(): T\n\n\t/**\n\t * Returns a plain object representing the last recorded drop result. The drop targets may optionally specify it by returning an object from their\n\t * drop() methods. When a chain of drop() is dispatched for the nested targets, bottom up, any parent that explicitly returns its own result from drop()\n\t * overrides the child drop result previously set by the child. Returns null if called outside endDrag().\n\t */\n\tgetDropResult<T = DropResult>(): T | null\n\n\t/**\n\t *  Returns true if some drop target has handled the drop event, false otherwise. Even if a target did not return a drop result, didDrop() returns true.\n\t * Use it inside endDrag() to test whether any drop target has handled the drop. Returns false if called outside endDrag().\n\t */\n\tdidDrop(): boolean\n\n\t/**\n\t * Returns the { x, y } client offset of the pointer at the time when the current drag operation has started. Returns null if no item is being dragged.\n\t */\n\tgetInitialClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } client offset of the drag source component's root DOM node at the time when the current drag operation has started.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetInitialSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the last recorded { x, y } client offset of the pointer while a drag operation is in progress. Returns null if no item is being dragged.\n\t */\n\tgetClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } difference between the last recorded client offset of the pointer and the client offset when the current drag operation has started.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetDifferenceFromInitialOffset(): XYCoord | null\n\n\t/**\n\t * Returns the projected { x, y } client offset of the drag source component's root DOM node, based on its position at the time when the current drag operation has\n\t * started, and the movement difference. Returns null if no item is being dragged.\n\t */\n\tgetSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the ids of the potential drop targets.\n\t */\n\tgetTargetIds(): Identifier[]\n}\n\nexport interface MonitorEventEmitter {\n\tsubscribeToStateChange(\n\t\tfn: () => void,\n\t\toptions?: { handlerIds?: Identifier[] },\n\t): Unsubscribe\n}\n\nexport interface DropTargetMonitor<DragObject = unknown, DropResult = unknown>\n\textends HandlerManager,\n\t\tMonitorEventEmitter {\n\t/**\n\t * Returns true if there is a drag operation in progress, and the owner's canDrop() returns true or is not defined.\n\t */\n\tcanDrop(): boolean\n\n\t/**\n\t * Returns true if there is a drag operation in progress, and the pointer is currently hovering over the owner.\n\t * You may optionally pass { shallow: true } to strictly check whether only the owner is being hovered, as opposed\n\t * to a nested target.\n\t */\n\tisOver(options?: { shallow?: boolean }): boolean\n\n\t/**\n\t * Returns a string or a symbol identifying the type of the current dragged item. Returns null if no item is being dragged.\n\t */\n\tgetItemType(): Identifier | null\n\n\t/**\n\t * Returns a plain object representing the currently dragged item. Every drag source must specify it by returning an object from\n\t * its beginDrag() method. Returns null if no item is being dragged.\n\t */\n\tgetItem<T = DragObject>(): T\n\n\t/**\n\t * Returns a plain object representing the last recorded drop result. The drop targets may optionally specify it by returning an\n\t * object from their drop() methods. When a chain of drop() is dispatched for the nested targets, bottom up, any parent that explicitly\n\t * returns its own result from drop() overrides the drop result previously set by the child. Returns null if called outside drop().\n\t */\n\tgetDropResult<T = DropResult>(): T | null\n\n\t/**\n\t *  Returns true if some drop target has handled the drop event, false otherwise. Even if a target did not return a drop result,\n\t * didDrop() returns true. Use it inside drop() to test whether any nested drop target has already handled the drop. Returns false\n\t * if called outside drop().\n\t */\n\tdidDrop(): boolean\n\n\t/**\n\t * Returns the { x, y } client offset of the pointer at the time when the current drag operation has started. Returns null if no item\n\t * is being dragged.\n\t */\n\tgetInitialClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } client offset of the drag source component's root DOM node at the time when the current drag operation has started.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetInitialSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the last recorded { x, y } client offset of the pointer while a drag operation is in progress. Returns null if no item is being dragged.\n\t */\n\tgetClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } difference between the last recorded client offset of the pointer and the client offset when current the drag operation has\n\t * started. Returns null if no item is being dragged.\n\t */\n\tgetDifferenceFromInitialOffset(): XYCoord | null\n\n\t/**\n\t * Returns the projected { x, y } client offset of the drag source component's root DOM node, based on its position at the time when the current\n\t * drag operation has started, and the movement difference. Returns null if no item is being dragged.\n\t */\n\tgetSourceClientOffset(): XYCoord | null\n}\n\nexport interface DragLayerMonitor<DragObject = unknown> {\n\t/**\n\t * Returns true if a drag operation is in progress. Returns false otherwise.\n\t */\n\tisDragging(): boolean\n\n\t/**\n\t * Returns a string or a symbol identifying the type of the current dragged item.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetItemType(): Identifier | null\n\n\t/**\n\t * Returns a plain object representing the currently dragged item.\n\t * Every drag source must specify it by returning an object from its beginDrag() method.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetItem<T = DragObject>(): T\n\n\t/**\n\t * Returns the { x, y } client offset of the pointer at the time when the current drag operation has started.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetInitialClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } client offset of the drag source component's root DOM node at the time when the current\n\t * drag operation has started. Returns null if no item is being dragged.\n\t */\n\tgetInitialSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the last recorded { x, y } client offset of the pointer while a drag operation is in progress.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } difference between the last recorded client offset of the pointer and the client\n\t * offset when current the drag operation has started. Returns null if no item is being dragged.\n\t */\n\tgetDifferenceFromInitialOffset(): XYCoord | null\n\n\t/**\n\t * Returns the projected { x, y } client offset of the drag source component's root DOM node, based on its\n\t * position at the time when the current drag operation has started, and the movement difference.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetSourceClientOffset(): XYCoord | null\n}\n"]}, "metadata": {}, "sourceType": "module"}