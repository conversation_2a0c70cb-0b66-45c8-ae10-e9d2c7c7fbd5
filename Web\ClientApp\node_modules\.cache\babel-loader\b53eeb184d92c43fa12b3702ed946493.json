{"ast": null, "code": "let emptyImage;\nexport function getEmptyImage() {\n  if (!emptyImage) {\n    emptyImage = new Image();\n    emptyImage.src = 'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==';\n  }\n  return emptyImage;\n}", "map": {"version": 3, "mappings": "AAAA,IAAIA,UAAU;AAEd,OAAO,SAASC,aAAa,GAAqB;EACjD,IAAI,CAACD,UAAU,EAAE;IAChBA,UAAU,GAAG,IAAIE,KAAK,EAAE;IACxBF,UAAU,CAACG,GAAG,GACb,4EAA4E;;EAG9E,OAAOH,UAAU", "names": ["emptyImage", "getEmptyImage", "Image", "src"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd-html5-backend\\src\\getEmptyImage.ts"], "sourcesContent": ["let emptyImage: HTMLImageElement | undefined\n\nexport function getEmptyImage(): HTMLImageElement {\n\tif (!emptyImage) {\n\t\temptyImage = new Image()\n\t\temptyImage.src =\n\t\t\t'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=='\n\t}\n\n\treturn emptyImage\n}\n"]}, "metadata": {}, "sourceType": "module"}