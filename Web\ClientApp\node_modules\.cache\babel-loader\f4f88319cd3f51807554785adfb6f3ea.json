{"ast": null, "code": "export class DropTargetImpl {\n  canDrop() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    return spec.canDrop ? spec.canDrop(monitor.getItem(), monitor) : true;\n  }\n  hover() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    if (spec.hover) {\n      spec.hover(monitor.getItem(), monitor);\n    }\n  }\n  drop() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    if (spec.drop) {\n      return spec.drop(monitor.getItem(), monitor);\n    }\n    return;\n  }\n  constructor(spec, monitor) {\n    this.spec = spec;\n    this.monitor = monitor;\n  }\n}", "map": {"version": 3, "mappings": "AAKA,OAAO,MAAMA,cAAc;EAM1BC,OAAc,GAAG;IAChB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,OAAOD,IAAI,CAACD,OAAO,GAAGC,IAAI,CAACD,OAAO,CAACE,OAAO,CAACC,OAAO,EAAE,EAAED,OAAO,CAAC,GAAG,IAAI;;EAGtEE,KAAY,GAAG;IACd,MAAMH,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAID,IAAI,CAACG,KAAK,EAAE;MACfH,IAAI,CAACG,KAAK,CAACF,OAAO,CAACC,OAAO,EAAE,EAAED,OAAO,CAAC;;;EAIxCG,IAAW,GAAG;IACb,MAAMJ,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAID,IAAI,CAACI,IAAI,EAAE;MACd,OAAOJ,IAAI,CAACI,IAAI,CAACH,OAAO,CAACC,OAAO,EAAE,EAAED,OAAO,CAAC;;IAE7C;;EAzBDI,YACQL,IAAiC,EAChCC,OAAgC,EACvC;SAFMD,IAAiC,GAAjCA,IAAiC;SAChCC,OAAgC,GAAhCA,OAAgC", "names": ["DropTargetImpl", "canDrop", "spec", "monitor", "getItem", "hover", "drop", "constructor"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrop\\DropTargetImpl.ts"], "sourcesContent": ["import type { DropTarget } from 'dnd-core'\n\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\n\nexport class DropTargetImpl<O, R, P> implements DropTarget {\n\tpublic constructor(\n\t\tpublic spec: DropTargetHookSpec<O, R, P>,\n\t\tprivate monitor: DropTargetMonitor<O, R>,\n\t) {}\n\n\tpublic canDrop() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\treturn spec.canDrop ? spec.canDrop(monitor.getItem(), monitor) : true\n\t}\n\n\tpublic hover() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (spec.hover) {\n\t\t\tspec.hover(monitor.getItem(), monitor)\n\t\t}\n\t}\n\n\tpublic drop() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (spec.drop) {\n\t\t\treturn spec.drop(monitor.getItem(), monitor)\n\t\t}\n\t\treturn\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}