{"ast": null, "code": "import { createDragDropActions } from '../actions/dragDrop/index.js';\nexport class DragDropManagerImpl {\n  receiveBackend(backend) {\n    this.backend = backend;\n  }\n  getMonitor() {\n    return this.monitor;\n  }\n  getBackend() {\n    return this.backend;\n  }\n  getRegistry() {\n    return this.monitor.registry;\n  }\n  getActions() {\n    /* eslint-disable-next-line @typescript-eslint/no-this-alias */const manager = this;\n    const {\n      dispatch\n    } = this.store;\n    function bindActionCreator(actionCreator) {\n      return function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        const action = actionCreator.apply(manager, args);\n        if (typeof action !== 'undefined') {\n          dispatch(action);\n        }\n      };\n    }\n    const actions = createDragDropActions(this);\n    return Object.keys(actions).reduce((boundActions, key) => {\n      const action = actions[key];\n      boundActions[key] = bindActionCreator(action);\n      return boundActions;\n    }, {});\n  }\n  dispatch(action) {\n    this.store.dispatch(action);\n  }\n  constructor(store, monitor) {\n    this.isSetUp = false;\n    this.handleRefCountChange = () => {\n      const shouldSetUp = this.store.getState().refCount > 0;\n      if (this.backend) {\n        if (shouldSetUp && !this.isSetUp) {\n          this.backend.setup();\n          this.isSetUp = true;\n        } else if (!shouldSetUp && this.isSetUp) {\n          this.backend.teardown();\n          this.isSetUp = false;\n        }\n      }\n    };\n    this.store = store;\n    this.monitor = monitor;\n    store.subscribe(this.handleRefCountChange);\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,qBAAqB,QAAQ,8BAA8B;AAYpE,OAAO,MAAMC,mBAAmB;EAY/BC,cAAqB,CAACC,OAAgB,EAAQ;IAC7C,IAAI,CAACA,OAAO,GAAGA,OAAO;;EAGvBC,UAAiB,GAAoB;IACpC,OAAO,IAAI,CAACC,OAAO;;EAGpBC,UAAiB,GAAY;IAC5B,OAAO,IAAI,CAACH,OAAO;;EAGpBI,WAAkB,GAAoB;IACrC,OAAO,IAAK,CAACF,OAAO,CAAyBG,QAAQ;;EAGtDC,UAAiB,GAAoB;IACpC,+DACA,MAAMC,OAAO,GAAG,IAAI;IACpB,MAAM;MAAEC;IAAQ,CAAE,GAAG,IAAI,CAACC,KAAK;IAE/B,SAASC,iBAAiB,CAACC,aAAiC,EAAE;MAC7D,OAAO,YAAoB;QAAA,kCAAhBC,IAAI;UAAJA,IAAI;QAAA;QACd,MAAMC,MAAM,GAAGF,aAAa,CAACG,KAAK,CAACP,OAAO,EAAEK,IAAI,CAAQ;QACxD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;UAClCL,QAAQ,CAACK,MAAM,CAAC;;OAEjB;;IAGF,MAAME,OAAO,GAAGlB,qBAAqB,CAAC,IAAI,CAAC;IAE3C,OAAOmB,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,CACjC,CAACC,YAA6B,EAAEC,GAAW,KAAK;MAC/C,MAAMP,MAAM,GAAuBE,OAAQ,CAC1CK,GAAG,CACH;MACAD,YAAa,CAASC,GAAG,CAAC,GAAGV,iBAAiB,CAACG,MAAM,CAAC;MACvD,OAAOM,YAAY;KACnB,EACD,EAAE,CACF;;EAGFX,QAAe,CAACK,MAAmB,EAAQ;IAC1C,IAAI,CAACJ,KAAK,CAACD,QAAQ,CAACK,MAAM,CAAC;;EAnD5BQ,YAAmBZ,KAAmB,EAAEP,OAAwB,EAAE;IAFlE,KAAQoB,OAAO,GAAG,KAAK;IAwDvB,KAAQC,oBAAoB,GAAG,MAAY;MAC1C,MAAMC,WAAW,GAAG,IAAI,CAACf,KAAK,CAACgB,QAAQ,EAAE,CAACC,QAAQ,GAAG,CAAC;MACtD,IAAI,IAAI,CAAC1B,OAAO,EAAE;QACjB,IAAIwB,WAAW,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;UACjC,IAAI,CAACtB,OAAO,CAAC2B,KAAK,EAAE;UACpB,IAAI,CAACL,OAAO,GAAG,IAAI;SACnB,MAAM,IAAI,CAACE,WAAW,IAAI,IAAI,CAACF,OAAO,EAAE;UACxC,IAAI,CAACtB,OAAO,CAAC4B,QAAQ,EAAE;UACvB,IAAI,CAACN,OAAO,GAAG,KAAK;;;KAGtB;IAhEA,IAAI,CAACb,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACP,OAAO,GAAGA,OAAO;IACtBO,KAAK,CAACoB,SAAS,CAAC,IAAI,CAACN,oBAAoB,CAAC", "names": ["createDragDropActions", "DragDropManagerImpl", "receiveBackend", "backend", "getMonitor", "monitor", "getBackend", "getRegistry", "registry", "getActions", "manager", "dispatch", "store", "bindActionCreator", "actionCreator", "args", "action", "apply", "actions", "Object", "keys", "reduce", "boundActions", "key", "constructor", "isSetUp", "handleRefCountChange", "shouldSetUp", "getState", "refCount", "setup", "teardown", "subscribe"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\classes\\DragDropManagerImpl.ts"], "sourcesContent": ["import type { Action, Store } from 'redux'\n\nimport { createDragDropActions } from '../actions/dragDrop/index.js'\nimport type {\n\tActionCreator,\n\tBackend,\n\tDragDropActions,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n} from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport type { DragDropMonitorImpl } from './DragDropMonitorImpl.js'\n\nexport class DragDropManagerImpl implements DragDropManager {\n\tprivate store: Store<State>\n\tprivate monitor: DragDropMonitor\n\tprivate backend: Backend | undefined\n\tprivate isSetUp = false\n\n\tpublic constructor(store: Store<State>, monitor: DragDropMonitor) {\n\t\tthis.store = store\n\t\tthis.monitor = monitor\n\t\tstore.subscribe(this.handleRefCountChange)\n\t}\n\n\tpublic receiveBackend(backend: Backend): void {\n\t\tthis.backend = backend\n\t}\n\n\tpublic getMonitor(): DragDropMonitor {\n\t\treturn this.monitor\n\t}\n\n\tpublic getBackend(): Backend {\n\t\treturn this.backend as Backend\n\t}\n\n\tpublic getRegistry(): HandlerRegistry {\n\t\treturn (this.monitor as DragDropMonitorImpl).registry\n\t}\n\n\tpublic getActions(): DragDropActions {\n\t\t/* eslint-disable-next-line @typescript-eslint/no-this-alias */\n\t\tconst manager = this\n\t\tconst { dispatch } = this.store\n\n\t\tfunction bindActionCreator(actionCreator: ActionCreator<any>) {\n\t\t\treturn (...args: any[]) => {\n\t\t\t\tconst action = actionCreator.apply(manager, args as any)\n\t\t\t\tif (typeof action !== 'undefined') {\n\t\t\t\t\tdispatch(action)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst actions = createDragDropActions(this)\n\n\t\treturn Object.keys(actions).reduce(\n\t\t\t(boundActions: DragDropActions, key: string) => {\n\t\t\t\tconst action: ActionCreator<any> = (actions as any)[\n\t\t\t\t\tkey\n\t\t\t\t] as ActionCreator<any>\n\t\t\t\t;(boundActions as any)[key] = bindActionCreator(action)\n\t\t\t\treturn boundActions\n\t\t\t},\n\t\t\t{} as DragDropActions,\n\t\t)\n\t}\n\n\tpublic dispatch(action: Action<any>): void {\n\t\tthis.store.dispatch(action)\n\t}\n\n\tprivate handleRefCountChange = (): void => {\n\t\tconst shouldSetUp = this.store.getState().refCount > 0\n\t\tif (this.backend) {\n\t\t\tif (shouldSetUp && !this.isSetUp) {\n\t\t\t\tthis.backend.setup()\n\t\t\t\tthis.isSetUp = true\n\t\t\t} else if (!shouldSetUp && this.isSetUp) {\n\t\t\t\tthis.backend.teardown()\n\t\t\t\tthis.isSetUp = false\n\t\t\t}\n\t\t}\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}