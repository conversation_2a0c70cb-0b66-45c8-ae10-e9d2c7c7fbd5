import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router';
import { Link } from 'react-router-dom';
import { Button, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectCustomers } from './customers-slice';
import { deleteCustomer, saveCustomer, selectCustomer, setCustomer } from './detail-slice';
import { createCustomer } from 'api/models/customers';
import { handleFocus } from 'utils/focus';

export function Detail() {
  const dispatch = useDispatch(),
    navigate = useNavigate(),
    {isInRole} = useAuth(),
    {id} = useParams<{id: string}>(),
    customers = useSelector(selectCustomers),
    customer = useSelector(selectCustomer),
    isNew = !customer._rev,
    canUpdate = (isNew && isInRole('create:customers')) || isInRole('update:customers'),
    canDelete = isInRole('delete:customers');

  useEffect(() => {
    const found = customers.find(p => p._id === id);
    if(found && found._id !== customer._id) {
      dispatch(setCustomer(found));
    } else if(id === 'new' && customer._rev) {
      dispatch(setCustomer(createCustomer()));
    }
  }, [dispatch, id, customer, customers]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value,
      update = {...customer, name};

    dispatch(setCustomer(update));
  };

  const handleAbbreviationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const abbreviation = e.target.value,
      update = {...customer, abbreviation};

    dispatch(setCustomer(update));
  };

  const handleSaveClick = async () => {
    const result: any = await dispatch(saveCustomer());

    if(!result.error) {
      navigate(routes.customers.path);
    }
  };

  const handleDeleteClick = async () => {
    const result: any = await dispatch(deleteCustomer());

    if(!result.error) {
      navigate(routes.customers.path);
    }
  };

  return (
    <div className="container d-grid gap-2">
      <div className="row">
        <div className="col">
          <Link to={routes.customers.path}>
            <FontAwesomeIcon icon={['fat', 'chevron-left']} />
            &nbsp;
            Back to Customers List</Link>
        </div>
      </div>
      <h1>{isNew ? 'New Customer' : customer.name}</h1>      
      <div className="row">
        <div className="col-12 col-md-4">
          <label htmlFor="customer-name">Name</label>
          <Input id="customer-name" value={customer.name} onChange={handleNameChange} disabled={!canUpdate} />
        </div>
        <div className="col-12 col-md-4">
          <label htmlFor="customer-abbreviation">Abbreviation</label>
          <Input id="customer-abbreviation" value={customer.abbreviation} onChange={handleAbbreviationChange} onFocus={handleFocus} disabled={!canUpdate} />
        </div>
      </div>
      <div className="row sticky-bottom bg-white border-top py-2">        
        {!isNew && canDelete &&
          <div className="col-auto">
            <Button onClick={handleDeleteClick} outline color="danger" size="lg" className="me-auto">
              <FontAwesomeIcon icon={['fat', 'trash-alt']} />
              &nbsp;
              Delete
            </Button>
          </div>
        }
        <div className="col text-end">
          <Button tag={Link} to={routes.customers.path} outline size="lg">{canUpdate ? 'Cancel' : 'Close'}</Button>
          {canUpdate &&
          <>
            &nbsp;
            <Button onClick={handleSaveClick} color="success" size="lg">
              <FontAwesomeIcon icon={['fat', 'save']} />
              &nbsp;
              Save
            </Button>
          </>
          }
        </div>
      </div>
    </div>
  )
}

export default Detail;
