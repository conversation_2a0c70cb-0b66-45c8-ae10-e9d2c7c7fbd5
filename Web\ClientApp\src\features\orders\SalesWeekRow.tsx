import { useState } from 'react';
import { Button, Input } from 'reactstrap';
import moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { SalesWeek } from 'api/models/orders';
import { useAuth } from 'features/auth/use-auth';
import { handleFocus } from 'utils/focus';
import { parseWeekAndYear } from 'utils/format';

interface SalesWeekRowProps {
  salesWeek: SalesWeek;
  onChange: (id: string, salesWeek: SalesWeek | null) => void;
}

export function SalesWeekRow({ salesWeek, onChange }: SalesWeekRowProps) {
  const { isInRole } = useAuth(),
    salesWeekDate = parseWeekAndYear(salesWeek.week) ?? new Date(),
    m = moment(salesWeekDate),
    [cases, setCases] = useState(salesWeek.cases),
    [year, setYear] = useState(m.isoWeekYear()),
    [week, setWeek] = useState(m.isoWeek()),
    canUpdate = isInRole('update:orders');

  const handleCasesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cases = e.target.valueAsNumber || 0;

    setCases(cases);
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const year = e.target.valueAsNumber || 0;

    setYear(year);
  };

  const handleWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const week = e.target.valueAsNumber || 0;

    setWeek(week);
  };

  const handleBlur = () => {
    const update = { id: salesWeek.id, week: `${week}/${year}`, cases };
    onChange(salesWeek.id, update);
  };

  const handleDeleteClick = () => {
    onChange(salesWeek.id, null);
  };

  return (
    <div className="row pt-2 mt-2 border-top">
      <div className="col-12 col-md-1 text-center">
        {canUpdate && (
          <Button
            color="danger"
            outline
            size="sm"
            onClick={handleDeleteClick}
            className="mt-1">
            <FontAwesomeIcon icon={['fat', 'trash']} />
          </Button>
        )}
      </div>
      <div className="col-12 col-md-2">
        <div className="row gx-0">
          <div className="col-12 col-md-6">
            <Input
              type="number"
              value={year}
              onChange={handleYearChange}
              onBlur={handleBlur}
              onFocus={handleFocus}
              disabled={!canUpdate}
            />
          </div>
          <div className="col-12 col-md-6">
            <Input
              type="number"
              value={week}
              onChange={handleWeekChange}
              onBlur={handleBlur}
              onFocus={handleFocus}
              disabled={!canUpdate}
            />
          </div>
        </div>
      </div>
      <div className="col-12 col-md-2">
        <Input
          type="number"
          className="text-end"
          value={cases}
          onChange={handleCasesChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          disabled={!canUpdate}
        />
      </div>
    </div>
  );
}
