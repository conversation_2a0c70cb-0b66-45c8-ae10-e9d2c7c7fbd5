{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { isObject } from '../../utils/js_utils.js';\nimport { setClientOffset } from './local/setClientOffset.js';\nimport { BEGIN_DRAG, INIT_COORDS } from './types.js';\nconst ResetCoordinatesAction = {\n  type: INIT_COORDS,\n  payload: {\n    clientOffset: null,\n    sourceClientOffset: null\n  }\n};\nexport function createBeginDrag(manager) {\n  return function beginDrag() {\n    let sourceIds = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      publishSource: true\n    };\n    const {\n      publishSource = true,\n      clientOffset,\n      getSourceClientOffset\n    } = options;\n    const monitor = manager.getMonitor();\n    const registry = manager.getRegistry();\n    // Initialize the coordinates using the client offset\n    manager.dispatch(setClientOffset(clientOffset));\n    verifyInvariants(sourceIds, monitor, registry);\n    // Get the draggable source\n    const sourceId = getDraggableSource(sourceIds, monitor);\n    if (sourceId == null) {\n      manager.dispatch(ResetCoordinatesAction);\n      return;\n    }\n    // Get the source client offset\n    let sourceClientOffset = null;\n    if (clientOffset) {\n      if (!getSourceClientOffset) {\n        throw new Error('getSourceClientOffset must be defined');\n      }\n      verifyGetSourceClientOffsetIsFunction(getSourceClientOffset);\n      sourceClientOffset = getSourceClientOffset(sourceId);\n    }\n    // Initialize the full coordinates\n    manager.dispatch(setClientOffset(clientOffset, sourceClientOffset));\n    const source = registry.getSource(sourceId);\n    const item = source.beginDrag(monitor, sourceId);\n    // If source.beginDrag returns null, this is an indicator to cancel the drag\n    if (item == null) {\n      return undefined;\n    }\n    verifyItemIsObject(item);\n    registry.pinSource(sourceId);\n    const itemType = registry.getSourceType(sourceId);\n    return {\n      type: BEGIN_DRAG,\n      payload: {\n        itemType,\n        item,\n        sourceId,\n        clientOffset: clientOffset || null,\n        sourceClientOffset: sourceClientOffset || null,\n        isSourcePublic: !!publishSource\n      }\n    };\n  };\n}\nfunction verifyInvariants(sourceIds, monitor, registry) {\n  invariant(!monitor.isDragging(), 'Cannot call beginDrag while dragging.');\n  sourceIds.forEach(function (sourceId) {\n    invariant(registry.getSource(sourceId), 'Expected sourceIds to be registered.');\n  });\n}\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset) {\n  invariant(typeof getSourceClientOffset === 'function', 'When clientOffset is provided, getSourceClientOffset must be a function.');\n}\nfunction verifyItemIsObject(item) {\n  invariant(isObject(item), 'Item must be an object.');\n}\nfunction getDraggableSource(sourceIds, monitor) {\n  let sourceId = null;\n  for (let i = sourceIds.length - 1; i >= 0; i--) {\n    if (monitor.canDragSource(sourceIds[i])) {\n      sourceId = sourceIds[i];\n      break;\n    }\n  }\n  return sourceId;\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAYhD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,UAAU,EAAEC,WAAW,QAAQ,YAAY;AAEpD,MAAMC,sBAAsB,GAAG;EAC9BC,IAAI,EAAEF,WAAW;EACjBG,OAAO,EAAE;IACRC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE;;CAErB;AAED,OAAO,SAASC,eAAe,CAACC,OAAwB,EAAE;EACzD,OAAO,SAASC,SAAS,GAKe;IAAA,IAJvCC,SAAuB,uEAAG,EAAE;IAAA,IAC5BC,OAAyB,uEAAG;MAC3BC,aAAa,EAAE;KACf;IAED,MAAM;MACLA,aAAa,GAAG,IAAI;MACpBP,YAAY;MACZQ;IAAqB,CACrB,GAAqBF,OAAO;IAC7B,MAAMG,OAAO,GAAGN,OAAO,CAACO,UAAU,EAAE;IACpC,MAAMC,QAAQ,GAAGR,OAAO,CAACS,WAAW,EAAE;IAEtC;IACAT,OAAO,CAACU,QAAQ,CAACnB,eAAe,CAACM,YAAY,CAAC,CAAC;IAE/Cc,gBAAgB,CAACT,SAAS,EAAEI,OAAO,EAAEE,QAAQ,CAAC;IAE9C;IACA,MAAMI,QAAQ,GAAGC,kBAAkB,CAACX,SAAS,EAAEI,OAAO,CAAC;IACvD,IAAIM,QAAQ,IAAI,IAAI,EAAE;MACrBZ,OAAO,CAACU,QAAQ,CAAChB,sBAAsB,CAAC;MACxC;;IAGD;IACA,IAAII,kBAAkB,GAAmB,IAAI;IAC7C,IAAID,YAAY,EAAE;MACjB,IAAI,CAACQ,qBAAqB,EAAE;QAC3B,MAAM,IAAIS,KAAK,CAAC,uCAAuC,CAAC;;MAEzDC,qCAAqC,CAACV,qBAAqB,CAAC;MAC5DP,kBAAkB,GAAGO,qBAAqB,CAACO,QAAQ,CAAC;;IAGrD;IACAZ,OAAO,CAACU,QAAQ,CAACnB,eAAe,CAACM,YAAY,EAAEC,kBAAkB,CAAC,CAAC;IAEnE,MAAMkB,MAAM,GAAGR,QAAQ,CAACS,SAAS,CAACL,QAAQ,CAAC;IAC3C,MAAMM,IAAI,GAAGF,MAAM,CAACf,SAAS,CAACK,OAAO,EAAEM,QAAQ,CAAC;IAChD;IACA,IAAIM,IAAI,IAAI,IAAI,EAAE;MACjB,OAAOC,SAAS;;IAEjBC,kBAAkB,CAACF,IAAI,CAAC;IACxBV,QAAQ,CAACa,SAAS,CAACT,QAAQ,CAAC;IAE5B,MAAMU,QAAQ,GAAGd,QAAQ,CAACe,aAAa,CAACX,QAAQ,CAAC;IACjD,OAAO;MACNjB,IAAI,EAAEH,UAAU;MAChBI,OAAO,EAAE;QACR0B,QAAQ;QACRJ,IAAI;QACJN,QAAQ;QACRf,YAAY,EAAEA,YAAY,IAAI,IAAI;QAClCC,kBAAkB,EAAEA,kBAAkB,IAAI,IAAI;QAC9C0B,cAAc,EAAE,CAAC,CAACpB;;KAEnB;GACD;;AAGF,SAASO,gBAAgB,CACxBT,SAAuB,EACvBI,OAAwB,EACxBE,QAAyB,EACxB;EACDnB,SAAS,CAAC,CAACiB,OAAO,CAACmB,UAAU,EAAE,EAAE,uCAAuC,CAAC;EACzEvB,SAAS,CAACwB,OAAO,CAAC,UAAUd,QAAQ,EAAE;IACrCvB,SAAS,CACRmB,QAAQ,CAACS,SAAS,CAACL,QAAQ,CAAC,EAC5B,sCAAsC,CACtC;GACD,CAAC;;AAGH,SAASG,qCAAqC,CAACV,qBAA0B,EAAE;EAC1EhB,SAAS,CACR,OAAOgB,qBAAqB,KAAK,UAAU,EAC3C,0EAA0E,CAC1E;;AAGF,SAASe,kBAAkB,CAACF,IAAS,EAAE;EACtC7B,SAAS,CAACC,QAAQ,CAAC4B,IAAI,CAAC,EAAE,yBAAyB,CAAC;;AAGrD,SAASL,kBAAkB,CAACX,SAAuB,EAAEI,OAAwB,EAAE;EAC9E,IAAIM,QAAQ,GAAG,IAAI;EACnB,KAAK,IAAIe,CAAC,GAAGzB,SAAS,CAAC0B,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/C,IAAIrB,OAAO,CAACuB,aAAa,CAAC3B,SAAS,CAACyB,CAAC,CAAC,CAAC,EAAE;MACxCf,QAAQ,GAAGV,SAAS,CAACyB,CAAC,CAAC;MACvB;;;EAGF,OAAOf,QAAQ", "names": ["invariant", "isObject", "setClientOffset", "BEGIN_DRAG", "INIT_COORDS", "ResetCoordinatesAction", "type", "payload", "clientOffset", "sourceClientOffset", "createBeginDrag", "manager", "beginDrag", "sourceIds", "options", "publishSource", "getSourceClientOffset", "monitor", "getMonitor", "registry", "getRegistry", "dispatch", "verifyInvariants", "sourceId", "getDraggableSource", "Error", "verifyGetSourceClientOffsetIsFunction", "source", "getSource", "item", "undefined", "verifyItemIsObject", "pinSource", "itemType", "getSourceType", "isSourcePublic", "isDragging", "for<PERSON>ach", "i", "length", "canDragSource"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\actions\\dragDrop\\beginDrag.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tBeginDragOptions,\n\tBeginDragPayload,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tXYCoord,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { setClientOffset } from './local/setClientOffset.js'\nimport { BEGIN_DRAG, INIT_COORDS } from './types.js'\n\nconst ResetCoordinatesAction = {\n\ttype: INIT_COORDS,\n\tpayload: {\n\t\tclientOffset: null,\n\t\tsourceClientOffset: null,\n\t},\n}\n\nexport function createBeginDrag(manager: DragDropManager) {\n\treturn function beginDrag(\n\t\tsourceIds: Identifier[] = [],\n\t\toptions: BeginDragOptions = {\n\t\t\tpublishSource: true,\n\t\t},\n\t): Action<BeginDragPayload> | undefined {\n\t\tconst {\n\t\t\tpublishSource = true,\n\t\t\tclientOffset,\n\t\t\tgetSourceClientOffset,\n\t\t}: BeginDragOptions = options\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\n\t\t// Initialize the coordinates using the client offset\n\t\tmanager.dispatch(setClientOffset(clientOffset))\n\n\t\tverifyInvariants(sourceIds, monitor, registry)\n\n\t\t// Get the draggable source\n\t\tconst sourceId = getDraggableSource(sourceIds, monitor)\n\t\tif (sourceId == null) {\n\t\t\tmanager.dispatch(ResetCoordinatesAction)\n\t\t\treturn\n\t\t}\n\n\t\t// Get the source client offset\n\t\tlet sourceClientOffset: XYCoord | null = null\n\t\tif (clientOffset) {\n\t\t\tif (!getSourceClientOffset) {\n\t\t\t\tthrow new Error('getSourceClientOffset must be defined')\n\t\t\t}\n\t\t\tverifyGetSourceClientOffsetIsFunction(getSourceClientOffset)\n\t\t\tsourceClientOffset = getSourceClientOffset(sourceId)\n\t\t}\n\n\t\t// Initialize the full coordinates\n\t\tmanager.dispatch(setClientOffset(clientOffset, sourceClientOffset))\n\n\t\tconst source = registry.getSource(sourceId)\n\t\tconst item = source.beginDrag(monitor, sourceId)\n\t\t// If source.beginDrag returns null, this is an indicator to cancel the drag\n\t\tif (item == null) {\n\t\t\treturn undefined\n\t\t}\n\t\tverifyItemIsObject(item)\n\t\tregistry.pinSource(sourceId)\n\n\t\tconst itemType = registry.getSourceType(sourceId)\n\t\treturn {\n\t\t\ttype: BEGIN_DRAG,\n\t\t\tpayload: {\n\t\t\t\titemType,\n\t\t\t\titem,\n\t\t\t\tsourceId,\n\t\t\t\tclientOffset: clientOffset || null,\n\t\t\t\tsourceClientOffset: sourceClientOffset || null,\n\t\t\t\tisSourcePublic: !!publishSource,\n\t\t\t},\n\t\t}\n\t}\n}\n\nfunction verifyInvariants(\n\tsourceIds: Identifier[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\tinvariant(!monitor.isDragging(), 'Cannot call beginDrag while dragging.')\n\tsourceIds.forEach(function (sourceId) {\n\t\tinvariant(\n\t\t\tregistry.getSource(sourceId),\n\t\t\t'Expected sourceIds to be registered.',\n\t\t)\n\t})\n}\n\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset: any) {\n\tinvariant(\n\t\ttypeof getSourceClientOffset === 'function',\n\t\t'When clientOffset is provided, getSourceClientOffset must be a function.',\n\t)\n}\n\nfunction verifyItemIsObject(item: any) {\n\tinvariant(isObject(item), 'Item must be an object.')\n}\n\nfunction getDraggableSource(sourceIds: Identifier[], monitor: DragDropMonitor) {\n\tlet sourceId = null\n\tfor (let i = sourceIds.length - 1; i >= 0; i--) {\n\t\tif (monitor.canDragSource(sourceIds[i])) {\n\t\t\tsourceId = sourceIds[i]\n\t\t\tbreak\n\t\t}\n\t}\n\treturn sourceId\n}\n"]}, "metadata": {}, "sourceType": "module"}