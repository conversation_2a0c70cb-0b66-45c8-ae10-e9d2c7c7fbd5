{"ast": null, "code": "import { union, without } from './utils/js_utils.js';\nexport class EnterLeaveCounter {\n  enter(enteringNode) {\n    const previousLength = this.entered.length;\n    const isNodeEntered = node => this.isNodeInDocument(node) && (!node.contains || node.contains(enteringNode));\n    this.entered = union(this.entered.filter(isNodeEntered), [enteringNode]);\n    return previousLength === 0 && this.entered.length > 0;\n  }\n  leave(leavingNode) {\n    const previousLength = this.entered.length;\n    this.entered = without(this.entered.filter(this.isNodeInDocument), leavingNode);\n    return previousLength > 0 && this.entered.length === 0;\n  }\n  reset() {\n    this.entered = [];\n  }\n  constructor(isNodeInDocument) {\n    this.entered = [];\n    this.isNodeInDocument = isNodeInDocument;\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AAIpD,OAAO,MAAMC,iBAAiB;EAQ7BC,KAAY,CAACC,YAAgC,EAAW;IACvD,MAAMC,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,MAAM;IAE1C,MAAMC,aAAa,GAAIC,IAAU,IAChC,IAAI,CAACC,gBAAgB,CAACD,IAAI,CAAC,KAC1B,CAACA,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACP,YAAY,CAAS,CAAC;IAExD,IAAI,CAACE,OAAO,GAAGN,KAAK,CAAC,IAAI,CAACM,OAAO,CAACM,MAAM,CAACJ,aAAa,CAAC,EAAE,CAACJ,YAAY,CAAC,CAAC;IAExE,OAAOC,cAAc,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC;;EAGvDM,KAAY,CAACC,WAA+B,EAAW;IACtD,MAAMT,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,MAAM;IAE1C,IAAI,CAACD,OAAO,GAAGL,OAAO,CACrB,IAAI,CAACK,OAAO,CAACM,MAAM,CAAC,IAAI,CAACF,gBAAgB,CAAC,EAC1CI,WAAW,CACX;IAED,OAAOT,cAAc,GAAG,CAAC,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC;;EAGvDQ,KAAY,GAAS;IACpB,IAAI,CAACT,OAAO,GAAG,EAAE;;EA5BlBU,YAAmBN,gBAA+B,EAAE;IAHpD,KAAQJ,OAAO,GAAU,EAAE;IAI1B,IAAI,CAACI,gBAAgB,GAAGA,gBAAgB", "names": ["union", "without", "EnterLeave<PERSON><PERSON>nter", "enter", "enteringNode", "<PERSON><PERSON><PERSON><PERSON>", "entered", "length", "isNodeEntered", "node", "isNodeInDocument", "contains", "filter", "leave", "leavingNode", "reset", "constructor"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd-html5-backend\\src\\EnterLeaveCounter.ts"], "sourcesContent": ["import { union, without } from './utils/js_utils.js'\n\ntype NodePredicate = (node: Node | null | undefined) => boolean\n\nexport class EnterLeaveCounter {\n\tprivate entered: any[] = []\n\tprivate isNodeInDocument: NodePredicate\n\n\tpublic constructor(isNodeInDocument: NodePredicate) {\n\t\tthis.isNodeInDocument = isNodeInDocument\n\t}\n\n\tpublic enter(enteringNode: EventTarget | null): boolean {\n\t\tconst previousLength = this.entered.length\n\n\t\tconst isNodeEntered = (node: Node): boolean =>\n\t\t\tthis.isNodeInDocument(node) &&\n\t\t\t(!node.contains || node.contains(enteringNode as Node))\n\n\t\tthis.entered = union(this.entered.filter(isNodeEntered), [enteringNode])\n\n\t\treturn previousLength === 0 && this.entered.length > 0\n\t}\n\n\tpublic leave(leavingNode: EventTarget | null): boolean {\n\t\tconst previousLength = this.entered.length\n\n\t\tthis.entered = without(\n\t\t\tthis.entered.filter(this.isNodeInDocument),\n\t\t\tleavingNode,\n\t\t)\n\n\t\treturn previousLength > 0 && this.entered.length === 0\n\t}\n\n\tpublic reset(): void {\n\t\tthis.entered = []\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}