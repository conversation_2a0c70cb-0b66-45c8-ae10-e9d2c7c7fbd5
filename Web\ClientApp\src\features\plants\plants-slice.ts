import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { Plant } from 'api/models/plants';
import { plantApi } from 'api/plant-service';
import { RootState } from 'app/store';
import { sortBy, sortSizeName } from 'utils/sort';
import { ProblemDetails } from 'utils/problem-details';

export interface PlantsState {
  plants: Plant[];
}

const initialState: PlantsState = {
  plants: []
};

export const savePlants = createAsyncThunk<Plant[], Plant[], { state: RootState }>(
  'plants/save-plants',
  async (plants, { rejectWithValue }) => {
    try {
      const updatedPlants = await plantApi.saveAll(plants);
      return updatedPlants;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const generateAndSaveSortOrder = createAsyncThunk<Plant[], void, { state: RootState }>(
  'plants/generate-and-save-sort-order',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const plants = state.plants.plants;

      // Check if all plants have null stickingSortOrder
      const allHaveNullSortOrder = plants.every(p => p.stickingSortOrder == null);

      if (allHaveNullSortOrder) {
        // Sort plants by the default sort order and assign stickingSortOrder
        const sortedPlants = [...plants].sort(sortPlant);
        const plantsWithSortOrder = sortedPlants.map((plant, index) => ({
          ...plant,
          stickingSortOrder: index
        }));

        // Save all plants with their new sort order
        const updatedPlants = await plantApi.saveAll(plantsWithSortOrder);
        return updatedPlants;
      }

      // If not all have null sort order, just return the current plants
      return plants;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const plantsSlice = createSlice({
  name: 'plants',
  initialState,
  reducers: {
    setPlants(state, action: PayloadAction<Plant[]>) {
      state.plants = action.payload;
    },
    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {
      const { movingItem, existingItem } = action.payload;

      console.log("moving item", existingItem, movingItem);

      if (existingItem.stickingSortOrder == null) {
        // generate a new order for all items based on current index
        state.plants = state.plants.sort(sortPlant).map((p, index) => ({ ...p, stickingSortOrder: index }));
      }

      const existingItemIndex = state.plants.findIndex(p => p._id === existingItem._id);

      // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1
      state.plants = state.plants.map(p => {
        if (p._id === existingItem._id) {
          return { ...p, stickingSortOrder: existingItemIndex + 1 };
        } else if (p._id === movingItem._id) {
          return { ...p, stickingSortOrder: existingItemIndex };
        } else if (p.stickingSortOrder != null && p.stickingSortOrder > 1) {
          return { ...p, stickingSortOrder: p.stickingSortOrder + 1 };
        }
        return p;
      });
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(savePlants.fulfilled, (state, action) => {
        // Update the plants in state with the saved versions (which have updated _rev values)
        const savedPlants = action.payload;
        state.plants = state.plants.map(plant => {
          const savedPlant = savedPlants.find(sp => sp._id === plant._id);
          return savedPlant || plant;
        });
      })
      .addCase(generateAndSaveSortOrder.fulfilled, (state, action) => {
        // Replace all plants with the updated versions that have sort orders
        state.plants = action.payload;
      });
  }
});

export const { setPlants, moveItem } = plantsSlice.actions;

export const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);

export default plantsSlice.reducer;

const sortByCrop = sortBy('crop');

function sortByStickingSortOrder(a: Plant, b: Plant) {
  // Handle null values - place them last
  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;
  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)
  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1;  // b comes before a (non-null before null)

  // Both are non-null, compare normally
  return a.stickingSortOrder! - b.stickingSortOrder!;
}

function sortPlant(a: Plant, b: Plant) {
  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);
}