import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Plant } from 'api/models/plants';
import { RootState } from 'app/store';
import { sortBy, sortSizeName } from 'utils/sort';

export interface PlantsState {
  plants: Plant[];
}

const initialState: PlantsState = {
  plants: []
};

export const plantsSlice = createSlice({
  name: 'plants',
  initialState,
  reducers: {
    setPlants(state, action: PayloadAction<Plant[]>) {
      state.plants = action.payload;
    },
    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {
      const { movingItem, existingItem } = action.payload;

      console.log("moving item", existingItem, movingItem);

      if (existingItem.stickingSortOrder == null) {
        // generate a new order for all items based on current index
        state.plants = state.plants.sort(sortPlant).map((p, index) => ({ ...p, stickingSortOrder: index }));
      }

      const existingItemIndex = state.plants.findIndex(p => p._id === existingItem._id);

      // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1
      state.plants = state.plants.map(p => {
        if (p._id === existingItem._id) {
          return { ...p, stickingSortOrder: existingItemIndex + 1 };
        } else if (p._id === movingItem._id) {
          return { ...p, stickingSortOrder: existingItemIndex };
        } else if (p.stickingSortOrder != null && p.stickingSortOrder > 1) {
          return { ...p, stickingSortOrder: p.stickingSortOrder + 1 };
        }
        return p;
      });
    }
  }
});

export const { setPlants, moveItem } = plantsSlice.actions;

export const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);

export default plantsSlice.reducer;

const sortByCrop = sortBy('crop');

function sortByStickingSortOrder(a: Plant, b: Plant) {
  // Handle null values - place them last
  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;
  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)
  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1;  // b comes before a (non-null before null)

  // Both are non-null, compare normally
  return a.stickingSortOrder! - b.stickingSortOrder!;
}

function sortPlant(a: Plant, b: Plant) {
  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);
}