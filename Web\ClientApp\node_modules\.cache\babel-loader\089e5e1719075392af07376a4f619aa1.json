{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    },\n    moveItem(state, action) {\n      const {\n        movingItem,\n        existingItem\n      } = action.payload;\n      if (existingItem.stickingSortOrder == null) {\n        existingItem.stickingSortOrder = 0;\n      } else {\n        const existingIndex = state.plants.findIndex(p => p._id === existingItem._id),\n          movingIndex = state.plants.findIndex(p => p._id === movingItem._id),\n          plants = state.plants.map(p => ({\n            ...p\n          }));\n        plants.splice(existingIndex, 1);\n        plants.splice(movingIndex, 0, existingItem);\n        state.plants = plants;\n      }\n    }\n  }\n});\nexport const {\n  setPlants\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nfunction sortPlant(a, b) {\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "sortBy", "sortSizeName", "initialState", "plants", "plantsSlice", "name", "reducers", "setPlants", "state", "action", "payload", "moveItem", "movingItem", "existingItem", "stickingSortOrder", "existingIndex", "findIndex", "p", "_id", "movingIndex", "map", "splice", "actions", "selectPlants", "sort", "sortPlant", "reducer", "sortByCrop", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    },\r\n    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {\r\n      const { movingItem, existingItem } = action.payload;\r\n      if (existingItem.stickingSortOrder == null) {\r\n        existingItem.stickingSortOrder = 0;\r\n      } else {\r\n        const existingIndex = state.plants.findIndex(p => p._id === existingItem._id),\r\n          movingIndex = state.plants.findIndex(p => p._id === movingItem._id),\r\n          plants = state.plants.map(p => ({...p}));\r\n        plants.splice(existingIndex, 1);\r\n        plants.splice(movingIndex, 0, existingItem);\r\n        state.plants = plants;\r\n      }\r\n    }\r\n  }\r\n});\r\n\r\nexport const { setPlants } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAMjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGL,WAAW,CAAC;EACrCM,IAAI,EAAE,QAAQ;EACdH,YAAY;EACZI,QAAQ,EAAE;IACRC,SAAS,CAACC,KAAK,EAAEC,MAA8B,EAAE;MAC/CD,KAAK,CAACL,MAAM,GAAGM,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDC,QAAQ,CAACH,KAAK,EAAEC,MAAiE,EAAE;MACjF,MAAM;QAAEG,UAAU;QAAEC;MAAa,CAAC,GAAGJ,MAAM,CAACC,OAAO;MACnD,IAAIG,YAAY,CAACC,iBAAiB,IAAI,IAAI,EAAE;QAC1CD,YAAY,CAACC,iBAAiB,GAAG,CAAC;MACpC,CAAC,MAAM;QACL,MAAMC,aAAa,GAAGP,KAAK,CAACL,MAAM,CAACa,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKL,YAAY,CAACK,GAAG,CAAC;UAC3EC,WAAW,GAAGX,KAAK,CAACL,MAAM,CAACa,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKN,UAAU,CAACM,GAAG,CAAC;UACnEf,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACiB,GAAG,CAACH,CAAC,KAAK;YAAC,GAAGA;UAAC,CAAC,CAAC,CAAC;QAC1Cd,MAAM,CAACkB,MAAM,CAACN,aAAa,EAAE,CAAC,CAAC;QAC/BZ,MAAM,CAACkB,MAAM,CAACF,WAAW,EAAE,CAAC,EAAEN,YAAY,CAAC;QAC3CL,KAAK,CAACL,MAAM,GAAGA,MAAM;MACvB;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEI;AAAU,CAAC,GAAGH,WAAW,CAACkB,OAAO;AAEhD,OAAO,MAAMC,YAAY,GAAIf,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAACA,MAAM,CAACiB,GAAG,CAACH,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACO,IAAI,CAACC,SAAS,CAAC;AAExG,eAAerB,WAAW,CAACsB,OAAO;AAElC,MAAMC,UAAU,GAAG3B,MAAM,CAAC,MAAM,CAAC;AAEjC,SAASyB,SAAS,CAACG,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,UAAU,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAI5B,YAAY,CAAC2B,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AACzD"}, "metadata": {}, "sourceType": "module"}