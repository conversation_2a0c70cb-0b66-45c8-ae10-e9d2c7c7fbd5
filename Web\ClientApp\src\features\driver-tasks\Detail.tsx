import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { Button, Input } from 'reactstrap';
import moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { driverTasksApi } from 'api/driver-tasks-service';
import * as models from 'api/models/driver-tasks';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { Error } from 'features/error/Error';
import { ProblemDetails } from 'utils/problem-details';
import { getDrivers, selectDrivers } from './driver-task-slice';

export function Detail() {
  const { isInRole, user } = useAuth(),
    dispatch = useDispatch(),
    navigate = useNavigate(),
    { id } = useParams<{ id: string }>(),
    drivers = useSelector(selectDrivers),
    [task, setTask] = useState<models.DriverTask | null>(null),
    [dueDate, setDueDate] = useState(moment().format('YYYY-MM-DD')),
    [assignedTo, setAssignedTo] = useState<models.Driver | null>(null),
    [priority, setPriority] = useState<models.Priority>('Normal'),
    [notes, setNotes] = useState(''),
    [status, setStatus] = useState<models.Status>('Not Started'),
    [fromLocation, setFromLocation] = useState(''),
    [toLocation, setToLocation] = useState(''),
    [error, setError] = useState<ProblemDetails | null>(null),
    canCreateDriverTasks = isInRole('create:driver-tasks'),
    isDriver = user && user.name === assignedTo?.name;

  useEffect(() => {
    if (user) {
      dispatch(getDrivers(user));
    }
  }, [dispatch, user]);

  useEffect(() => {
    if (id) {
      driverTasksApi
        .getOneDriverTask(id)
        .then((task) => {
          setTask(task);
          setDueDate(task.dueDate);
          setAssignedTo(task.assignedTo);
          setPriority(task.priority);
          setNotes(task.notes);
          setStatus(task.status);
          setFromLocation(task.fromLocation);
          setToLocation(task.toLocation);
        })
        .catch((e) => setError(e as ProblemDetails));
    }

    return function cleanup() {
      setTask(null);
    };
  }, [id]);

  const handleGoBackClick = () => goBack();

  const handleSaveClick = async () => {
    if (user && task) {
      try {
        const updated = {
          ...task,
          user,
          dueDate,
          assignedTo,
          priority,
          notes,
          status,
          fromLocation,
          toLocation,
        };
        await driverTasksApi.updateDriverTask(updated);

        goBack();
      } catch (e) {
        setError(e as ProblemDetails);
      }
    }
  };

  const handleDueDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDueDate(e.target.value);
  };

  const handleAssignedToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const driver = drivers.find((d) => d.name === e.target.value) || null;
    setAssignedTo(driver);
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNotes(e.target.value);
  };

  const handlePriorityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPriority(e.target.value as models.Priority);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStatus(e.target.value as models.Status);
  };

  const handleFromLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFromLocation(e.target.value);
  };

  const handleToLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setToLocation(e.target.value);
  };

  const handleClearErrorClick = () => {
    setError(null);
  };

  const goBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate(routes.driverTasks.list.path);
    }
  };

  return (
    <div className="container-fluid h-100-vh d-flex flex-column">
      <div className="bg-white sticky-top-navbar">
        <div className="container mx-auto row mt-2 py-2 border-bottom shadow">
          <div className="col-12 row my-2">
            <h1 className="col">
              <FontAwesomeIcon icon={['fat', 'truck-fast']} />
              &nbsp; Edit Driver Task
            </h1>
          </div>
          <div className="col-12 row"></div>
        </div>
      </div>
      <div className="container flex-grow-1">
        <div className="row p-2">
          <div className="col-12 col-md-3">
            <label htmlFor="due-date">
              Due Date&nbsp;<span className="text-danger">*</span>
            </label>
            <Input
              id="due-date"
              type="date"
              value={dueDate}
              onChange={handleDueDateChange}
              disabled={!canCreateDriverTasks}
            />
          </div>
          <div className="col-12 col-md-3">
            <label htmlFor="assigned-to">Assigned To</label>
            <Input
              id="assigned-to"
              type="select"
              value={assignedTo?.name || ''}
              onChange={handleAssignedToChange}
              disabled={
                !canCreateDriverTasks && !isDriver && !!task?.assignedTo
              }>
              <option value="">Unassigned</option>
              {canCreateDriverTasks &&
                drivers.map((driver) => (
                  <option key={driver.name} className="text-capitalize">
                    {driver.name}
                  </option>
                ))}
              {!canCreateDriverTasks && user && (
                <option value={user.name} className="text-capitalize">
                  {user.name}
                </option>
              )}
            </Input>
          </div>
          <div className="col-12 col-md-6">
            <label htmlFor="notes">
              Task&nbsp;<span className="text-danger">*</span>
            </label>
            <Input
              id="notes"
              type="textarea"
              rows={3}
              value={notes}
              onChange={handleNotesChange}
              disabled={!canCreateDriverTasks}
            />
          </div>
          <div className="col-12 col-md-3">
            <label htmlFor="from-location">
              From Location&nbsp;<span className="text-danger">*</span>
            </label>
            <Input
              id="from-location"
              value={fromLocation}
              onChange={handleFromLocationChange}
              disabled={!canCreateDriverTasks}
            />
          </div>
          <div className="col-12 col-md-3">
            <label htmlFor="to-location">
              To Location&nbsp;<span className="text-danger">*</span>
            </label>
            <Input
              id="to-location"
              value={toLocation}
              onChange={handleToLocationChange}
              disabled={!canCreateDriverTasks}
            />
          </div>
          <div className="col-12 col-md-3">
            <label htmlFor="priority">Priority</label>
            <Input
              id="priority"
              type="select"
              value={priority}
              onChange={handlePriorityChange}
              disabled={!canCreateDriverTasks}>
              <option value={models.HighPriority}>{models.HighPriority}</option>
              <option value={models.NormalPriority}>
                {models.NormalPriority}
              </option>
              <option value={models.LowPriority}>{models.LowPriority}</option>
            </Input>
          </div>
          <div className="col-12 col-md-3">
            <label htmlFor="status">Status</label>
            <Input
              id="status"
              type="select"
              value={status}
              onChange={handleStatusChange}
              disabled={!canCreateDriverTasks && !isDriver}>
              <option value={models.NotStartedStatus}>
                {models.NotStartedStatus}
              </option>
              <option value={models.InProgressStatus}>
                {models.InProgressStatus}
              </option>
              <option value={models.CompleteStatus}>
                {models.CompleteStatus}
              </option>
            </Input>
          </div>
        </div>
      </div>
      <div className="row container mx-auto mt-5 bg-white sticky-bottom border-top py-2">
        <div className="col-0 col-md">&nbsp;</div>
        {(canCreateDriverTasks || isDriver) && (
          <>
            <div className="col-6 col-md-auto text-end">
              <Button
                outline
                size="lg"
                onClick={handleGoBackClick}
                className="d-block d-md-inline-block w-100">
                Cancel
              </Button>
            </div>
            <div className="col-6 col-md-auto">
              <Button
                onClick={handleSaveClick}
                color="success"
                size="lg"
                className="d-block d-md-inline-block w-100">
                <FontAwesomeIcon icon={['fat', 'save']} />
                &nbsp; Save
              </Button>
            </div>
          </>
        )}
        {!canCreateDriverTasks && !isDriver && (
          <div className="col-12 col-md-auto">
            <Button
              outline
              size="lg"
              onClick={handleGoBackClick}
              className="d-block d-md-inline-block w-100">
              Close
            </Button>
          </div>
        )}
        <Error error={error} clearError={handleClearErrorClick} />
      </div>
    </div>
  );
}

export default Detail;
