export type Roles =
  | 'view:orders'
  | 'create:orders'
  | 'update:orders'
  | 'delete:orders'
  | 'notify:orders'
  | 'view:plants'
  | 'create:plants'
  | 'update:plants'
  | 'delete:plants'
  | 'view:zones'
  | 'create:zones'
  | 'update:zones'
  | 'delete:zones'
  | 'view:customers'
  | 'create:customers'
  | 'update:customers'
  | 'delete:customers'
  | 'view:colours'
  | 'create:colours'
  | 'update:colours'
  | 'delete:colours'
  | 'admin:users'
  | 'view:driver-tasks'
  | 'create:driver-tasks'
  | 'driver';

export interface UserInfo {
  name: string;
  password: string;
  roles: string[];
  email: string | null;
  phone: string | null;
}

export interface CouchSessionDoc {
  ok: boolean;
  name: string;
  roles: string[];
}

export interface CouchUserDoc {
  _id: string;
  _rev: string;
  name: string;
  orgs: string[];
  roles: string[];
  type: string;
  password_sha: string;
  salt: string;
  email?: string;
  phone?: string;
}

export type UserDoc = PouchDB.Core.ExistingDocument<
  CouchUserDoc & PouchDB.Core.AllDocsMeta
>;
