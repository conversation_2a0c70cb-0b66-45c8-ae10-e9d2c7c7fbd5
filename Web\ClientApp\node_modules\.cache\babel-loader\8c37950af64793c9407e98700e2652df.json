{"ast": null, "code": "export class DragSourceImpl {\n  beginDrag() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    let result = null;\n    if (typeof spec.item === 'object') {\n      result = spec.item;\n    } else if (typeof spec.item === 'function') {\n      result = spec.item(monitor);\n    } else {\n      result = {};\n    }\n    return result !== null && result !== void 0 ? result : null;\n  }\n  canDrag() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    if (typeof spec.canDrag === 'boolean') {\n      return spec.canDrag;\n    } else if (typeof spec.canDrag === 'function') {\n      return spec.canDrag(monitor);\n    } else {\n      return true;\n    }\n  }\n  isDragging(globalMonitor, target) {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    const {\n      isDragging\n    } = spec;\n    return isDragging ? isDragging(monitor) : target === globalMonitor.getSourceId();\n  }\n  endDrag() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    const connector = this.connector;\n    const {\n      end\n    } = spec;\n    if (end) {\n      end(monitor.getItem(), monitor);\n    }\n    connector.reconnect();\n  }\n  constructor(spec, monitor, connector) {\n    this.spec = spec;\n    this.monitor = monitor;\n    this.connector = connector;\n  }\n}", "map": {"version": 3, "mappings": "AAMA,OAAO,MAAMA,cAAc;EAO1BC,SAAgB,GAAG;IAClB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,IAAIC,MAAM,GAAa,IAAI;IAC3B,IAAI,OAAOF,IAAI,CAACG,IAAI,KAAK,QAAQ,EAAE;MAClCD,MAAM,GAAGF,IAAI,CAACG,IAAI;KAClB,MAAM,IAAI,OAAOH,IAAI,CAACG,IAAI,KAAK,UAAU,EAAE;MAC3CD,MAAM,GAAGF,IAAK,CAACG,IAAI,CAA0BF,OAAO,CAAC;KACrD,MAAM;MACNC,MAAM,GAAG,EAAE;;IAEZ,OAAOA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,IAAI;;EAGtBE,OAAc,GAAG;IAChB,MAAMJ,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,OAAOD,IAAI,CAACI,OAAO,KAAK,SAAS,EAAE;MACtC,OAAOJ,IAAI,CAACI,OAAO;KACnB,MAAM,IAAI,OAAOJ,IAAI,CAACI,OAAO,KAAK,UAAU,EAAE;MAC9C,OAAOJ,IAAI,CAACI,OAAO,CAACH,OAAO,CAAC;KAC5B,MAAM;MACN,OAAO,IAAI;;;EAIbI,UAAiB,CAACC,aAA8B,EAAEC,MAAkB,EAAE;IACrE,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAM;MAAEI;IAAU,CAAE,GAAGL,IAAI;IAC3B,OAAOK,UAAU,GACdA,UAAU,CAACJ,OAAO,CAAC,GACnBM,MAAM,KAAKD,aAAa,CAACE,WAAW,EAAE;;EAG1CC,OAAc,GAAG;IAChB,MAAMT,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMS,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAM;MAAEC;IAAG,CAAE,GAAGX,IAAI;IACpB,IAAIW,GAAG,EAAE;MACRA,GAAG,CAACV,OAAO,CAACW,OAAO,EAAE,EAAEX,OAAO,CAAC;;IAEhCS,SAAS,CAACG,SAAS,EAAE;;EAlDtBC,YACQd,IAAiC,EAChCC,OAAgC,EAChCS,SAAoB,EAC3B;SAHMV,IAAiC,GAAjCA,IAAiC;SAChCC,OAAgC,GAAhCA,OAAgC;SAChCS,SAAoB,GAApBA,SAAoB", "names": ["DragSourceImpl", "beginDrag", "spec", "monitor", "result", "item", "canDrag", "isDragging", "globalMonitor", "target", "getSourceId", "endDrag", "connector", "end", "getItem", "reconnect", "constructor"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrag\\DragSourceImpl.ts"], "sourcesContent": ["import type { DragDropMonitor, DragSource, Identifier } from 'dnd-core'\n\nimport type { Connector } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragObjectFactory, DragSourceHookSpec } from '../types.js'\n\nexport class DragSourceImpl<O, R, P> implements DragSource {\n\tpublic constructor(\n\t\tpublic spec: DragSourceHookSpec<O, R, P>,\n\t\tprivate monitor: DragSourceMonitor<O, R>,\n\t\tprivate connector: Connector,\n\t) {}\n\n\tpublic beginDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\n\t\tlet result: O | null = null\n\t\tif (typeof spec.item === 'object') {\n\t\t\tresult = spec.item as O\n\t\t} else if (typeof spec.item === 'function') {\n\t\t\tresult = (spec.item as DragObjectFactory<O>)(monitor)\n\t\t} else {\n\t\t\tresult = {} as O\n\t\t}\n\t\treturn result ?? null\n\t}\n\n\tpublic canDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (typeof spec.canDrag === 'boolean') {\n\t\t\treturn spec.canDrag\n\t\t} else if (typeof spec.canDrag === 'function') {\n\t\t\treturn spec.canDrag(monitor)\n\t\t} else {\n\t\t\treturn true\n\t\t}\n\t}\n\n\tpublic isDragging(globalMonitor: DragDropMonitor, target: Identifier) {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tconst { isDragging } = spec\n\t\treturn isDragging\n\t\t\t? isDragging(monitor)\n\t\t\t: target === globalMonitor.getSourceId()\n\t}\n\n\tpublic endDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tconst connector = this.connector\n\t\tconst { end } = spec\n\t\tif (end) {\n\t\t\tend(monitor.getItem(), monitor)\n\t\t}\n\t\tconnector.reconnect()\n\t}\n}\n"]}, "metadata": {}, "sourceType": "module"}