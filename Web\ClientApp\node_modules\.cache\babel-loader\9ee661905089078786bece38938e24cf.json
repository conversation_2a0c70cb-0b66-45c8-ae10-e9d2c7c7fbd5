{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { useCollectedProps } from '../useCollectedProps.js';\nimport { useOptionalFactory } from '../useOptionalFactory.js';\nimport { useConnectDragPreview, useConnectDragSource } from './connectors.js';\nimport { useDragSourceConnector } from './useDragSourceConnector.js';\nimport { useDragSourceMonitor } from './useDragSourceMonitor.js';\nimport { useRegisteredDragSource } from './useRegisteredDragSource.js';\n/**\n * useDragSource hook\n * @param sourceSpec The drag source specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrag(specArg, deps) {\n  const spec = useOptionalFactory(specArg, deps);\n  invariant(!spec.begin, `useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)`);\n  const monitor = useDragSourceMonitor();\n  const connector = useDragSourceConnector(spec.options, spec.previewOptions);\n  useRegisteredDragSource(spec, monitor, connector);\n  return [useCollectedProps(spec.collect, monitor, connector), useConnectDragSource(connector), useConnectDragPreview(connector)];\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAOhD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,iBAAiB;AAC7E,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtE;;;;;AAKA,OAAO,SAASC,OAAO,CAKtBC,OAEC,EACDC,IAAgB,EAC0C;EAC1D,MAAMC,IAAI,GAAGT,kBAAkB,CAACO,OAAO,EAAEC,IAAI,CAAC;EAC9CV,SAAS,CACR,CAACW,IAAK,CAASC,KAAK,EACnB,6JAA4J,CAC7J;EAED,MAAMC,OAAO,GAAGP,oBAAoB,EAA0B;EAC9D,MAAMQ,SAAS,GAAGT,sBAAsB,CAACM,IAAI,CAACI,OAAO,EAAEJ,IAAI,CAACK,cAAc,CAAC;EAC3ET,uBAAuB,CAACI,IAAI,EAAEE,OAAO,EAAEC,SAAS,CAAC;EAEjD,OAAO,CACNb,iBAAiB,CAACU,IAAI,CAACM,OAAO,EAAEJ,OAAO,EAAEC,SAAS,CAAC,EACnDV,oBAAoB,CAACU,SAAS,CAAC,EAC/BX,qBAAqB,CAACW,SAAS,CAAC,CAChC", "names": ["invariant", "useCollectedProps", "useOptionalFactory", "useConnectDragPreview", "useConnectDragSource", "useDragSourceConnector", "useDragSourceMonitor", "useRegisteredDragSource", "useDrag", "specArg", "deps", "spec", "begin", "monitor", "connector", "options", "previewOptions", "collect"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrag\\useDrag.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tConnectDragPreview,\n\tConnectDragSource,\n} from '../../types/index.js'\nimport type { DragSourceHookSpec, FactoryOrInstance } from '../types.js'\nimport { useCollectedProps } from '../useCollectedProps.js'\nimport { useOptionalFactory } from '../useOptionalFactory.js'\nimport { useConnectDragPreview, useConnectDragSource } from './connectors.js'\nimport { useDragSourceConnector } from './useDragSourceConnector.js'\nimport { useDragSourceMonitor } from './useDragSourceMonitor.js'\nimport { useRegisteredDragSource } from './useRegisteredDragSource.js'\n\n/**\n * useDragSource hook\n * @param sourceSpec The drag source specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrag<\n\tDragObject = unknown,\n\tDropResult = unknown,\n\tCollectedProps = unknown,\n>(\n\tspecArg: FactoryOrInstance<\n\t\tDragSourceHookSpec<DragObject, DropResult, CollectedProps>\n\t>,\n\tdeps?: unknown[],\n): [CollectedProps, ConnectDragSource, ConnectDragPreview] {\n\tconst spec = useOptionalFactory(specArg, deps)\n\tinvariant(\n\t\t!(spec as any).begin,\n\t\t`useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)`,\n\t)\n\n\tconst monitor = useDragSourceMonitor<DragObject, DropResult>()\n\tconst connector = useDragSourceConnector(spec.options, spec.previewOptions)\n\tuseRegisteredDragSource(spec, monitor, connector)\n\n\treturn [\n\t\tuseCollectedProps(spec.collect, monitor, connector),\n\t\tuseConnectDragSource(connector),\n\t\tuseConnectDragPreview(connector),\n\t]\n}\n"]}, "metadata": {}, "sourceType": "module"}