{"ast": null, "code": "import { useMemo } from 'react';\nimport { SourceConnector } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js';\nexport function useDragSourceConnector(dragSourceOptions, dragPreviewOptions) {\n  const manager = useDragDropManager();\n  const connector = useMemo(() => new SourceConnector(manager.getBackend()), [manager]);\n  useIsomorphicLayoutEffect(() => {\n    connector.dragSourceOptions = dragSourceOptions || null;\n    connector.reconnect();\n    return () => connector.disconnectDragSource();\n  }, [connector, dragSourceOptions]);\n  useIsomorphicLayoutEffect(() => {\n    connector.dragPreviewOptions = dragPreviewOptions || null;\n    connector.reconnect();\n    return () => connector.disconnectDragPreview();\n  }, [connector, dragPreviewOptions]);\n  return connector;\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAE/B,SAASC,eAAe,QAAQ,0BAA0B;AAK1D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC;AAE3E,OAAO,SAASC,sBAAsB,CACrCC,iBAAgD,EAChDC,kBAAkD,EAChC;EAClB,MAAMC,OAAO,GAAGL,kBAAkB,EAAE;EACpC,MAAMM,SAAS,GAAGR,OAAO,CACxB,MAAM,IAAIC,eAAe,CAACM,OAAO,CAACE,UAAU,EAAE,CAAC,EAC/C,CAACF,OAAO,CAAC,CACT;EACDJ,yBAAyB,CAAC,MAAM;IAC/BK,SAAS,CAACH,iBAAiB,GAAGA,iBAAiB,IAAI,IAAI;IACvDG,SAAS,CAACE,SAAS,EAAE;IACrB,OAAO,MAAMF,SAAS,CAACG,oBAAoB,EAAE;GAC7C,EAAE,CAACH,SAAS,EAAEH,iBAAiB,CAAC,CAAC;EAClCF,yBAAyB,CAAC,MAAM;IAC/BK,SAAS,CAACF,kBAAkB,GAAGA,kBAAkB,IAAI,IAAI;IACzDE,SAAS,CAACE,SAAS,EAAE;IACrB,OAAO,MAAMF,SAAS,CAACI,qBAAqB,EAAE;GAC9C,EAAE,CAACJ,SAAS,EAAEF,kBAAkB,CAAC,CAAC;EACnC,OAAOE,SAAS", "names": ["useMemo", "SourceConnector", "useDragDropManager", "useIsomorphicLayoutEffect", "useDragSourceConnector", "dragSourceOptions", "dragPreviewOptions", "manager", "connector", "getBackend", "reconnect", "disconnectDragSource", "disconnectDragPreview"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-dnd\\src\\hooks\\useDrag\\useDragSourceConnector.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { SourceConnector } from '../../internals/index.js'\nimport type {\n\tDragPreviewOptions,\n\tDragSourceOptions,\n} from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\n\nexport function useDragSourceConnector(\n\tdragSourceOptions: DragSourceOptions | undefined,\n\tdragPreviewOptions: DragPreviewOptions | undefined,\n): SourceConnector {\n\tconst manager = useDragDropManager()\n\tconst connector = useMemo(\n\t\t() => new SourceConnector(manager.getBackend()),\n\t\t[manager],\n\t)\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dragSourceOptions = dragSourceOptions || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDragSource()\n\t}, [connector, dragSourceOptions])\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dragPreviewOptions = dragPreviewOptions || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDragPreview()\n\t}, [connector, dragPreviewOptions])\n\treturn connector\n}\n"]}, "metadata": {}, "sourceType": "module"}