{"ast": null, "code": "export function reduce() {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  return state + 1;\n}", "map": {"version": 3, "mappings": "AAEA,OAAO,SAASA,MAAM,GAA0B;EAAA,IAAzBC,KAAY,uEAAG,CAAC;EACtC,OAAOA,KAAK,GAAG,CAAC", "names": ["reduce", "state"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\dnd-core\\src\\reducers\\stateId.ts"], "sourcesContent": ["export type State = number\n\nexport function reduce(state: State = 0): State {\n\treturn state + 1\n}\n"]}, "metadata": {}, "sourceType": "module"}